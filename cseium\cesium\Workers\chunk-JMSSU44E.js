/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.127
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as w}from"./chunk-4UYSGV57.js";import{a as O}from"./chunk-VLPNAR64.js";import{a as S,b as c}from"./chunk-GE5NEIZC.js";import{e as u}from"./chunk-35CVRQTC.js";function f(n,e,o){this.x=O(n,0),this.y=O(e,0),this.z=O(o,0)}f.fromSpherical=function(n,e){c.typeOf.object("spherical",n),u(e)||(e=new f);let o=n.clock,t=n.cone,i=O(n.magnitude,1),p=i*Math.sin(t);return e.x=p*Math.cos(o),e.y=p*Math.sin(o),e.z=i*Math.cos(t),e};f.fromElements=function(n,e,o,t){return u(t)?(t.x=n,t.y=e,t.z=o,t):new f(n,e,o)};f.clone=function(n,e){if(u(n))return u(e)?(e.x=n.x,e.y=n.y,e.z=n.z,e):new f(n.x,n.y,n.z)};f.fromCartesian4=f.clone;f.packedLength=3;f.pack=function(n,e,o){return c.typeOf.object("value",n),c.defined("array",e),o=O(o,0),e[o++]=n.x,e[o++]=n.y,e[o]=n.z,e};f.unpack=function(n,e,o){return c.defined("array",n),e=O(e,0),u(o)||(o=new f),o.x=n[e++],o.y=n[e++],o.z=n[e],o};f.packArray=function(n,e){c.defined("array",n);let o=n.length,t=o*3;if(!u(e))e=new Array(t);else{if(!Array.isArray(e)&&e.length!==t)throw new S("If result is a typed array, it must have exactly array.length * 3 elements");e.length!==t&&(e.length=t)}for(let i=0;i<o;++i)f.pack(n[i],e,i*3);return e};f.unpackArray=function(n,e){if(c.defined("array",n),c.typeOf.number.greaterThanOrEquals("array.length",n.length,3),n.length%3!==0)throw new S("array length must be a multiple of 3.");let o=n.length;u(e)?e.length=o/3:e=new Array(o/3);for(let t=0;t<o;t+=3){let i=t/3;e[i]=f.unpack(n,t,e[i])}return e};f.fromArray=f.unpack;f.maximumComponent=function(n){return c.typeOf.object("cartesian",n),Math.max(n.x,n.y,n.z)};f.minimumComponent=function(n){return c.typeOf.object("cartesian",n),Math.min(n.x,n.y,n.z)};f.minimumByComponent=function(n,e,o){return c.typeOf.object("first",n),c.typeOf.object("second",e),c.typeOf.object("result",o),o.x=Math.min(n.x,e.x),o.y=Math.min(n.y,e.y),o.z=Math.min(n.z,e.z),o};f.maximumByComponent=function(n,e,o){return c.typeOf.object("first",n),c.typeOf.object("second",e),c.typeOf.object("result",o),o.x=Math.max(n.x,e.x),o.y=Math.max(n.y,e.y),o.z=Math.max(n.z,e.z),o};f.clamp=function(n,e,o,t){c.typeOf.object("value",n),c.typeOf.object("min",e),c.typeOf.object("max",o),c.typeOf.object("result",t);let i=w.clamp(n.x,e.x,o.x),p=w.clamp(n.y,e.y,o.y),d=w.clamp(n.z,e.z,o.z);return t.x=i,t.y=p,t.z=d,t};f.magnitudeSquared=function(n){return c.typeOf.object("cartesian",n),n.x*n.x+n.y*n.y+n.z*n.z};f.magnitude=function(n){return Math.sqrt(f.magnitudeSquared(n))};var G=new f;f.distance=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),f.subtract(n,e,G),f.magnitude(G)};f.distanceSquared=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),f.subtract(n,e,G),f.magnitudeSquared(G)};f.normalize=function(n,e){c.typeOf.object("cartesian",n),c.typeOf.object("result",e);let o=f.magnitude(n);if(e.x=n.x/o,e.y=n.y/o,e.z=n.z/o,isNaN(e.x)||isNaN(e.y)||isNaN(e.z))throw new S("normalized result is not a number");return e};f.dot=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),n.x*e.x+n.y*e.y+n.z*e.z};f.multiplyComponents=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x*e.x,o.y=n.y*e.y,o.z=n.z*e.z,o};f.divideComponents=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x/e.x,o.y=n.y/e.y,o.z=n.z/e.z,o};f.add=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x+e.x,o.y=n.y+e.y,o.z=n.z+e.z,o};f.subtract=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x-e.x,o.y=n.y-e.y,o.z=n.z-e.z,o};f.multiplyByScalar=function(n,e,o){return c.typeOf.object("cartesian",n),c.typeOf.number("scalar",e),c.typeOf.object("result",o),o.x=n.x*e,o.y=n.y*e,o.z=n.z*e,o};f.divideByScalar=function(n,e,o){return c.typeOf.object("cartesian",n),c.typeOf.number("scalar",e),c.typeOf.object("result",o),o.x=n.x/e,o.y=n.y/e,o.z=n.z/e,o};f.negate=function(n,e){return c.typeOf.object("cartesian",n),c.typeOf.object("result",e),e.x=-n.x,e.y=-n.y,e.z=-n.z,e};f.abs=function(n,e){return c.typeOf.object("cartesian",n),c.typeOf.object("result",e),e.x=Math.abs(n.x),e.y=Math.abs(n.y),e.z=Math.abs(n.z),e};var ln=new f;f.lerp=function(n,e,o,t){return c.typeOf.object("start",n),c.typeOf.object("end",e),c.typeOf.number("t",o),c.typeOf.object("result",t),f.multiplyByScalar(e,o,ln),t=f.multiplyByScalar(n,1-o,t),f.add(ln,t,t)};var Y=new f,yn=new f;f.angleBetween=function(n,e){c.typeOf.object("left",n),c.typeOf.object("right",e),f.normalize(n,Y),f.normalize(e,yn);let o=f.dot(Y,yn),t=f.magnitude(f.cross(Y,yn,Y));return Math.atan2(t,o)};var Xn=new f;f.mostOrthogonalAxis=function(n,e){c.typeOf.object("cartesian",n),c.typeOf.object("result",e);let o=f.normalize(n,Xn);return f.abs(o,o),o.x<=o.y?o.x<=o.z?e=f.clone(f.UNIT_X,e):e=f.clone(f.UNIT_Z,e):o.y<=o.z?e=f.clone(f.UNIT_Y,e):e=f.clone(f.UNIT_Z,e),e};f.projectVector=function(n,e,o){c.defined("a",n),c.defined("b",e),c.defined("result",o);let t=f.dot(n,e)/f.dot(e,e);return f.multiplyByScalar(e,t,o)};f.equals=function(n,e){return n===e||u(n)&&u(e)&&n.x===e.x&&n.y===e.y&&n.z===e.z};f.equalsArray=function(n,e,o){return n.x===e[o]&&n.y===e[o+1]&&n.z===e[o+2]};f.equalsEpsilon=function(n,e,o,t){return n===e||u(n)&&u(e)&&w.equalsEpsilon(n.x,e.x,o,t)&&w.equalsEpsilon(n.y,e.y,o,t)&&w.equalsEpsilon(n.z,e.z,o,t)};f.cross=function(n,e,o){c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o);let t=n.x,i=n.y,p=n.z,d=e.x,m=e.y,s=e.z,h=i*s-p*m,j=p*d-t*s,z=t*m-i*d;return o.x=h,o.y=j,o.z=z,o};f.midpoint=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=(n.x+e.x)*.5,o.y=(n.y+e.y)*.5,o.z=(n.z+e.z)*.5,o};f.fromDegrees=function(n,e,o,t,i){return c.typeOf.number("longitude",n),c.typeOf.number("latitude",e),n=w.toRadians(n),e=w.toRadians(e),f.fromRadians(n,e,o,t,i)};var C=new f,v=new f;f._ellipsoidRadiiSquared=new f(6378137*6378137,6378137*6378137,6356752314245179e-9*6356752314245179e-9);f.fromRadians=function(n,e,o,t,i){c.typeOf.number("longitude",n),c.typeOf.number("latitude",e),o=O(o,0);let p=u(t)?t.radiiSquared:f._ellipsoidRadiiSquared,d=Math.cos(e);C.x=d*Math.cos(n),C.y=d*Math.sin(n),C.z=Math.sin(e),C=f.normalize(C,C),f.multiplyComponents(p,C,v);let m=Math.sqrt(f.dot(C,v));return v=f.divideByScalar(v,m,v),C=f.multiplyByScalar(C,o,C),u(i)||(i=new f),f.add(v,C,i)};f.fromDegreesArray=function(n,e,o){if(c.defined("coordinates",n),n.length<2||n.length%2!==0)throw new S("the number of coordinates must be a multiple of 2 and at least 2");let t=n.length;u(o)?o.length=t/2:o=new Array(t/2);for(let i=0;i<t;i+=2){let p=n[i],d=n[i+1],m=i/2;o[m]=f.fromDegrees(p,d,0,e,o[m])}return o};f.fromRadiansArray=function(n,e,o){if(c.defined("coordinates",n),n.length<2||n.length%2!==0)throw new S("the number of coordinates must be a multiple of 2 and at least 2");let t=n.length;u(o)?o.length=t/2:o=new Array(t/2);for(let i=0;i<t;i+=2){let p=n[i],d=n[i+1],m=i/2;o[m]=f.fromRadians(p,d,0,e,o[m])}return o};f.fromDegreesArrayHeights=function(n,e,o){if(c.defined("coordinates",n),n.length<3||n.length%3!==0)throw new S("the number of coordinates must be a multiple of 3 and at least 3");let t=n.length;u(o)?o.length=t/3:o=new Array(t/3);for(let i=0;i<t;i+=3){let p=n[i],d=n[i+1],m=n[i+2],s=i/3;o[s]=f.fromDegrees(p,d,m,e,o[s])}return o};f.fromRadiansArrayHeights=function(n,e,o){if(c.defined("coordinates",n),n.length<3||n.length%3!==0)throw new S("the number of coordinates must be a multiple of 3 and at least 3");let t=n.length;u(o)?o.length=t/3:o=new Array(t/3);for(let i=0;i<t;i+=3){let p=n[i],d=n[i+1],m=n[i+2],s=i/3;o[s]=f.fromRadians(p,d,m,e,o[s])}return o};f.ZERO=Object.freeze(new f(0,0,0));f.ONE=Object.freeze(new f(1,1,1));f.UNIT_X=Object.freeze(new f(1,0,0));f.UNIT_Y=Object.freeze(new f(0,1,0));f.UNIT_Z=Object.freeze(new f(0,0,1));f.prototype.clone=function(n){return f.clone(this,n)};f.prototype.equals=function(n){return f.equals(this,n)};f.prototype.equalsEpsilon=function(n,e,o){return f.equalsEpsilon(this,n,e,o)};f.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z})`};var y=f;var Yn=new y,Gn=new y;function Hn(n,e,o,t,i){if(!u(n))throw new S("cartesian is required.");if(!u(e))throw new S("oneOverRadii is required.");if(!u(o))throw new S("oneOverRadiiSquared is required.");if(!u(t))throw new S("centerToleranceSquared is required.");let p=n.x,d=n.y,m=n.z,s=e.x,h=e.y,j=e.z,z=p*p*s*s,M=d*d*h*h,q=m*m*j*j,N=z+M+q,l=Math.sqrt(1/N),x=y.multiplyByScalar(n,l,Yn);if(N<t)return isFinite(l)?y.clone(x,i):void 0;let U=o.x,I=o.y,P=o.z,k=Gn;k.x=x.x*U*2,k.y=x.y*I*2,k.z=x.z*P*2;let X=(1-l)*y.magnitude(n)/(.5*y.magnitude(k)),Mn=0,pn,qn,L,V,W,un,an,dn,En,An,_n;do{X-=Mn,L=1/(1+X*U),V=1/(1+X*I),W=1/(1+X*P),un=L*L,an=V*V,dn=W*W,En=un*L,An=an*V,_n=dn*W,pn=z*un+M*an+q*dn-1,qn=z*En*U+M*An*I+q*_n*P;let Zn=-2*qn;Mn=pn/Zn}while(Math.abs(pn)>w.EPSILON12);return u(i)?(i.x=p*L,i.y=d*V,i.z=m*W,i):new y(p*L,d*V,m*W)}var H=Hn;function g(n,e,o){this.longitude=O(n,0),this.latitude=O(e,0),this.height=O(o,0)}g.fromRadians=function(n,e,o,t){return c.typeOf.number("longitude",n),c.typeOf.number("latitude",e),o=O(o,0),u(t)?(t.longitude=n,t.latitude=e,t.height=o,t):new g(n,e,o)};g.fromDegrees=function(n,e,o,t){return c.typeOf.number("longitude",n),c.typeOf.number("latitude",e),n=w.toRadians(n),e=w.toRadians(e),g.fromRadians(n,e,o,t)};var Qn=new y,Kn=new y,Jn=new y;g._ellipsoidOneOverRadii=new y(1/6378137,1/6378137,1/6356752314245179e-9);g._ellipsoidOneOverRadiiSquared=new y(1/(6378137*6378137),1/(6378137*6378137),1/(6356752314245179e-9*6356752314245179e-9));g._ellipsoidCenterToleranceSquared=w.EPSILON1;g.fromCartesian=function(n,e,o){let t=u(e)?e.oneOverRadii:g._ellipsoidOneOverRadii,i=u(e)?e.oneOverRadiiSquared:g._ellipsoidOneOverRadiiSquared,p=u(e)?e._centerToleranceSquared:g._ellipsoidCenterToleranceSquared,d=H(n,t,i,p,Kn);if(!u(d))return;let m=y.multiplyComponents(d,i,Qn);m=y.normalize(m,m);let s=y.subtract(n,d,Jn),h=Math.atan2(m.y,m.x),j=Math.asin(m.z),z=w.sign(y.dot(s,n))*y.magnitude(s);return u(o)?(o.longitude=h,o.latitude=j,o.height=z,o):new g(h,j,z)};g.toCartesian=function(n,e,o){return c.defined("cartographic",n),y.fromRadians(n.longitude,n.latitude,n.height,e,o)};g.clone=function(n,e){if(u(n))return u(e)?(e.longitude=n.longitude,e.latitude=n.latitude,e.height=n.height,e):new g(n.longitude,n.latitude,n.height)};g.equals=function(n,e){return n===e||u(n)&&u(e)&&n.longitude===e.longitude&&n.latitude===e.latitude&&n.height===e.height};g.equalsEpsilon=function(n,e,o){return o=O(o,0),n===e||u(n)&&u(e)&&Math.abs(n.longitude-e.longitude)<=o&&Math.abs(n.latitude-e.latitude)<=o&&Math.abs(n.height-e.height)<=o};g.ZERO=Object.freeze(new g(0,0,0));g.prototype.clone=function(n){return g.clone(this,n)};g.prototype.equals=function(n){return g.equals(this,n)};g.prototype.equalsEpsilon=function(n,e){return g.equalsEpsilon(this,n,e)};g.prototype.toString=function(){return`(${this.longitude}, ${this.latitude}, ${this.height})`};var D=g;function a(n,e){this.x=O(n,0),this.y=O(e,0)}a.fromElements=function(n,e,o){return u(o)?(o.x=n,o.y=e,o):new a(n,e)};a.clone=function(n,e){if(u(n))return u(e)?(e.x=n.x,e.y=n.y,e):new a(n.x,n.y)};a.fromCartesian3=a.clone;a.fromCartesian4=a.clone;a.packedLength=2;a.pack=function(n,e,o){return c.typeOf.object("value",n),c.defined("array",e),o=O(o,0),e[o++]=n.x,e[o]=n.y,e};a.unpack=function(n,e,o){return c.defined("array",n),e=O(e,0),u(o)||(o=new a),o.x=n[e++],o.y=n[e],o};a.packArray=function(n,e){c.defined("array",n);let o=n.length,t=o*2;if(!u(e))e=new Array(t);else{if(!Array.isArray(e)&&e.length!==t)throw new S("If result is a typed array, it must have exactly array.length * 2 elements");e.length!==t&&(e.length=t)}for(let i=0;i<o;++i)a.pack(n[i],e,i*2);return e};a.unpackArray=function(n,e){if(c.defined("array",n),c.typeOf.number.greaterThanOrEquals("array.length",n.length,2),n.length%2!==0)throw new S("array length must be a multiple of 2.");let o=n.length;u(e)?e.length=o/2:e=new Array(o/2);for(let t=0;t<o;t+=2){let i=t/2;e[i]=a.unpack(n,t,e[i])}return e};a.fromArray=a.unpack;a.maximumComponent=function(n){return c.typeOf.object("cartesian",n),Math.max(n.x,n.y)};a.minimumComponent=function(n){return c.typeOf.object("cartesian",n),Math.min(n.x,n.y)};a.minimumByComponent=function(n,e,o){return c.typeOf.object("first",n),c.typeOf.object("second",e),c.typeOf.object("result",o),o.x=Math.min(n.x,e.x),o.y=Math.min(n.y,e.y),o};a.maximumByComponent=function(n,e,o){return c.typeOf.object("first",n),c.typeOf.object("second",e),c.typeOf.object("result",o),o.x=Math.max(n.x,e.x),o.y=Math.max(n.y,e.y),o};a.clamp=function(n,e,o,t){c.typeOf.object("value",n),c.typeOf.object("min",e),c.typeOf.object("max",o),c.typeOf.object("result",t);let i=w.clamp(n.x,e.x,o.x),p=w.clamp(n.y,e.y,o.y);return t.x=i,t.y=p,t};a.magnitudeSquared=function(n){return c.typeOf.object("cartesian",n),n.x*n.x+n.y*n.y};a.magnitude=function(n){return Math.sqrt(a.magnitudeSquared(n))};var Q=new a;a.distance=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),a.subtract(n,e,Q),a.magnitude(Q)};a.distanceSquared=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),a.subtract(n,e,Q),a.magnitudeSquared(Q)};a.normalize=function(n,e){c.typeOf.object("cartesian",n),c.typeOf.object("result",e);let o=a.magnitude(n);if(e.x=n.x/o,e.y=n.y/o,isNaN(e.x)||isNaN(e.y))throw new S("normalized result is not a number");return e};a.dot=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),n.x*e.x+n.y*e.y};a.cross=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),n.x*e.y-n.y*e.x};a.multiplyComponents=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x*e.x,o.y=n.y*e.y,o};a.divideComponents=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x/e.x,o.y=n.y/e.y,o};a.add=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x+e.x,o.y=n.y+e.y,o};a.subtract=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o.x=n.x-e.x,o.y=n.y-e.y,o};a.multiplyByScalar=function(n,e,o){return c.typeOf.object("cartesian",n),c.typeOf.number("scalar",e),c.typeOf.object("result",o),o.x=n.x*e,o.y=n.y*e,o};a.divideByScalar=function(n,e,o){return c.typeOf.object("cartesian",n),c.typeOf.number("scalar",e),c.typeOf.object("result",o),o.x=n.x/e,o.y=n.y/e,o};a.negate=function(n,e){return c.typeOf.object("cartesian",n),c.typeOf.object("result",e),e.x=-n.x,e.y=-n.y,e};a.abs=function(n,e){return c.typeOf.object("cartesian",n),c.typeOf.object("result",e),e.x=Math.abs(n.x),e.y=Math.abs(n.y),e};var Cn=new a;a.lerp=function(n,e,o,t){return c.typeOf.object("start",n),c.typeOf.object("end",e),c.typeOf.number("t",o),c.typeOf.object("result",t),a.multiplyByScalar(e,o,Cn),t=a.multiplyByScalar(n,1-o,t),a.add(Cn,t,t)};var Tn=new a,Nn=new a;a.angleBetween=function(n,e){return c.typeOf.object("left",n),c.typeOf.object("right",e),a.normalize(n,Tn),a.normalize(e,Nn),w.acosClamped(a.dot(Tn,Nn))};var ne=new a;a.mostOrthogonalAxis=function(n,e){c.typeOf.object("cartesian",n),c.typeOf.object("result",e);let o=a.normalize(n,ne);return a.abs(o,o),o.x<=o.y?e=a.clone(a.UNIT_X,e):e=a.clone(a.UNIT_Y,e),e};a.equals=function(n,e){return n===e||u(n)&&u(e)&&n.x===e.x&&n.y===e.y};a.equalsArray=function(n,e,o){return n.x===e[o]&&n.y===e[o+1]};a.equalsEpsilon=function(n,e,o,t){return n===e||u(n)&&u(e)&&w.equalsEpsilon(n.x,e.x,o,t)&&w.equalsEpsilon(n.y,e.y,o,t)};a.ZERO=Object.freeze(new a(0,0));a.ONE=Object.freeze(new a(1,1));a.UNIT_X=Object.freeze(new a(1,0));a.UNIT_Y=Object.freeze(new a(0,1));a.prototype.clone=function(n){return a.clone(this,n)};a.prototype.equals=function(n){return a.equals(this,n)};a.prototype.equalsEpsilon=function(n,e,o){return a.equalsEpsilon(this,n,e,o)};a.prototype.toString=function(){return`(${this.x}, ${this.y})`};var mn=a;function Fn(n,e,o,t){e=O(e,0),o=O(o,0),t=O(t,0),c.typeOf.number.greaterThanOrEquals("x",e,0),c.typeOf.number.greaterThanOrEquals("y",o,0),c.typeOf.number.greaterThanOrEquals("z",t,0),n._radii=new y(e,o,t),n._radiiSquared=new y(e*e,o*o,t*t),n._radiiToTheFourth=new y(e*e*e*e,o*o*o*o,t*t*t*t),n._oneOverRadii=new y(e===0?0:1/e,o===0?0:1/o,t===0?0:1/t),n._oneOverRadiiSquared=new y(e===0?0:1/(e*e),o===0?0:1/(o*o),t===0?0:1/(t*t)),n._minimumRadius=Math.min(e,o,t),n._maximumRadius=Math.max(e,o,t),n._centerToleranceSquared=w.EPSILON1,n._radiiSquared.z!==0&&(n._squaredXOverSquaredZ=n._radiiSquared.x/n._radiiSquared.z)}function b(n,e,o){this._radii=void 0,this._radiiSquared=void 0,this._radiiToTheFourth=void 0,this._oneOverRadii=void 0,this._oneOverRadiiSquared=void 0,this._minimumRadius=void 0,this._maximumRadius=void 0,this._centerToleranceSquared=void 0,this._squaredXOverSquaredZ=void 0,Fn(this,n,e,o)}Object.defineProperties(b.prototype,{radii:{get:function(){return this._radii}},radiiSquared:{get:function(){return this._radiiSquared}},radiiToTheFourth:{get:function(){return this._radiiToTheFourth}},oneOverRadii:{get:function(){return this._oneOverRadii}},oneOverRadiiSquared:{get:function(){return this._oneOverRadiiSquared}},minimumRadius:{get:function(){return this._minimumRadius}},maximumRadius:{get:function(){return this._maximumRadius}}});b.clone=function(n,e){if(!u(n))return;let o=n._radii;return u(e)?(y.clone(o,e._radii),y.clone(n._radiiSquared,e._radiiSquared),y.clone(n._radiiToTheFourth,e._radiiToTheFourth),y.clone(n._oneOverRadii,e._oneOverRadii),y.clone(n._oneOverRadiiSquared,e._oneOverRadiiSquared),e._minimumRadius=n._minimumRadius,e._maximumRadius=n._maximumRadius,e._centerToleranceSquared=n._centerToleranceSquared,e):new b(o.x,o.y,o.z)};b.fromCartesian3=function(n,e){return u(e)||(e=new b),u(n)&&Fn(e,n.x,n.y,n.z),e};b.WGS84=Object.freeze(new b(6378137,6378137,6356752314245179e-9));b.UNIT_SPHERE=Object.freeze(new b(1,1,1));b.MOON=Object.freeze(new b(w.LUNAR_RADIUS,w.LUNAR_RADIUS,w.LUNAR_RADIUS));b._default=b.WGS84;Object.defineProperties(b,{default:{get:function(){return b._default},set:function(n){c.typeOf.object("value",n),b._default=n,y._ellipsoidRadiiSquared=n.radiiSquared,D._ellipsoidOneOverRadii=n.oneOverRadii,D._ellipsoidOneOverRadiiSquared=n.oneOverRadiiSquared,D._ellipsoidCenterToleranceSquared=n._centerToleranceSquared}}});b.prototype.clone=function(n){return b.clone(this,n)};b.packedLength=y.packedLength;b.pack=function(n,e,o){return c.typeOf.object("value",n),c.defined("array",e),o=O(o,0),y.pack(n._radii,e,o),e};b.unpack=function(n,e,o){c.defined("array",n),e=O(e,0);let t=y.unpack(n,e);return b.fromCartesian3(t,o)};b.prototype.geocentricSurfaceNormal=y.normalize;b.prototype.geodeticSurfaceNormalCartographic=function(n,e){c.typeOf.object("cartographic",n);let o=n.longitude,t=n.latitude,i=Math.cos(t),p=i*Math.cos(o),d=i*Math.sin(o),m=Math.sin(t);return u(e)||(e=new y),e.x=p,e.y=d,e.z=m,y.normalize(e,e)};b.prototype.geodeticSurfaceNormal=function(n,e){if(c.typeOf.object("cartesian",n),isNaN(n.x)||isNaN(n.y)||isNaN(n.z))throw new S("cartesian has a NaN component");if(!y.equalsEpsilon(n,y.ZERO,w.EPSILON14))return u(e)||(e=new y),e=y.multiplyComponents(n,this._oneOverRadiiSquared,e),y.normalize(e,e)};var ee=new y,oe=new y;b.prototype.cartographicToCartesian=function(n,e){let o=ee,t=oe;this.geodeticSurfaceNormalCartographic(n,o),y.multiplyComponents(this._radiiSquared,o,t);let i=Math.sqrt(y.dot(o,t));return y.divideByScalar(t,i,t),y.multiplyByScalar(o,n.height,o),u(e)||(e=new y),y.add(t,o,e)};b.prototype.cartographicArrayToCartesianArray=function(n,e){c.defined("cartographics",n);let o=n.length;u(e)?e.length=o:e=new Array(o);for(let t=0;t<o;t++)e[t]=this.cartographicToCartesian(n[t],e[t]);return e};var te=new y,ce=new y,ie=new y;b.prototype.cartesianToCartographic=function(n,e){let o=this.scaleToGeodeticSurface(n,ce);if(!u(o))return;let t=this.geodeticSurfaceNormal(o,te),i=y.subtract(n,o,ie),p=Math.atan2(t.y,t.x),d=Math.asin(t.z),m=w.sign(y.dot(i,n))*y.magnitude(i);return u(e)?(e.longitude=p,e.latitude=d,e.height=m,e):new D(p,d,m)};b.prototype.cartesianArrayToCartographicArray=function(n,e){c.defined("cartesians",n);let o=n.length;u(e)?e.length=o:e=new Array(o);for(let t=0;t<o;++t)e[t]=this.cartesianToCartographic(n[t],e[t]);return e};b.prototype.scaleToGeodeticSurface=function(n,e){return H(n,this._oneOverRadii,this._oneOverRadiiSquared,this._centerToleranceSquared,e)};b.prototype.scaleToGeocentricSurface=function(n,e){c.typeOf.object("cartesian",n),u(e)||(e=new y);let o=n.x,t=n.y,i=n.z,p=this._oneOverRadiiSquared,d=1/Math.sqrt(o*o*p.x+t*t*p.y+i*i*p.z);return y.multiplyByScalar(n,d,e)};b.prototype.transformPositionToScaledSpace=function(n,e){return u(e)||(e=new y),y.multiplyComponents(n,this._oneOverRadii,e)};b.prototype.transformPositionFromScaledSpace=function(n,e){return u(e)||(e=new y),y.multiplyComponents(n,this._radii,e)};b.prototype.equals=function(n){return this===n||u(n)&&y.equals(this._radii,n._radii)};b.prototype.toString=function(){return this._radii.toString()};b.prototype.getSurfaceNormalIntersectionWithZAxis=function(n,e,o){if(c.typeOf.object("position",n),!w.equalsEpsilon(this._radii.x,this._radii.y,w.EPSILON15))throw new S("Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)");c.typeOf.number.greaterThan("Ellipsoid.radii.z",this._radii.z,0),e=O(e,0);let t=this._squaredXOverSquaredZ;if(u(o)||(o=new y),o.x=0,o.y=0,o.z=n.z*(1-t),!(Math.abs(o.z)>=this._radii.z-e))return o};var fe=new y;b.prototype.getLocalCurvature=function(n,e){c.typeOf.object("surfacePosition",n),u(e)||(e=new mn);let o=this.getSurfaceNormalIntersectionWithZAxis(n,0,fe),t=y.distance(n,o),i=this.minimumRadius*t/this.maximumRadius**2,p=t*i**2;return mn.fromElements(1/t,1/p,e)};var re=[.14887433898163,.43339539412925,.67940956829902,.86506336668898,.97390652851717,0],pe=[.29552422471475,.26926671930999,.21908636251598,.14945134915058,.066671344308684,0];function xn(n,e,o){c.typeOf.number("a",n),c.typeOf.number("b",e),c.typeOf.func("func",o);let t=.5*(e+n),i=.5*(e-n),p=0;for(let d=0;d<5;d++){let m=i*re[d];p+=pe[d]*(o(t+m)+o(t-m))}return p*=i,p}b.prototype.surfaceArea=function(n){c.typeOf.object("rectangle",n);let e=n.west,o=n.east,t=n.south,i=n.north;for(;o<e;)o+=w.TWO_PI;let p=this._radiiSquared,d=p.x,m=p.y,s=p.z,h=d*m;return xn(t,i,function(j){let z=Math.cos(j),M=Math.sin(j);return Math.cos(j)*xn(e,o,function(q){let N=Math.cos(q),l=Math.sin(q);return Math.sqrt(h*M*M+s*(m*N*N+d*l*l)*z*z)})})};var fo=b;function r(n,e,o,t,i,p,d,m,s){this[0]=O(n,0),this[1]=O(t,0),this[2]=O(d,0),this[3]=O(e,0),this[4]=O(i,0),this[5]=O(m,0),this[6]=O(o,0),this[7]=O(p,0),this[8]=O(s,0)}r.packedLength=9;r.pack=function(n,e,o){return c.typeOf.object("value",n),c.defined("array",e),o=O(o,0),e[o++]=n[0],e[o++]=n[1],e[o++]=n[2],e[o++]=n[3],e[o++]=n[4],e[o++]=n[5],e[o++]=n[6],e[o++]=n[7],e[o++]=n[8],e};r.unpack=function(n,e,o){return c.defined("array",n),e=O(e,0),u(o)||(o=new r),o[0]=n[e++],o[1]=n[e++],o[2]=n[e++],o[3]=n[e++],o[4]=n[e++],o[5]=n[e++],o[6]=n[e++],o[7]=n[e++],o[8]=n[e++],o};r.packArray=function(n,e){c.defined("array",n);let o=n.length,t=o*9;if(!u(e))e=new Array(t);else{if(!Array.isArray(e)&&e.length!==t)throw new S("If result is a typed array, it must have exactly array.length * 9 elements");e.length!==t&&(e.length=t)}for(let i=0;i<o;++i)r.pack(n[i],e,i*9);return e};r.unpackArray=function(n,e){if(c.defined("array",n),c.typeOf.number.greaterThanOrEquals("array.length",n.length,9),n.length%9!==0)throw new S("array length must be a multiple of 9.");let o=n.length;u(e)?e.length=o/9:e=new Array(o/9);for(let t=0;t<o;t+=9){let i=t/9;e[i]=r.unpack(n,t,e[i])}return e};r.clone=function(n,e){if(u(n))return u(e)?(e[0]=n[0],e[1]=n[1],e[2]=n[2],e[3]=n[3],e[4]=n[4],e[5]=n[5],e[6]=n[6],e[7]=n[7],e[8]=n[8],e):new r(n[0],n[3],n[6],n[1],n[4],n[7],n[2],n[5],n[8])};r.fromArray=r.unpack;r.fromColumnMajorArray=function(n,e){return c.defined("values",n),r.clone(n,e)};r.fromRowMajorArray=function(n,e){return c.defined("values",n),u(e)?(e[0]=n[0],e[1]=n[3],e[2]=n[6],e[3]=n[1],e[4]=n[4],e[5]=n[7],e[6]=n[2],e[7]=n[5],e[8]=n[8],e):new r(n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7],n[8])};r.fromQuaternion=function(n,e){c.typeOf.object("quaternion",n);let o=n.x*n.x,t=n.x*n.y,i=n.x*n.z,p=n.x*n.w,d=n.y*n.y,m=n.y*n.z,s=n.y*n.w,h=n.z*n.z,j=n.z*n.w,z=n.w*n.w,M=o-d-h+z,q=2*(t-j),N=2*(i+s),l=2*(t+j),x=-o+d-h+z,U=2*(m-p),I=2*(i-s),P=2*(m+p),k=-o-d+h+z;return u(e)?(e[0]=M,e[1]=l,e[2]=I,e[3]=q,e[4]=x,e[5]=P,e[6]=N,e[7]=U,e[8]=k,e):new r(M,q,N,l,x,U,I,P,k)};r.fromHeadingPitchRoll=function(n,e){c.typeOf.object("headingPitchRoll",n);let o=Math.cos(-n.pitch),t=Math.cos(-n.heading),i=Math.cos(n.roll),p=Math.sin(-n.pitch),d=Math.sin(-n.heading),m=Math.sin(n.roll),s=o*t,h=-i*d+m*p*t,j=m*d+i*p*t,z=o*d,M=i*t+m*p*d,q=-m*t+i*p*d,N=-p,l=m*o,x=i*o;return u(e)?(e[0]=s,e[1]=z,e[2]=N,e[3]=h,e[4]=M,e[5]=l,e[6]=j,e[7]=q,e[8]=x,e):new r(s,h,j,z,M,q,N,l,x)};r.fromScale=function(n,e){return c.typeOf.object("scale",n),u(e)?(e[0]=n.x,e[1]=0,e[2]=0,e[3]=0,e[4]=n.y,e[5]=0,e[6]=0,e[7]=0,e[8]=n.z,e):new r(n.x,0,0,0,n.y,0,0,0,n.z)};r.fromUniformScale=function(n,e){return c.typeOf.number("scale",n),u(e)?(e[0]=n,e[1]=0,e[2]=0,e[3]=0,e[4]=n,e[5]=0,e[6]=0,e[7]=0,e[8]=n,e):new r(n,0,0,0,n,0,0,0,n)};r.fromCrossProduct=function(n,e){return c.typeOf.object("vector",n),u(e)?(e[0]=0,e[1]=n.z,e[2]=-n.y,e[3]=-n.z,e[4]=0,e[5]=n.x,e[6]=n.y,e[7]=-n.x,e[8]=0,e):new r(0,-n.z,n.y,n.z,0,-n.x,-n.y,n.x,0)};r.fromRotationX=function(n,e){c.typeOf.number("angle",n);let o=Math.cos(n),t=Math.sin(n);return u(e)?(e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=o,e[5]=t,e[6]=0,e[7]=-t,e[8]=o,e):new r(1,0,0,0,o,-t,0,t,o)};r.fromRotationY=function(n,e){c.typeOf.number("angle",n);let o=Math.cos(n),t=Math.sin(n);return u(e)?(e[0]=o,e[1]=0,e[2]=-t,e[3]=0,e[4]=1,e[5]=0,e[6]=t,e[7]=0,e[8]=o,e):new r(o,0,t,0,1,0,-t,0,o)};r.fromRotationZ=function(n,e){c.typeOf.number("angle",n);let o=Math.cos(n),t=Math.sin(n);return u(e)?(e[0]=o,e[1]=t,e[2]=0,e[3]=-t,e[4]=o,e[5]=0,e[6]=0,e[7]=0,e[8]=1,e):new r(o,-t,0,t,o,0,0,0,1)};r.toArray=function(n,e){return c.typeOf.object("matrix",n),u(e)?(e[0]=n[0],e[1]=n[1],e[2]=n[2],e[3]=n[3],e[4]=n[4],e[5]=n[5],e[6]=n[6],e[7]=n[7],e[8]=n[8],e):[n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7],n[8]]};r.getElementIndex=function(n,e){return c.typeOf.number.greaterThanOrEquals("row",e,0),c.typeOf.number.lessThanOrEquals("row",e,2),c.typeOf.number.greaterThanOrEquals("column",n,0),c.typeOf.number.lessThanOrEquals("column",n,2),n*3+e};r.getColumn=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.number.greaterThanOrEquals("index",e,0),c.typeOf.number.lessThanOrEquals("index",e,2),c.typeOf.object("result",o);let t=e*3,i=n[t],p=n[t+1],d=n[t+2];return o.x=i,o.y=p,o.z=d,o};r.setColumn=function(n,e,o,t){c.typeOf.object("matrix",n),c.typeOf.number.greaterThanOrEquals("index",e,0),c.typeOf.number.lessThanOrEquals("index",e,2),c.typeOf.object("cartesian",o),c.typeOf.object("result",t),t=r.clone(n,t);let i=e*3;return t[i]=o.x,t[i+1]=o.y,t[i+2]=o.z,t};r.getRow=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.number.greaterThanOrEquals("index",e,0),c.typeOf.number.lessThanOrEquals("index",e,2),c.typeOf.object("result",o);let t=n[e],i=n[e+3],p=n[e+6];return o.x=t,o.y=i,o.z=p,o};r.setRow=function(n,e,o,t){return c.typeOf.object("matrix",n),c.typeOf.number.greaterThanOrEquals("index",e,0),c.typeOf.number.lessThanOrEquals("index",e,2),c.typeOf.object("cartesian",o),c.typeOf.object("result",t),t=r.clone(n,t),t[e]=o.x,t[e+3]=o.y,t[e+6]=o.z,t};var ue=new y;r.setScale=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.object("scale",e),c.typeOf.object("result",o);let t=r.getScale(n,ue),i=e.x/t.x,p=e.y/t.y,d=e.z/t.z;return o[0]=n[0]*i,o[1]=n[1]*i,o[2]=n[2]*i,o[3]=n[3]*p,o[4]=n[4]*p,o[5]=n[5]*p,o[6]=n[6]*d,o[7]=n[7]*d,o[8]=n[8]*d,o};var ae=new y;r.setUniformScale=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.number("scale",e),c.typeOf.object("result",o);let t=r.getScale(n,ae),i=e/t.x,p=e/t.y,d=e/t.z;return o[0]=n[0]*i,o[1]=n[1]*i,o[2]=n[2]*i,o[3]=n[3]*p,o[4]=n[4]*p,o[5]=n[5]*p,o[6]=n[6]*d,o[7]=n[7]*d,o[8]=n[8]*d,o};var sn=new y;r.getScale=function(n,e){return c.typeOf.object("matrix",n),c.typeOf.object("result",e),e.x=y.magnitude(y.fromElements(n[0],n[1],n[2],sn)),e.y=y.magnitude(y.fromElements(n[3],n[4],n[5],sn)),e.z=y.magnitude(y.fromElements(n[6],n[7],n[8],sn)),e};var kn=new y;r.getMaximumScale=function(n){return r.getScale(n,kn),y.maximumComponent(kn)};var de=new y;r.setRotation=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.object("result",o);let t=r.getScale(n,de);return o[0]=e[0]*t.x,o[1]=e[1]*t.x,o[2]=e[2]*t.x,o[3]=e[3]*t.y,o[4]=e[4]*t.y,o[5]=e[5]*t.y,o[6]=e[6]*t.z,o[7]=e[7]*t.z,o[8]=e[8]*t.z,o};var ye=new y;r.getRotation=function(n,e){c.typeOf.object("matrix",n),c.typeOf.object("result",e);let o=r.getScale(n,ye);return e[0]=n[0]/o.x,e[1]=n[1]/o.x,e[2]=n[2]/o.x,e[3]=n[3]/o.y,e[4]=n[4]/o.y,e[5]=n[5]/o.y,e[6]=n[6]/o.z,e[7]=n[7]/o.z,e[8]=n[8]/o.z,e};r.multiply=function(n,e,o){c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o);let t=n[0]*e[0]+n[3]*e[1]+n[6]*e[2],i=n[1]*e[0]+n[4]*e[1]+n[7]*e[2],p=n[2]*e[0]+n[5]*e[1]+n[8]*e[2],d=n[0]*e[3]+n[3]*e[4]+n[6]*e[5],m=n[1]*e[3]+n[4]*e[4]+n[7]*e[5],s=n[2]*e[3]+n[5]*e[4]+n[8]*e[5],h=n[0]*e[6]+n[3]*e[7]+n[6]*e[8],j=n[1]*e[6]+n[4]*e[7]+n[7]*e[8],z=n[2]*e[6]+n[5]*e[7]+n[8]*e[8];return o[0]=t,o[1]=i,o[2]=p,o[3]=d,o[4]=m,o[5]=s,o[6]=h,o[7]=j,o[8]=z,o};r.add=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o[0]=n[0]+e[0],o[1]=n[1]+e[1],o[2]=n[2]+e[2],o[3]=n[3]+e[3],o[4]=n[4]+e[4],o[5]=n[5]+e[5],o[6]=n[6]+e[6],o[7]=n[7]+e[7],o[8]=n[8]+e[8],o};r.subtract=function(n,e,o){return c.typeOf.object("left",n),c.typeOf.object("right",e),c.typeOf.object("result",o),o[0]=n[0]-e[0],o[1]=n[1]-e[1],o[2]=n[2]-e[2],o[3]=n[3]-e[3],o[4]=n[4]-e[4],o[5]=n[5]-e[5],o[6]=n[6]-e[6],o[7]=n[7]-e[7],o[8]=n[8]-e[8],o};r.multiplyByVector=function(n,e,o){c.typeOf.object("matrix",n),c.typeOf.object("cartesian",e),c.typeOf.object("result",o);let t=e.x,i=e.y,p=e.z,d=n[0]*t+n[3]*i+n[6]*p,m=n[1]*t+n[4]*i+n[7]*p,s=n[2]*t+n[5]*i+n[8]*p;return o.x=d,o.y=m,o.z=s,o};r.multiplyByScalar=function(n,e,o){return c.typeOf.object("matrix",n),c.typeOf.number("scalar",e),c.typeOf.object("result",o),o[0]=n[0]*e,o[1]=n[1]*e,o[2]=n[2]*e,o[3]=n[3]*e,o[4]=n[4]*e,o[5]=n[5]*e,o[6]=n[6]*e,o[7]=n[7]*e,o[8]=n[8]*e,o};r.multiplyByScale=function(n,e,o){return c.typeOf.object("matrix",n),c.typeOf.object("scale",e),c.typeOf.object("result",o),o[0]=n[0]*e.x,o[1]=n[1]*e.x,o[2]=n[2]*e.x,o[3]=n[3]*e.y,o[4]=n[4]*e.y,o[5]=n[5]*e.y,o[6]=n[6]*e.z,o[7]=n[7]*e.z,o[8]=n[8]*e.z,o};r.multiplyByUniformScale=function(n,e,o){return c.typeOf.object("matrix",n),c.typeOf.number("scale",e),c.typeOf.object("result",o),o[0]=n[0]*e,o[1]=n[1]*e,o[2]=n[2]*e,o[3]=n[3]*e,o[4]=n[4]*e,o[5]=n[5]*e,o[6]=n[6]*e,o[7]=n[7]*e,o[8]=n[8]*e,o};r.negate=function(n,e){return c.typeOf.object("matrix",n),c.typeOf.object("result",e),e[0]=-n[0],e[1]=-n[1],e[2]=-n[2],e[3]=-n[3],e[4]=-n[4],e[5]=-n[5],e[6]=-n[6],e[7]=-n[7],e[8]=-n[8],e};r.transpose=function(n,e){c.typeOf.object("matrix",n),c.typeOf.object("result",e);let o=n[0],t=n[3],i=n[6],p=n[1],d=n[4],m=n[7],s=n[2],h=n[5],j=n[8];return e[0]=o,e[1]=t,e[2]=i,e[3]=p,e[4]=d,e[5]=m,e[6]=s,e[7]=h,e[8]=j,e};function me(n){let e=0;for(let o=0;o<9;++o){let t=n[o];e+=t*t}return Math.sqrt(e)}var On=[1,0,0],bn=[2,2,1];function se(n){let e=0;for(let o=0;o<3;++o){let t=n[r.getElementIndex(bn[o],On[o])];e+=2*t*t}return Math.sqrt(e)}function Oe(n,e){let o=w.EPSILON15,t=0,i=1;for(let h=0;h<3;++h){let j=Math.abs(n[r.getElementIndex(bn[h],On[h])]);j>t&&(i=h,t=j)}let p=1,d=0,m=On[i],s=bn[i];if(Math.abs(n[r.getElementIndex(s,m)])>o){let h=n[r.getElementIndex(s,s)],j=n[r.getElementIndex(m,m)],z=n[r.getElementIndex(s,m)],M=(h-j)/2/z,q;M<0?q=-1/(-M+Math.sqrt(1+M*M)):q=1/(M+Math.sqrt(1+M*M)),p=1/Math.sqrt(1+q*q),d=q*p}return e=r.clone(r.IDENTITY,e),e[r.getElementIndex(m,m)]=e[r.getElementIndex(s,s)]=p,e[r.getElementIndex(s,m)]=d,e[r.getElementIndex(m,s)]=-d,e}var K=new r,Bn=new r;r.computeEigenDecomposition=function(n,e){c.typeOf.object("matrix",n);let o=w.EPSILON20,t=10,i=0,p=0;u(e)||(e={});let d=e.unitary=r.clone(r.IDENTITY,e.unitary),m=e.diagonal=r.clone(n,e.diagonal),s=o*me(m);for(;p<t&&se(m)>s;)Oe(m,K),r.transpose(K,Bn),r.multiply(m,K,m),r.multiply(Bn,m,m),r.multiply(d,K,d),++i>2&&(++p,i=0);return e};r.abs=function(n,e){return c.typeOf.object("matrix",n),c.typeOf.object("result",e),e[0]=Math.abs(n[0]),e[1]=Math.abs(n[1]),e[2]=Math.abs(n[2]),e[3]=Math.abs(n[3]),e[4]=Math.abs(n[4]),e[5]=Math.abs(n[5]),e[6]=Math.abs(n[6]),e[7]=Math.abs(n[7]),e[8]=Math.abs(n[8]),e};r.determinant=function(n){c.typeOf.object("matrix",n);let e=n[0],o=n[3],t=n[6],i=n[1],p=n[4],d=n[7],m=n[2],s=n[5],h=n[8];return e*(p*h-s*d)+i*(s*t-o*h)+m*(o*d-p*t)};r.inverse=function(n,e){c.typeOf.object("matrix",n),c.typeOf.object("result",e);let o=n[0],t=n[1],i=n[2],p=n[3],d=n[4],m=n[5],s=n[6],h=n[7],j=n[8],z=r.determinant(n);if(Math.abs(z)<=w.EPSILON15)throw new S("matrix is not invertible");e[0]=d*j-h*m,e[1]=h*i-t*j,e[2]=t*m-d*i,e[3]=s*m-p*j,e[4]=o*j-s*i,e[5]=p*i-o*m,e[6]=p*h-s*d,e[7]=s*t-o*h,e[8]=o*d-p*t;let M=1/z;return r.multiplyByScalar(e,M,e)};var be=new r;r.inverseTranspose=function(n,e){return c.typeOf.object("matrix",n),c.typeOf.object("result",e),r.inverse(r.transpose(n,be),e)};r.equals=function(n,e){return n===e||u(n)&&u(e)&&n[0]===e[0]&&n[1]===e[1]&&n[2]===e[2]&&n[3]===e[3]&&n[4]===e[4]&&n[5]===e[5]&&n[6]===e[6]&&n[7]===e[7]&&n[8]===e[8]};r.equalsEpsilon=function(n,e,o){return o=O(o,0),n===e||u(n)&&u(e)&&Math.abs(n[0]-e[0])<=o&&Math.abs(n[1]-e[1])<=o&&Math.abs(n[2]-e[2])<=o&&Math.abs(n[3]-e[3])<=o&&Math.abs(n[4]-e[4])<=o&&Math.abs(n[5]-e[5])<=o&&Math.abs(n[6]-e[6])<=o&&Math.abs(n[7]-e[7])<=o&&Math.abs(n[8]-e[8])<=o};r.IDENTITY=Object.freeze(new r(1,0,0,0,1,0,0,0,1));r.ZERO=Object.freeze(new r(0,0,0,0,0,0,0,0,0));r.COLUMN0ROW0=0;r.COLUMN0ROW1=1;r.COLUMN0ROW2=2;r.COLUMN1ROW0=3;r.COLUMN1ROW1=4;r.COLUMN1ROW2=5;r.COLUMN2ROW0=6;r.COLUMN2ROW1=7;r.COLUMN2ROW2=8;Object.defineProperties(r.prototype,{length:{get:function(){return r.packedLength}}});r.prototype.clone=function(n){return r.clone(this,n)};r.prototype.equals=function(n){return r.equals(this,n)};r.equalsArray=function(n,e,o){return n[0]===e[o]&&n[1]===e[o+1]&&n[2]===e[o+2]&&n[3]===e[o+3]&&n[4]===e[o+4]&&n[5]===e[o+5]&&n[6]===e[o+6]&&n[7]===e[o+7]&&n[8]===e[o+8]};r.prototype.equalsEpsilon=function(n,e){return r.equalsEpsilon(this,n,e)};r.prototype.toString=function(){return`(${this[0]}, ${this[3]}, ${this[6]})
(${this[1]}, ${this[4]}, ${this[7]})
(${this[2]}, ${this[5]}, ${this[8]})`};var Oo=r;var F,R={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},_={};Object.defineProperties(_,{element:{get:function(){if(_.supportsFullscreen())return document[R.fullscreenElement]}},changeEventName:{get:function(){if(_.supportsFullscreen())return R.fullscreenchange}},errorEventName:{get:function(){if(_.supportsFullscreen())return R.fullscreenerror}},enabled:{get:function(){if(_.supportsFullscreen())return document[R.fullscreenEnabled]}},fullscreen:{get:function(){if(_.supportsFullscreen())return _.element!==null}}});_.supportsFullscreen=function(){if(u(F))return F;F=!1;let n=document.body;if(typeof n.requestFullscreen=="function")return R.requestFullscreen="requestFullscreen",R.exitFullscreen="exitFullscreen",R.fullscreenEnabled="fullscreenEnabled",R.fullscreenElement="fullscreenElement",R.fullscreenchange="fullscreenchange",R.fullscreenerror="fullscreenerror",F=!0,F;let e=["webkit","moz","o","ms","khtml"],o;for(let t=0,i=e.length;t<i;++t){let p=e[t];o=`${p}RequestFullscreen`,typeof n[o]=="function"?(R.requestFullscreen=o,F=!0):(o=`${p}RequestFullScreen`,typeof n[o]=="function"&&(R.requestFullscreen=o,F=!0)),o=`${p}ExitFullscreen`,typeof document[o]=="function"?R.exitFullscreen=o:(o=`${p}CancelFullScreen`,typeof document[o]=="function"&&(R.exitFullscreen=o)),o=`${p}FullscreenEnabled`,document[o]!==void 0?R.fullscreenEnabled=o:(o=`${p}FullScreenEnabled`,document[o]!==void 0&&(R.fullscreenEnabled=o)),o=`${p}FullscreenElement`,document[o]!==void 0?R.fullscreenElement=o:(o=`${p}FullScreenElement`,document[o]!==void 0&&(R.fullscreenElement=o)),o=`${p}fullscreenchange`,document[`on${o}`]!==void 0&&(p==="ms"&&(o="MSFullscreenChange"),R.fullscreenchange=o),o=`${p}fullscreenerror`,document[`on${o}`]!==void 0&&(p==="ms"&&(o="MSFullscreenError"),R.fullscreenerror=o)}return F};_.requestFullscreen=function(n,e){_.supportsFullscreen()&&n[R.requestFullscreen]({vrDisplay:e})};_.exitFullscreen=function(){_.supportsFullscreen()&&document[R.exitFullscreen]()};_._names=R;var Un=_;var A;typeof navigator<"u"?A=navigator:A={};function B(n){let e=n.split(".");for(let o=0,t=e.length;o<t;++o)e[o]=parseInt(e[o],10);return e}var J,In;function zn(){if(!u(J)&&(J=!1,!fn())){let n=/ Chrome\/([\.0-9]+)/.exec(A.userAgent);n!==null&&(J=!0,In=B(n[1]))}return J}function he(){return zn()&&In}var nn,Pn;function Ln(){if(!u(nn)&&(nn=!1,!zn()&&!fn()&&/ Safari\/[\.0-9]+/.test(A.userAgent))){let n=/ Version\/([\.0-9]+)/.exec(A.userAgent);n!==null&&(nn=!0,Pn=B(n[1]))}return nn}function we(){return Ln()&&Pn}var en,gn;function Vn(){if(!u(en)){en=!1;let n=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(A.userAgent);n!==null&&(en=!0,gn=B(n[1]),gn.isNightly=!!n[2])}return en}function je(){return Vn()&&gn}var Z,Sn;function Wn(){if(!u(Z)){Z=!1;let n;A.appName==="Microsoft Internet Explorer"?(n=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(A.userAgent),n!==null&&(Z=!0,Sn=B(n[1]))):A.appName==="Netscape"&&(n=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(A.userAgent),n!==null&&(Z=!0,Sn=B(n[1])))}return Z}function ge(){return Wn()&&Sn}var on,vn;function fn(){if(!u(on)){on=!1;let n=/ Edg\/([\.0-9]+)/.exec(A.userAgent);n!==null&&(on=!0,vn=B(n[1]))}return on}function Se(){return fn()&&vn}var tn,Rn;function rn(){if(!u(tn)){tn=!1;let n=/Firefox\/([\.0-9]+)/.exec(A.userAgent);n!==null&&(tn=!0,Rn=B(n[1]))}return tn}var hn;function ze(){return u(hn)||(hn=/Windows/i.test(A.appVersion)),hn}var wn;function Re(){return u(wn)||(wn=navigator.platform==="iPhone"||navigator.platform==="iPod"||navigator.platform==="iPad"),wn}function Me(){return rn()&&Rn}var jn;function qe(){return u(jn)||(jn=!rn()&&typeof PointerEvent<"u"&&(!u(A.pointerEnabled)||A.pointerEnabled)),jn}var $n,cn;function Dn(){if(!u(cn)){let n=document.createElement("canvas");n.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");let e=n.style.imageRendering;cn=u(e)&&e!=="",cn&&($n=e)}return cn}function Ee(){return Dn()?$n:void 0}function E(){if(!E.initialized)throw new S("You must call FeatureDetection.supportsWebP.initialize and wait for the promise to resolve before calling FeatureDetection.supportsWebP");return E._result}E._promise=void 0;E._result=void 0;E.initialize=function(){return u(E._promise)||(E._promise=new Promise(n=>{let e=new Image;e.onload=function(){E._result=e.width>0&&e.height>0,n(E._result)},e.onerror=function(){E._result=!1,n(E._result)},e.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA"})),E._promise};Object.defineProperties(E,{initialized:{get:function(){return u(E._result)}}});var $=[];typeof ArrayBuffer<"u"&&($.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),typeof Uint8ClampedArray<"u"&&$.push(Uint8ClampedArray),typeof Uint8ClampedArray<"u"&&$.push(Uint8ClampedArray),typeof BigInt64Array<"u"&&$.push(BigInt64Array),typeof BigUint64Array<"u"&&$.push(BigUint64Array));var T={isChrome:zn,chromeVersion:he,isSafari:Ln,safariVersion:we,isWebkit:Vn,webkitVersion:je,isInternetExplorer:Wn,internetExplorerVersion:ge,isEdge:fn,edgeVersion:Se,isFirefox:rn,firefoxVersion:Me,isWindows:ze,isIPadOrIOS:Re,hardwareConcurrency:O(A.hardwareConcurrency,3),supportsPointerEvents:qe,supportsImageRenderingPixelated:Dn,supportsWebP:E,imageRenderingValue:Ee,typedArrayTypes:$};T.supportsBasis=function(n){return T.supportsWebAssembly()&&n.context.supportsBasis};T.supportsFullscreen=function(){return Un.supportsFullscreen()};T.supportsTypedArrays=function(){return typeof ArrayBuffer<"u"};T.supportsBigInt64Array=function(){return typeof BigInt64Array<"u"};T.supportsBigUint64Array=function(){return typeof BigUint64Array<"u"};T.supportsBigInt=function(){return typeof BigInt<"u"};T.supportsWebWorkers=function(){return typeof Worker<"u"};T.supportsWebAssembly=function(){return typeof WebAssembly<"u"};T.supportsWebgl2=function(n){return c.defined("scene",n),n.context.webgl2};T.supportsEsmWebWorkers=function(){return!rn()||parseInt(Rn)>=114};var Mo=T;export{y as a,D as b,mn as c,fo as d,Oo as e,Mo as f};
