[{"name": "@tweenjs/tween.js", "license": ["MIT"], "version": "25.0.0", "url": "https://www.npmjs.com/package/@tweenjs/tween.js"}, {"name": "@zip.js/zip.js", "license": ["BSD-3-<PERSON><PERSON>"], "version": "2.7.57", "url": "https://www.npmjs.com/package/@zip.js/zip.js"}, {"name": "autolinker", "license": ["MIT"], "version": "4.1.0", "url": "https://www.npmjs.com/package/autolinker"}, {"name": "basis_universal", "license": ["Apache-2.0"], "version": "1.15", "url": "https://github.com/BinomialLLC/basis_universal"}, {"name": "bitmap-sdf", "license": ["MIT"], "version": "1.0.4", "url": "https://www.npmjs.com/package/bitmap-sdf"}, {"name": "dompurify", "license": ["Apache-2.0"], "version": "3.2.4", "url": "https://www.npmjs.com/package/dompurify", "notes": "dompurify is available as both MPL-2.0 OR Apache-2.0"}, {"name": "draco3d", "license": ["Apache-2.0"], "version": "1.5.7", "url": "https://www.npmjs.com/package/draco3d"}, {"name": "earcut", "license": ["ISC"], "version": "3.0.1", "url": "https://www.npmjs.com/package/earcut"}, {"name": "grapheme-splitter", "license": ["MIT"], "version": "1.0.4", "url": "https://www.npmjs.com/package/grapheme-splitter"}, {"name": "jsep", "license": ["MIT"], "version": "1.4.0", "url": "https://www.npmjs.com/package/jsep"}, {"name": "kdbush", "license": ["ISC"], "version": "4.0.2", "url": "https://www.npmjs.com/package/kdbush"}, {"name": "Knockout", "license": ["MIT"], "version": "3.5.1", "url": "https://knockoutjs.com/"}, {"name": "Knockout ES5 plugin", "license": ["MIT"], "version": "0.4.6", "url": "https://github.com/Steve<PERSON>son/knockout-es5"}, {"name": "ktx-parse", "license": ["MIT"], "version": "1.0.0", "url": "https://www.npmjs.com/package/ktx-parse"}, {"name": "lerc", "license": ["Apache-2.0"], "version": "2.0.0", "url": "https://www.npmjs.com/package/lerc"}, {"name": "mersenne-twister", "license": ["MIT"], "version": "1.1.0", "url": "https://www.npmjs.com/package/mersenne-twister"}, {"name": "meshoptimizer", "license": ["MIT"], "version": "0.22.0", "url": "https://www.npmjs.com/package/meshoptimizer"}, {"name": "nosleep.js", "license": ["MIT"], "version": "0.12.0", "url": "https://www.npmjs.com/package/nosleep.js"}, {"name": "pako", "license": ["MIT"], "version": "2.1.0", "url": "https://www.npmjs.com/package/pako", "notes": "pako is MIT, and its dependency zlib is attributed separately"}, {"name": "protobufjs", "license": ["BSD-3-<PERSON><PERSON>"], "version": "7.4.0", "url": "https://www.npmjs.com/package/protobufjs"}, {"name": "rbush", "license": ["MIT"], "version": "3.0.1", "url": "https://www.npmjs.com/package/rbush"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-client", "license": ["ISC"], "version": "3.1.0", "url": "https://www.npmjs.com/package/topojson-client"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "license": ["MIT"], "version": "1.19.11", "url": "https://www.npmjs.com/package/urijs"}]