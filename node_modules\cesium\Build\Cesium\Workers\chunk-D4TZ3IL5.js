/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.127
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as R}from"./chunk-GHQSI7F2.js";import{a as rt}from"./chunk-AOALMXNW.js";import{a as Z,b as H}from"./chunk-CKMJZ5B3.js";import{a as V}from"./chunk-WNTCOVG2.js";import{a as U}from"./chunk-EEKB62LQ.js";import{b as it}from"./chunk-V3DSE3OK.js";import{a as ct}from"./chunk-FMRIJYNG.js";import{a as ot}from"./chunk-NNHN6WUY.js";import{a as nt}from"./chunk-T7ERZ2CJ.js";import{b as J,c as X,d as z}from"./chunk-GSNDLQ4C.js";import{f as K}from"./chunk-YWRPWWKI.js";import{a as k}from"./chunk-VTVHUB7E.js";import{a as w,b as q,c as M,d as et,e as F}from"./chunk-JMSSU44E.js";import{a as I}from"./chunk-4UYSGV57.js";import{a as tt}from"./chunk-VLPNAR64.js";import{e as x}from"./chunk-35CVRQTC.js";function dt(n,i){this.positions=x(n)?n:[],this.holes=x(i)?i:[]}var st=dt;function S(){this._array=[],this._offset=0,this._length=0}Object.defineProperties(S.prototype,{length:{get:function(){return this._length}}});S.prototype.enqueue=function(n){this._array.push(n),this._length++};S.prototype.dequeue=function(){if(this._length===0)return;let n=this._array,i=this._offset,u=n[i];return n[i]=void 0,i++,i>10&&i*2>n.length&&(this._array=n.slice(i),i=0),this._offset=i,this._length--,u};S.prototype.peek=function(){if(this._length!==0)return this._array[this._offset]};S.prototype.contains=function(n){return this._array.indexOf(n)!==-1};S.prototype.clear=function(){this._array.length=this._offset=this._length=0};S.prototype.sort=function(n){this._offset>0&&(this._array=this._array.slice(this._offset),this._offset=0),this._array.sort(n)};var $=S;var b={};b.computeHierarchyPackedLength=function(n,i){let u=0,r=[n];for(;r.length>0;){let c=r.pop();if(!x(c))continue;u+=2;let a=c.positions,t=c.holes;if(x(a)&&a.length>0&&(u+=a.length*i.packedLength),x(t)){let o=t.length;for(let e=0;e<o;++e)r.push(t[e])}}return u};b.packPolygonHierarchy=function(n,i,u,r){let c=[n];for(;c.length>0;){let a=c.pop();if(!x(a))continue;let t=a.positions,o=a.holes;if(i[u++]=x(t)?t.length:0,i[u++]=x(o)?o.length:0,x(t)){let e=t.length;for(let s=0;s<e;++s,u+=r.packedLength)r.pack(t[s],i,u)}if(x(o)){let e=o.length;for(let s=0;s<e;++s)c.push(o[s])}}return u};b.unpackPolygonHierarchy=function(n,i,u){let r=n[i++],c=n[i++],a=new Array(r),t=c>0?new Array(c):void 0;for(let o=0;o<r;++o,i+=u.packedLength)a[o]=u.unpack(n,i);for(let o=0;o<c;++o)t[o]=b.unpackPolygonHierarchy(n,i,u),i=t[o].startingIndex,delete t[o].startingIndex;return{positions:a,holes:t,startingIndex:i}};var O=new M;function ht(n,i,u,r){return M.subtract(i,n,O),M.multiplyByScalar(O,u/r,O),M.add(n,O,O),[O.x,O.y]}var G=new w;function gt(n,i,u,r){return w.subtract(i,n,G),w.multiplyByScalar(G,u/r,G),w.add(n,G,G),[G.x,G.y,G.z]}b.subdivideLineCount=function(n,i,u){let c=w.distance(n,i)/u,a=Math.max(0,Math.ceil(I.log2(c)));return Math.pow(2,a)};var j=new q,Q=new q,pt=new q,mt=new w,Y=new U;b.subdivideRhumbLineCount=function(n,i,u,r){let c=n.cartesianToCartographic(i,j),a=n.cartesianToCartographic(u,Q),o=new U(c,a,n).surfaceDistance/r,e=Math.max(0,Math.ceil(I.log2(o)));return Math.pow(2,e)};b.subdivideTexcoordLine=function(n,i,u,r,c,a){let t=b.subdivideLineCount(u,r,c),o=M.distance(n,i),e=o/t,s=a;s.length=t*2;let l=0;for(let h=0;h<t;h++){let f=ht(n,i,h*e,o);s[l++]=f[0],s[l++]=f[1]}return s};b.subdivideLine=function(n,i,u,r){let c=b.subdivideLineCount(n,i,u),a=w.distance(n,i),t=a/c;x(r)||(r=[]);let o=r;o.length=c*3;let e=0;for(let s=0;s<c;s++){let l=gt(n,i,s*t,a);o[e++]=l[0],o[e++]=l[1],o[e++]=l[2]}return o};b.subdivideTexcoordRhumbLine=function(n,i,u,r,c,a,t){let o=u.cartesianToCartographic(r,j),e=u.cartesianToCartographic(c,Q);Y.setEndPoints(o,e);let s=Y.surfaceDistance/a,l=Math.max(0,Math.ceil(I.log2(s))),h=Math.pow(2,l),f=M.distance(n,i),g=f/h,m=t;m.length=h*2;let p=0;for(let d=0;d<h;d++){let y=ht(n,i,d*g,f);m[p++]=y[0],m[p++]=y[1]}return m};b.subdivideRhumbLine=function(n,i,u,r,c){let a=n.cartesianToCartographic(i,j),t=n.cartesianToCartographic(u,Q),o=new U(a,t,n);if(x(c)||(c=[]),o.surfaceDistance<=r)return c.length=3,c[0]=i.x,c[1]=i.y,c[2]=i.z,c;let e=o.surfaceDistance/r,s=Math.max(0,Math.ceil(I.log2(e))),l=Math.pow(2,s),h=o.surfaceDistance/l,f=c;f.length=l*3;let g=0;for(let m=0;m<l;m++){let p=o.interpolateUsingSurfaceDistance(m*h,pt),d=n.cartographicToCartesian(p,mt);f[g++]=d.x,f[g++]=d.y,f[g++]=d.z}return f};var yt=new w,xt=new w,wt=new w,bt=new w;b.scaleToGeodeticHeightExtruded=function(n,i,u,r,c){r=tt(r,et.default);let a=yt,t=xt,o=wt,e=bt;if(x(n)&&x(n.attributes)&&x(n.attributes.position)){let s=n.attributes.position.values,l=s.length/2;for(let h=0;h<l;h+=3)w.fromArray(s,h,o),r.geodeticSurfaceNormal(o,a),e=r.scaleToGeodeticSurface(o,e),t=w.multiplyByScalar(a,u,t),t=w.add(e,t,t),s[h+l]=t.x,s[h+1+l]=t.y,s[h+2+l]=t.z,c&&(e=w.clone(o,e)),t=w.multiplyByScalar(a,i,t),t=w.add(e,t,t),s[h]=t.x,s[h+1]=t.y,s[h+2]=t.z}return n};b.polygonOutlinesFromHierarchy=function(n,i,u){let r=[],c=new $;c.enqueue(n);let a,t,o;for(;c.length!==0;){let e=c.dequeue(),s=e.positions;if(i)for(o=s.length,a=0;a<o;a++)u.scaleToGeodeticSurface(s[a],s[a]);if(s=V(s,w.equalsEpsilon,!0),s.length<3)continue;let l=e.holes?e.holes.length:0;for(a=0;a<l;a++){let h=e.holes[a],f=h.positions;if(i)for(o=f.length,t=0;t<o;++t)u.scaleToGeodeticSurface(f[t],f[t]);if(f=V(f,w.equalsEpsilon,!0),f.length<3)continue;r.push(f);let g=0;for(x(h.holes)&&(g=h.holes.length),t=0;t<g;t++)c.enqueue(h.holes[t])}r.push(s)}return r};var Lt=new q;function Tt(n,i,u){let r=u.cartesianToCartographic(n,j),c=u.cartesianToCartographic(i,Q);if(Math.sign(r.latitude)===Math.sign(c.latitude))return;Y.setEndPoints(r,c);let a=Y.findIntersectionWithLatitude(0,Lt);if(!x(a))return;let t=Math.min(r.longitude,c.longitude),o=Math.max(r.longitude,c.longitude);if(Math.abs(o-t)>I.PI){let e=t;t=o,o=e}if(!(a.longitude<t||a.longitude>o))return u.cartographicToCartesian(a)}function Et(n,i,u,r){if(r===R.RHUMB)return Tt(n,i,u);let c=it.lineSegmentPlane(n,i,ct.ORIGIN_XY_PLANE);if(x(c))return u.scaleToGeodeticSurface(c,c)}var vt=new q;function Ct(n,i,u){let r=[],c,a,t,o,e,s=0;for(;s<n.length;){c=n[s],a=n[(s+1)%n.length],t=I.sign(c.z),o=I.sign(a.z);let l=h=>i.cartesianToCartographic(h,vt).longitude;if(t===0)r.push({position:s,type:t,visited:!1,next:o,theta:l(c)});else if(o!==0){if(e=Et(c,a,i,u),++s,!x(e))continue;n.splice(s,0,e),r.push({position:s,type:t,visited:!1,next:o,theta:l(e)})}++s}return r}function lt(n,i,u,r,c,a,t){let o=[],e=a,s=h=>f=>f.position===h,l=[];do{let h=u[e];o.push(h);let f=r.findIndex(s(e)),g=r[f];if(!x(g)){++e;continue}let{visited:m,type:p,next:d}=g;if(g.visited=!0,p===0){if(d===0){let C=r[f-(t?1:-1)];if(C?.position===e+1)C.visited=!0;else{++e;continue}}if(!m&&t&&d>0||a===e&&!t&&d<0){++e;continue}}if(!(t?p>=0:p<=0)){++e;continue}m||l.push(e);let L=f+(t?1:-1),_=r[L];if(!x(_)){++e;continue}e=_.position}while(e<u.length&&e>=0&&e!==a&&o.length<u.length);n.splice(i,c,o);for(let h of l)i=lt(n,++i,u,r,0,h,!t);return i}b.splitPolygonsOnEquator=function(n,i,u,r){x(r)||(r=[]),r.splice(0,0,...n),r.length=n.length;let c=0;for(;c<r.length;){let a=r[c],t=a.slice();if(a.length<3){r[c]=t,++c;continue}let o=Ct(t,i,u);if(t.length===a.length||o.length<=1){r[c]=t,++c;continue}o.sort((s,l)=>s.theta-l.theta);let e=t[0].z>=0;c=lt(r,c,t,o,1,0,e)}return r};b.polygonsFromHierarchy=function(n,i,u,r,c,a){let t=[],o=[],e=new $;e.enqueue(n);let s=x(a);for(;e.length!==0;){let l=e.dequeue(),h=l.positions,f=l.holes,g,m;if(r)for(m=h.length,g=0;g<m;g++)c.scaleToGeodeticSurface(h[g],h[g]);if(i||(h=V(h,w.equalsEpsilon,!0)),h.length<3)continue;let p=u(h);if(!x(p))continue;let d=[],y=H.computeWindingOrder2D(p);if(y===Z.CLOCKWISE&&(p.reverse(),h=h.slice().reverse()),s){s=!1;let P=[h];if(P=a(P,P),P.length>1){for(let v of P)e.enqueue(new st(v,f));continue}}let L=h.slice(),_=x(f)?f.length:0,C=[],T;for(g=0;g<_;g++){let P=f[g],v=P.positions;if(r)for(m=v.length,T=0;T<m;++T)c.scaleToGeodeticSurface(v[T],v[T]);if(i||(v=V(v,w.equalsEpsilon,!0)),v.length<3)continue;let E=u(v);if(!x(E))continue;y=H.computeWindingOrder2D(E),y===Z.CLOCKWISE&&(E.reverse(),v=v.slice().reverse()),C.push(v),d.push(L.length),L=L.concat(v),p=p.concat(E);let D=0;for(x(P.holes)&&(D=P.holes.length),T=0;T<D;T++)e.enqueue(P.holes[T])}t.push({outerRing:h,holes:C}),o.push({positions:L,positions2D:p,holes:d})}return{hierarchy:t,polygons:o}};var Pt=new M,Dt=new w,It=new K,_t=new F;b.computeBoundingRectangle=function(n,i,u,r,c){let a=K.fromAxisAngle(n,r,It),t=F.fromQuaternion(a,_t),o=Number.POSITIVE_INFINITY,e=Number.NEGATIVE_INFINITY,s=Number.POSITIVE_INFINITY,l=Number.NEGATIVE_INFINITY,h=u.length;for(let f=0;f<h;++f){let g=w.clone(u[f],Dt);F.multiplyByVector(t,g,g);let m=i(g,Pt);x(m)&&(o=Math.min(o,m.x),e=Math.max(e,m.x),s=Math.min(s,m.y),l=Math.max(l,m.y))}return c.x=o,c.y=s,c.width=e-o,c.height=l-s,c};b.createGeometryFromPositions=function(n,i,u,r,c,a,t){let o=H.triangulate(i.positions2D,i.holes);o.length<3&&(o=[0,1,2]);let e=i.positions,s=x(u),l=s?u.positions:void 0;if(c){let h=e.length,f=new Array(h*3),g=0;for(let d=0;d<h;d++){let y=e[d];f[g++]=y.x,f[g++]=y.y,f[g++]=y.z}let m={attributes:{position:new z({componentDatatype:k.DOUBLE,componentsPerAttribute:3,values:f})},indices:o,primitiveType:J.TRIANGLES};s&&(m.attributes.st=new z({componentDatatype:k.FLOAT,componentsPerAttribute:2,values:M.packArray(l)}));let p=new X(m);return a.normal?rt.computeNormal(p):p}if(t===R.GEODESIC)return H.computeSubdivision(n,e,o,l,r);if(t===R.RHUMB)return H.computeRhumbLineSubdivision(n,e,o,l,r)};var ut=[],at=[],At=new w,Mt=new w;b.computeWallGeometry=function(n,i,u,r,c,a){let t,o,e,s,l,h,f,g,m,p=n.length,d=0,y=0,L=x(i),_=L?i.positions:void 0;if(c)for(o=p*3*2,t=new Array(o*2),L&&(m=p*2*2,g=new Array(m*2)),e=0;e<p;e++)s=n[e],l=n[(e+1)%p],t[d]=t[d+o]=s.x,++d,t[d]=t[d+o]=s.y,++d,t[d]=t[d+o]=s.z,++d,t[d]=t[d+o]=l.x,++d,t[d]=t[d+o]=l.y,++d,t[d]=t[d+o]=l.z,++d,L&&(h=_[e],f=_[(e+1)%p],g[y]=g[y+m]=h.x,++y,g[y]=g[y+m]=h.y,++y,g[y]=g[y+m]=f.x,++y,g[y]=g[y+m]=f.y,++y);else{let E=I.chordLength(r,u.maximumRadius),D=0;if(a===R.GEODESIC)for(e=0;e<p;e++)D+=b.subdivideLineCount(n[e],n[(e+1)%p],E);else if(a===R.RHUMB)for(e=0;e<p;e++)D+=b.subdivideRhumbLineCount(u,n[e],n[(e+1)%p],E);for(o=(D+p)*3,t=new Array(o*2),L&&(m=(D+p)*2,g=new Array(m*2)),e=0;e<p;e++){s=n[e],l=n[(e+1)%p];let A,N;L&&(h=_[e],f=_[(e+1)%p]),a===R.GEODESIC?(A=b.subdivideLine(s,l,E,at),L&&(N=b.subdivideTexcoordLine(h,f,s,l,E,ut))):a===R.RHUMB&&(A=b.subdivideRhumbLine(u,s,l,E,at),L&&(N=b.subdivideTexcoordRhumbLine(h,f,u,s,l,E,ut)));let ft=A.length;for(let B=0;B<ft;++B,++d)t[d]=A[B],t[d+o]=A[B];if(t[d]=l.x,t[d+o]=l.x,++d,t[d]=l.y,t[d+o]=l.y,++d,t[d]=l.z,t[d+o]=l.z,++d,L){let B=N.length;for(let W=0;W<B;++W,++y)g[y]=N[W],g[y+m]=N[W];g[y]=f.x,g[y+m]=f.x,++y,g[y]=f.y,g[y+m]=f.y,++y}}}p=t.length;let C=ot.createTypedArray(p/3,p-n.length*6),T=0;for(p/=6,e=0;e<p;e++){let E=e,D=E+1,A=E+p,N=A+1;s=w.fromArray(t,E*3,At),l=w.fromArray(t,D*3,Mt),!w.equalsEpsilon(s,l,I.EPSILON10,I.EPSILON10)&&(C[T++]=E,C[T++]=A,C[T++]=D,C[T++]=D,C[T++]=A,C[T++]=N)}let P={attributes:new nt({position:new z({componentDatatype:k.DOUBLE,componentsPerAttribute:3,values:t})}),indices:C,primitiveType:J.TRIANGLES};return L&&(P.attributes.st=new z({componentDatatype:k.FLOAT,componentsPerAttribute:2,values:g})),new X(P)};var re=b;export{re as a};
