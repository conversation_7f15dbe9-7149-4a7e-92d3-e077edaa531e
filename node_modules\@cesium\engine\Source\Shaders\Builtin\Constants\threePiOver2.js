//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * A built-in GLSL floating-point constant for <code>3pi/2</code>.\n\
 *\n\
 * @alias czm_threePiOver2\n\
 * @glslConstant\n\
 *\n\
 * @see CesiumMath.THREE_PI_OVER_TWO\n\
 *\n\
 * @example\n\
 * // GLSL declaration\n\
 * const float czm_threePiOver2 = ...;\n\
 *\n\
 * // Example\n\
 * float pi = (2.0 / 3.0) * czm_threePiOver2;\n\
 */\n\
const float czm_threePiOver2 = 4.71238898038469;\n\
";
