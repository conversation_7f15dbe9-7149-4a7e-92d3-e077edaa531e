{"name": "cesium", "version": "1.127.0", "description": "CesiumJS is a JavaScript library for creating 3D globes and 2D maps in a web browser without a plugin.", "homepage": "http://cesium.com/cesiumjs/", "license": "Apache-2.0", "author": {"name": "Cesium GS, Inc.", "url": "https://cesium.com"}, "contributors": [{"name": "CesiumJS community", "url": "https://github.com/CesiumGS/cesium/blob/main/CONTRIBUTORS.md"}], "keywords": ["3D", "webgl", "geospatial", "map", "globe"], "repository": {"type": "git", "url": "https://github.com/CesiumGS/cesium.git"}, "bugs": {"url": "https://github.com/CesiumGS/cesium/issues"}, "main": "index.cjs", "module": "./Source/Cesium.js", "types": "./Source/Cesium.d.ts", "exports": {"./package.json": "./package.json", "./Source/*": "./Source/*", "./Source/*.js": null, "./Build/*": "./Build/*", "./Build/*.js": null, ".": {"types": "./Source/Cesium.d.ts", "require": "./index.cjs", "import": "./Source/Cesium.js"}}, "type": "module", "sideEffects": ["./Source/ThirdParty/**/*", "./Source/Widgets/**/*.css", "./Source/Workers/*", "./Specs/**/*"], "dependencies": {"@cesium/engine": "^15.0.0", "@cesium/widgets": "^11.0.0"}, "devDependencies": {"@cesium/eslint-config": "^12.0.0", "@playwright/test": "^1.41.1", "chokidar": "^4.0.1", "cloc": "^2.4.0-cloc", "compression": "^1.7.4", "esbuild": "^0.25.0", "eslint": "^9.1.1", "eslint-plugin-html": "^8.1.1", "express": "^4.17.1", "globals": "^16.0.0", "globby": "^14.0.0", "glsl-strip-comments": "^1.0.0", "gulp": "^5.0.0", "gulp-clean-css": "^4.3.0", "gulp-insert": "^0.5.0", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.3", "gulp-tap": "^2.0.0", "gulp-zip": "^6.0.0", "husky": "^9.0.7", "istanbul-lib-instrument": "^6.0.0", "jasmine-core": "^5.0.1", "jsdoc": "^3.6.7", "karma": "^6.3.20", "karma-chrome-launcher": "^3.1.0", "karma-coverage": "^2.0.3", "karma-detect-browsers": "^2.3.3", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^2.1.1", "karma-ie-launcher": "^1.0.0", "karma-jasmine": "^5.1.0", "karma-longest-reporter": "^1.1.0", "karma-safari-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.4.0", "karma-spec-reporter": "^0.0.36", "markdownlint-cli": "^0.44.0", "merge-stream": "^2.0.0", "mkdirp": "^3.0.1", "node-fetch": "^3.2.10", "open": "^10.0.2", "prettier": "3.5.1", "prismjs": "^1.28.0", "request": "^2.79.0", "rimraf": "^5.0.0", "tsd-jsdoc": "^2.5.0", "typescript": "^5.3.2", "yargs": "^17.0.1"}, "scripts": {"prepare": "gulp prepare && husky && playwright install --with-deps", "start": "node server.js", "start-public": "node server.js --public", "build": "gulp build", "build-release": "gulp buildRelease", "build-watch": "gulp buildWatch", "build-ts": "gulp buildTs", "build-third-party": "gulp buildThirdParty", "build-apps": "gulp buildApps", "clean": "gulp clean", "cloc": "gulp cloc", "coverage": "gulp coverage", "build-docs": "gulp buildDocs", "build-docs-watch": "gulp buildDocsWatch", "eslint": "eslint \"./**/*.*js\" \"./**/*.html\" --cache --quiet", "make-zip": "gulp makeZip", "markdownlint": "markdownlint \"**/*.md\"", "release": "gulp release", "website-release": "gulp websiteRelease", "test": "gulp test", "test-all": "gulp test --all", "test-webgl": "gulp test --include WebGL", "test-non-webgl": "gulp test --exclude WebGL", "test-webgl-validation": "gulp test --webglValidation", "test-webgl-stub": "gulp test --webglStub", "test-release": "gulp test --release", "test-e2e": "playwright test -c Specs/e2e/playwright.config.js --project=chromium", "test-e2e-all": "playwright test -c Specs/e2e/playwright.config.js", "test-e2e-release": "release=true playwright test -c Specs/e2e/playwright.config.js --project=chromium", "test-e2e-release-all": "release=true playwright test -c Specs/e2e/playwright.config.js", "test-e2e-report": "playwright show-report Build/Specs/e2e/report", "test-e2e-update": "playwright test -c Specs/e2e/playwright.config.js --project=chromium -u", "deploy-status": "gulp deployStatus", "deploy-set-version": "gulp deploySetVersion", "prettier": "prettier --write \"**/*\"", "prettier-check": "prettier --check \"**/*\"", "cspell": "npx cspell lint -c .vscode/cspell.json --unique --no-progress"}, "engines": {"node": ">=18.18.0"}, "lint-staged": {"*.{js,cjs,mjs,css,html}": ["eslint --cache --quiet", "prettier --write"], "*.md": ["markdownlint", "prettier --write"]}, "workspaces": ["packages/engine", "packages/widgets"]}