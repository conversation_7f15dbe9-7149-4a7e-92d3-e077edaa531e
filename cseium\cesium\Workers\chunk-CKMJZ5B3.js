/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.127
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as ct}from"./chunk-EEKB62LQ.js";import{b as at,c as ht,d as nt}from"./chunk-GSNDLQ4C.js";import{a as et}from"./chunk-VTVHUB7E.js";import{a,b as tt,c as g,d as vt}from"./chunk-JMSSU44E.js";import{a as Y}from"./chunk-4UYSGV57.js";import{a as xt}from"./chunk-ANLJ4KBN.js";import{a as K}from"./chunk-VLPNAR64.js";import{b as A}from"./chunk-GE5NEIZC.js";import{e as F}from"./chunk-35CVRQTC.js";var ut={CLOCKWISE:xt.CW,COUNTER_CLOCKWISE:xt.CCW};ut.validate=function(t){return t===ut.CLOCKWISE||t===ut.COUNTER_CLOCKWISE};var pt=Object.freeze(ut);function dt(t,n,e=2){let o=n&&n.length,r=o?n[0]*e:t.length,s=St(t,0,r,e,!0),i=[];if(!s||s.next===s.prev)return i;let c,x,u;if(o&&(s=Wt(t,n,s,e)),t.length>80*e){c=1/0,x=1/0;let p=-1/0,y=-1/0;for(let b=e;b<r;b+=e){let S=t[b],h=t[b+1];S<c&&(c=S),h<x&&(x=h),S>p&&(p=S),h>y&&(y=h)}u=Math.max(p-c,y-x),u=u!==0?32767/u:0}return it(s,i,e,c,x,u,0),i}function St(t,n,e,o,r){let s;if(r===qt(t,n,e,o)>0)for(let i=n;i<e;i+=o)s=wt(i/o|0,t[i],t[i+1],s);else for(let i=e-o;i>=n;i-=o)s=wt(i/o|0,t[i],t[i+1],s);return s&&V(s,s.next)&&(st(s),s=s.next),s}function N(t,n){if(!t)return t;n||(n=t);let e=t,o;do if(o=!1,!e.steiner&&(V(e,e.next)||w(e.prev,e,e.next)===0)){if(st(e),e=n=e.prev,e===e.next)break;o=!0}else e=e.next;while(o||e!==n);return n}function it(t,n,e,o,r,s,i){if(!t)return;!i&&s&&kt(t,o,r,s);let c=t;for(;t.prev!==t.next;){let x=t.prev,u=t.next;if(s?zt(t,o,r,s):Rt(t)){n.push(x.i,t.i,u.i),st(t),t=u.next,c=u.next;continue}if(t=u,t===c){i?i===1?(t=Bt(N(t),n),it(t,n,e,o,r,s,2)):i===2&&It(t,n,e,o,r,s):it(N(t),n,e,o,r,s,1);break}}}function Rt(t){let n=t.prev,e=t,o=t.next;if(w(n,e,o)>=0)return!1;let r=n.x,s=e.x,i=o.x,c=n.y,x=e.y,u=o.y,p=Math.min(r,s,i),y=Math.min(c,x,u),b=Math.max(r,s,i),S=Math.max(c,x,u),h=o.next;for(;h!==n;){if(h.x>=p&&h.x<=b&&h.y>=y&&h.y<=S&&rt(r,c,s,x,i,u,h.x,h.y)&&w(h.prev,h,h.next)>=0)return!1;h=h.next}return!0}function zt(t,n,e,o){let r=t.prev,s=t,i=t.next;if(w(r,s,i)>=0)return!1;let c=r.x,x=s.x,u=i.x,p=r.y,y=s.y,b=i.y,S=Math.min(c,x,u),h=Math.min(p,y,b),T=Math.max(c,x,u),L=Math.max(p,y,b),z=yt(S,h,n,e,o),D=yt(T,L,n,e,o),l=t.prevZ,f=t.nextZ;for(;l&&l.z>=z&&f&&f.z<=D;){if(l.x>=S&&l.x<=T&&l.y>=h&&l.y<=L&&l!==r&&l!==i&&rt(c,p,x,y,u,b,l.x,l.y)&&w(l.prev,l,l.next)>=0||(l=l.prevZ,f.x>=S&&f.x<=T&&f.y>=h&&f.y<=L&&f!==r&&f!==i&&rt(c,p,x,y,u,b,f.x,f.y)&&w(f.prev,f,f.next)>=0))return!1;f=f.nextZ}for(;l&&l.z>=z;){if(l.x>=S&&l.x<=T&&l.y>=h&&l.y<=L&&l!==r&&l!==i&&rt(c,p,x,y,u,b,l.x,l.y)&&w(l.prev,l,l.next)>=0)return!1;l=l.prevZ}for(;f&&f.z<=D;){if(f.x>=S&&f.x<=T&&f.y>=h&&f.y<=L&&f!==r&&f!==i&&rt(c,p,x,y,u,b,f.x,f.y)&&w(f.prev,f,f.next)>=0)return!1;f=f.nextZ}return!0}function Bt(t,n){let e=t;do{let o=e.prev,r=e.next.next;!V(o,r)&&Mt(o,e,e.next,r)&&ot(o,r)&&ot(r,o)&&(n.push(o.i,e.i,r.i),st(e),st(e.next),e=t=r),e=e.next}while(e!==t);return N(e)}function It(t,n,e,o,r,s){let i=t;do{let c=i.next.next;for(;c!==i.prev;){if(i.i!==c.i&&Kt(i,c)){let x=At(i,c);i=N(i,i.next),x=N(x,x.next),it(i,n,e,o,r,s,0),it(x,n,e,o,r,s,0);return}c=c.next}i=i.next}while(i!==t)}function Wt(t,n,e,o){let r=[];for(let s=0,i=n.length;s<i;s++){let c=n[s]*o,x=s<i-1?n[s+1]*o:t.length,u=St(t,c,x,o,!1);u===u.next&&(u.steiner=!0),r.push(_t(u))}r.sort($t);for(let s=0;s<r.length;s++)e=Gt(r[s],e);return e}function $t(t,n){let e=t.x-n.x;if(e===0&&(e=t.y-n.y,e===0)){let o=(t.next.y-t.y)/(t.next.x-t.x),r=(n.next.y-n.y)/(n.next.x-n.x);e=o-r}return e}function Gt(t,n){let e=Ht(t,n);if(!e)return n;let o=At(e,t);return N(o,o.next),N(e,e.next)}function Ht(t,n){let e=n,o=t.x,r=t.y,s=-1/0,i;if(V(t,e))return e;do{if(V(t,e.next))return e.next;if(r<=e.y&&r>=e.next.y&&e.next.y!==e.y){let y=e.x+(r-e.y)*(e.next.x-e.x)/(e.next.y-e.y);if(y<=o&&y>s&&(s=y,i=e.x<e.next.x?e:e.next,y===o))return i}e=e.next}while(e!==n);if(!i)return null;let c=i,x=i.x,u=i.y,p=1/0;e=i;do{if(o>=e.x&&e.x>=x&&o!==e.x&&bt(r<u?o:s,r,x,u,r<u?s:o,r,e.x,e.y)){let y=Math.abs(r-e.y)/(o-e.x);ot(e,t)&&(y<p||y===p&&(e.x>i.x||e.x===i.x&&Nt(i,e)))&&(i=e,p=y)}e=e.next}while(e!==c);return i}function Nt(t,n){return w(t.prev,t,n.prev)<0&&w(n.next,t,t.next)<0}function kt(t,n,e,o){let r=t;do r.z===0&&(r.z=yt(r.x,r.y,n,e,o)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next;while(r!==t);r.prevZ.nextZ=null,r.prevZ=null,Ut(r)}function Ut(t){let n,e=1;do{let o=t,r;t=null;let s=null;for(n=0;o;){n++;let i=o,c=0;for(let u=0;u<e&&(c++,i=i.nextZ,!!i);u++);let x=e;for(;c>0||x>0&&i;)c!==0&&(x===0||!i||o.z<=i.z)?(r=o,o=o.nextZ,c--):(r=i,i=i.nextZ,x--),s?s.nextZ=r:t=r,r.prevZ=s,s=r;o=i}s.nextZ=null,e*=2}while(n>1);return t}function yt(t,n,e,o,r){return t=(t-e)*r|0,n=(n-o)*r|0,t=(t|t<<8)&16711935,t=(t|t<<4)&252645135,t=(t|t<<2)&858993459,t=(t|t<<1)&1431655765,n=(n|n<<8)&16711935,n=(n|n<<4)&252645135,n=(n|n<<2)&858993459,n=(n|n<<1)&1431655765,t|n<<1}function _t(t){let n=t,e=t;do(n.x<e.x||n.x===e.x&&n.y<e.y)&&(e=n),n=n.next;while(n!==t);return e}function bt(t,n,e,o,r,s,i,c){return(r-i)*(n-c)>=(t-i)*(s-c)&&(t-i)*(o-c)>=(e-i)*(n-c)&&(e-i)*(s-c)>=(r-i)*(o-c)}function rt(t,n,e,o,r,s,i,c){return!(t===i&&n===c)&&bt(t,n,e,o,r,s,i,c)}function Kt(t,n){return t.next.i!==n.i&&t.prev.i!==n.i&&!Vt(t,n)&&(ot(t,n)&&ot(n,t)&&jt(t,n)&&(w(t.prev,t,n.prev)||w(t,n.prev,n))||V(t,n)&&w(t.prev,t,t.next)>0&&w(n.prev,n,n.next)>0)}function w(t,n,e){return(n.y-t.y)*(e.x-n.x)-(n.x-t.x)*(e.y-n.y)}function V(t,n){return t.x===n.x&&t.y===n.y}function Mt(t,n,e,o){let r=ft(w(t,n,e)),s=ft(w(t,n,o)),i=ft(w(e,o,t)),c=ft(w(e,o,n));return!!(r!==s&&i!==c||r===0&&lt(t,e,n)||s===0&&lt(t,o,n)||i===0&&lt(e,t,o)||c===0&&lt(e,n,o))}function lt(t,n,e){return n.x<=Math.max(t.x,e.x)&&n.x>=Math.min(t.x,e.x)&&n.y<=Math.max(t.y,e.y)&&n.y>=Math.min(t.y,e.y)}function ft(t){return t>0?1:t<0?-1:0}function Vt(t,n){let e=t;do{if(e.i!==t.i&&e.next.i!==t.i&&e.i!==n.i&&e.next.i!==n.i&&Mt(e,e.next,t,n))return!0;e=e.next}while(e!==t);return!1}function ot(t,n){return w(t.prev,t,t.next)<0?w(t,n,t.next)>=0&&w(t,t.prev,n)>=0:w(t,n,t.prev)<0||w(t,t.next,n)<0}function jt(t,n){let e=t,o=!1,r=(t.x+n.x)/2,s=(t.y+n.y)/2;do e.y>s!=e.next.y>s&&e.next.y!==e.y&&r<(e.next.x-e.x)*(s-e.y)/(e.next.y-e.y)+e.x&&(o=!o),e=e.next;while(e!==t);return o}function At(t,n){let e=mt(t.i,t.x,t.y),o=mt(n.i,n.x,n.y),r=t.next,s=n.prev;return t.next=n,n.prev=t,e.next=r,r.prev=e,o.next=e,e.prev=o,s.next=o,o.prev=s,o}function wt(t,n,e,o){let r=mt(t,n,e);return o?(r.next=o.next,r.prev=o,o.next.prev=r,o.next=r):(r.prev=r,r.next=r),r}function st(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function mt(t,n,e){return{i:t,x:n,y:e,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}function qt(t,n,e,o){let r=0;for(let s=n,i=e-o;s<e;s+=o)r+=(t[i]-t[s])*(t[s+1]+t[i+1]),i=s;return r}var Jt=new a,Qt=new a,B={};B.computeArea2D=function(t){A.defined("positions",t),A.typeOf.number.greaterThanOrEquals("positions.length",t.length,3);let n=t.length,e=0;for(let o=n-1,r=0;r<n;o=r++){let s=t[o],i=t[r];e+=s.x*i.y-i.x*s.y}return e*.5};B.computeWindingOrder2D=function(t){return B.computeArea2D(t)>0?pt.COUNTER_CLOCKWISE:pt.CLOCKWISE};B.triangulate=function(t,n){A.defined("positions",t);let e=g.packArray(t);return dt(e,n,2)};var Ot=new a,Zt=new a,Lt=new a,Ct=new a,Et=new a,Tt=new a,R=new a,Ft=new g,Dt=new g,Pt=new g,j=new g;B.computeSubdivision=function(t,n,e,o,r){r=K(r,Y.RADIANS_PER_DEGREE);let s=F(o);A.typeOf.object("ellipsoid",t),A.defined("positions",n),A.defined("indices",e),A.typeOf.number.greaterThanOrEquals("indices.length",e.length,3),A.typeOf.number.equals("indices.length % 3","0",e.length%3,0),A.typeOf.number.greaterThan("granularity",r,0);let i=e.slice(0),c,x=n.length,u=new Array(x*3),p=new Array(x*2),y=0,b=0;for(c=0;c<x;c++){let l=n[c];if(u[y++]=l.x,u[y++]=l.y,u[y++]=l.z,s){let f=o[c];p[b++]=f.x,p[b++]=f.y}}let S=[],h={},T=t.maximumRadius,L=Y.chordLength(r,T),z=L*L;for(;i.length>0;){let l=i.pop(),f=i.pop(),m=i.pop(),M=a.fromArray(u,m*3,Ot),C=a.fromArray(u,f*3,Zt),q=a.fromArray(u,l*3,Lt),J,Q,I;s&&(J=g.fromArray(p,m*2,Ft),Q=g.fromArray(p,f*2,Dt),I=g.fromArray(p,l*2,Pt));let k=a.multiplyByScalar(a.normalize(M,Ct),T,Ct),U=a.multiplyByScalar(a.normalize(C,Et),T,Et),W=a.multiplyByScalar(a.normalize(q,Tt),T,Tt),$=a.magnitudeSquared(a.subtract(k,U,R)),G=a.magnitudeSquared(a.subtract(U,W,R)),X=a.magnitudeSquared(a.subtract(W,k,R)),H=Math.max($,G,X),O,v,d;H>z?$===H?(O=`${Math.min(m,f)} ${Math.max(m,f)}`,c=h[O],F(c)||(v=a.add(M,C,R),a.multiplyByScalar(v,.5,v),u.push(v.x,v.y,v.z),c=u.length/3-1,h[O]=c,s&&(d=g.add(J,Q,j),g.multiplyByScalar(d,.5,d),p.push(d.x,d.y))),i.push(m,c,l),i.push(c,f,l)):G===H?(O=`${Math.min(f,l)} ${Math.max(f,l)}`,c=h[O],F(c)||(v=a.add(C,q,R),a.multiplyByScalar(v,.5,v),u.push(v.x,v.y,v.z),c=u.length/3-1,h[O]=c,s&&(d=g.add(Q,I,j),g.multiplyByScalar(d,.5,d),p.push(d.x,d.y))),i.push(f,c,m),i.push(c,l,m)):X===H&&(O=`${Math.min(l,m)} ${Math.max(l,m)}`,c=h[O],F(c)||(v=a.add(q,M,R),a.multiplyByScalar(v,.5,v),u.push(v.x,v.y,v.z),c=u.length/3-1,h[O]=c,s&&(d=g.add(I,J,j),g.multiplyByScalar(d,.5,d),p.push(d.x,d.y))),i.push(l,c,f),i.push(c,m,f)):(S.push(m),S.push(f),S.push(l))}let D={attributes:{position:new nt({componentDatatype:et.DOUBLE,componentsPerAttribute:3,values:u})},indices:S,primitiveType:at.TRIANGLES};return s&&(D.attributes.st=new nt({componentDatatype:et.FLOAT,componentsPerAttribute:2,values:p})),new ht(D)};var Xt=new tt,Yt=new tt,te=new tt,gt=new tt;B.computeRhumbLineSubdivision=function(t,n,e,o,r){r=K(r,Y.RADIANS_PER_DEGREE);let s=F(o);A.typeOf.object("ellipsoid",t),A.defined("positions",n),A.defined("indices",e),A.typeOf.number.greaterThanOrEquals("indices.length",e.length,3),A.typeOf.number.equals("indices.length % 3","0",e.length%3,0),A.typeOf.number.greaterThan("granularity",r,0);let i=e.slice(0),c,x=n.length,u=new Array(x*3),p=new Array(x*2),y=0,b=0;for(c=0;c<x;c++){let m=n[c];if(u[y++]=m.x,u[y++]=m.y,u[y++]=m.z,s){let M=o[c];p[b++]=M.x,p[b++]=M.y}}let S=[],h={},T=t.maximumRadius,L=Y.chordLength(r,T),z=new ct(void 0,void 0,t),D=new ct(void 0,void 0,t),l=new ct(void 0,void 0,t);for(;i.length>0;){let m=i.pop(),M=i.pop(),C=i.pop(),q=a.fromArray(u,C*3,Ot),J=a.fromArray(u,M*3,Zt),Q=a.fromArray(u,m*3,Lt),I,k,U;s&&(I=g.fromArray(p,C*2,Ft),k=g.fromArray(p,M*2,Dt),U=g.fromArray(p,m*2,Pt));let W=t.cartesianToCartographic(q,Xt),$=t.cartesianToCartographic(J,Yt),G=t.cartesianToCartographic(Q,te);z.setEndPoints(W,$);let X=z.surfaceDistance;D.setEndPoints($,G);let H=D.surfaceDistance;l.setEndPoints(G,W);let O=l.surfaceDistance,v=Math.max(X,H,O),d,P,_,Z,E;v>L?X===v?(d=`${Math.min(C,M)} ${Math.max(C,M)}`,c=h[d],F(c)||(P=z.interpolateUsingFraction(.5,gt),_=(W.height+$.height)*.5,Z=a.fromRadians(P.longitude,P.latitude,_,t,R),u.push(Z.x,Z.y,Z.z),c=u.length/3-1,h[d]=c,s&&(E=g.add(I,k,j),g.multiplyByScalar(E,.5,E),p.push(E.x,E.y))),i.push(C,c,m),i.push(c,M,m)):H===v?(d=`${Math.min(M,m)} ${Math.max(M,m)}`,c=h[d],F(c)||(P=D.interpolateUsingFraction(.5,gt),_=($.height+G.height)*.5,Z=a.fromRadians(P.longitude,P.latitude,_,t,R),u.push(Z.x,Z.y,Z.z),c=u.length/3-1,h[d]=c,s&&(E=g.add(k,U,j),g.multiplyByScalar(E,.5,E),p.push(E.x,E.y))),i.push(M,c,C),i.push(c,m,C)):O===v&&(d=`${Math.min(m,C)} ${Math.max(m,C)}`,c=h[d],F(c)||(P=l.interpolateUsingFraction(.5,gt),_=(G.height+W.height)*.5,Z=a.fromRadians(P.longitude,P.latitude,_,t,R),u.push(Z.x,Z.y,Z.z),c=u.length/3-1,h[d]=c,s&&(E=g.add(U,I,j),g.multiplyByScalar(E,.5,E),p.push(E.x,E.y))),i.push(m,c,M),i.push(c,C,M)):(S.push(C),S.push(M),S.push(m))}let f={attributes:{position:new nt({componentDatatype:et.DOUBLE,componentsPerAttribute:3,values:u})},indices:S,primitiveType:at.TRIANGLES};return s&&(f.attributes.st=new nt({componentDatatype:et.FLOAT,componentsPerAttribute:2,values:p})),new ht(f)};B.scaleToGeodeticHeight=function(t,n,e,o){e=K(e,vt.default);let r=Jt,s=Qt;if(n=K(n,0),o=K(o,!0),F(t)){let i=t.length;for(let c=0;c<i;c+=3)a.fromArray(t,c,s),o&&(s=e.scaleToGeodeticSurface(s,s)),n!==0&&(r=e.geodeticSurfaceNormal(s,r),a.multiplyByScalar(r,n,r),a.add(s,r,s)),t[c]=s.x,t[c+1]=s.y,t[c+2]=s.z}return t};var ve=B;export{pt as a,ve as b};
