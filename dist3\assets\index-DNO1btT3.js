(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const o of n)if(o.type==="childList")for(const r of o.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&s(r)}).observe(document,{childList:!0,subtree:!0});function i(n){const o={};return n.integrity&&(o.integrity=n.integrity),n.referrerPolicy&&(o.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?o.credentials="include":n.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(n){if(n.ep)return;n.ep=!0;const o=i(n);fetch(n.href,o)}})();/**
* @vue/shared v3.5.12
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Qi(e){const t=Object.create(null);for(const i of e.split(","))t[i]=1;return i=>i in t}const N={},ut=[],De=()=>{},jn=()=>!1,ni=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ui=e=>e.startsWith("onUpdate:"),se=Object.assign,ji=(e,t)=>{const i=e.indexOf(t);i>-1&&e.splice(i,1)},Nn=Object.prototype.hasOwnProperty,W=(e,t)=>Nn.call(e,t),D=Array.isArray,bt=e=>Rt(e)==="[object Map]",Ni=e=>Rt(e)==="[object Set]",as=e=>Rt(e)==="[object Date]",R=e=>typeof e=="function",ee=e=>typeof e=="string",ke=e=>typeof e=="symbol",q=e=>e!==null&&typeof e=="object",Qs=e=>(q(e)||R(e))&&R(e.then)&&R(e.catch),Yn=Object.prototype.toString,Rt=e=>Yn.call(e),kn=e=>Rt(e).slice(8,-1),Kn=e=>Rt(e)==="[object Object]",Yi=e=>ee(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,xt=Qi(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),oi=e=>{const t=Object.create(null);return i=>t[i]||(t[i]=e(i))},Gn=/-(\w)/g,Ke=oi(e=>e.replace(Gn,(t,i)=>i?i.toUpperCase():"")),Jn=/\B([A-Z])/g,rt=oi(e=>e.replace(Jn,"-$1").toLowerCase()),Us=oi(e=>e.charAt(0).toUpperCase()+e.slice(1)),gi=oi(e=>e?`on${Us(e)}`:""),Ye=(e,t)=>!Object.is(e,t),Yt=(e,...t)=>{for(let i=0;i<e.length;i++)e[i](...t)},js=(e,t,i,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:i})},Zt=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let us;const ri=()=>us||(us=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ki(e){if(D(e)){const t={};for(let i=0;i<e.length;i++){const s=e[i],n=ee(s)?Xn(s):ki(s);if(n)for(const o in n)t[o]=n[o]}return t}else if(ee(e)||q(e))return e}const qn=/;(?![^(]*\))/g,Zn=/:([^]+)/,zn=/\/\*[^]*?\*\//g;function Xn(e){const t={};return e.replace(zn,"").split(qn).forEach(i=>{if(i){const s=i.split(Zn);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Ki(e){let t="";if(ee(e))t=e;else if(D(e))for(let i=0;i<e.length;i++){const s=Ki(e[i]);s&&(t+=s+" ")}else if(q(e))for(const i in e)e[i]&&(t+=i+" ");return t.trim()}const $n="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",eo=Qi($n);function Ns(e){return!!e||e===""}function to(e,t){if(e.length!==t.length)return!1;let i=!0;for(let s=0;i&&s<e.length;s++)i=li(e[s],t[s]);return i}function li(e,t){if(e===t)return!0;let i=as(e),s=as(t);if(i||s)return i&&s?e.getTime()===t.getTime():!1;if(i=ke(e),s=ke(t),i||s)return e===t;if(i=D(e),s=D(t),i||s)return i&&s?to(e,t):!1;if(i=q(e),s=q(t),i||s){if(!i||!s)return!1;const n=Object.keys(e).length,o=Object.keys(t).length;if(n!==o)return!1;for(const r in e){const l=e.hasOwnProperty(r),a=t.hasOwnProperty(r);if(l&&!a||!l&&a||!li(e[r],t[r]))return!1}}return String(e)===String(t)}function io(e,t){return e.findIndex(i=>li(i,t))}/**
* @vue/reactivity v3.5.12
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ce;class so{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ce,!t&&Ce&&(this.index=(Ce.scopes||(Ce.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,i;if(this.scopes)for(t=0,i=this.scopes.length;t<i;t++)this.scopes[t].pause();for(t=0,i=this.effects.length;t<i;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,i;if(this.scopes)for(t=0,i=this.scopes.length;t<i;t++)this.scopes[t].resume();for(t=0,i=this.effects.length;t<i;t++)this.effects[t].resume()}}run(t){if(this._active){const i=Ce;try{return Ce=this,t()}finally{Ce=i}}}on(){Ce=this}off(){Ce=this.parent}stop(t){if(this._active){let i,s;for(i=0,s=this.effects.length;i<s;i++)this.effects[i].stop();for(i=0,s=this.cleanups.length;i<s;i++)this.cleanups[i]();if(this.scopes)for(i=0,s=this.scopes.length;i<s;i++)this.scopes[i].stop(!0);if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0,this._active=!1}}}function no(){return Ce}let Y;const mi=new WeakSet;class Ys{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ce&&Ce.active&&Ce.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,mi.has(this)&&(mi.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ks(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,fs(this),Gs(this);const t=Y,i=Me;Y=this,Me=!0;try{return this.fn()}finally{Js(this),Y=t,Me=i,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)qi(t);this.deps=this.depsTail=void 0,fs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?mi.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){_i(this)&&this.run()}get dirty(){return _i(this)}}let ks=0,wt,At;function Ks(e,t=!1){if(e.flags|=8,t){e.next=At,At=e;return}e.next=wt,wt=e}function Gi(){ks++}function Ji(){if(--ks>0)return;if(At){let t=At;for(At=void 0;t;){const i=t.next;t.next=void 0,t.flags&=-9,t=i}}let e;for(;wt;){let t=wt;for(wt=void 0;t;){const i=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=i}}if(e)throw e}function Gs(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Js(e){let t,i=e.depsTail,s=i;for(;s;){const n=s.prevDep;s.version===-1?(s===i&&(i=n),qi(s),oo(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=n}e.deps=t,e.depsTail=i}function _i(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(qs(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function qs(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===It))return;e.globalVersion=It;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!_i(e)){e.flags&=-3;return}const i=Y,s=Me;Y=e,Me=!0;try{Gs(e);const n=e.fn(e._value);(t.version===0||Ye(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{Y=i,Me=s,Js(e),e.flags&=-3}}function qi(e,t=!1){const{dep:i,prevSub:s,nextSub:n}=e;if(s&&(s.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=s,e.nextSub=void 0),i.subs===e&&(i.subs=s,!s&&i.computed)){i.computed.flags&=-5;for(let o=i.computed.deps;o;o=o.nextDep)qi(o,!0)}!t&&!--i.sc&&i.map&&i.map.delete(i.key)}function oo(e){const{prevDep:t,nextDep:i}=e;t&&(t.nextDep=i,e.prevDep=void 0),i&&(i.prevDep=t,e.nextDep=void 0)}let Me=!0;const Zs=[];function Ge(){Zs.push(Me),Me=!1}function Je(){const e=Zs.pop();Me=e===void 0?!0:e}function fs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const i=Y;Y=void 0;try{t()}finally{Y=i}}}let It=0;class ro{constructor(t,i){this.sub=t,this.dep=i,this.version=i.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Zi{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!Y||!Me||Y===this.computed)return;let i=this.activeLink;if(i===void 0||i.sub!==Y)i=this.activeLink=new ro(Y,this),Y.deps?(i.prevDep=Y.depsTail,Y.depsTail.nextDep=i,Y.depsTail=i):Y.deps=Y.depsTail=i,zs(i);else if(i.version===-1&&(i.version=this.version,i.nextDep)){const s=i.nextDep;s.prevDep=i.prevDep,i.prevDep&&(i.prevDep.nextDep=s),i.prevDep=Y.depsTail,i.nextDep=void 0,Y.depsTail.nextDep=i,Y.depsTail=i,Y.deps===i&&(Y.deps=s)}return i}trigger(t){this.version++,It++,this.notify(t)}notify(t){Gi();try{for(let i=this.subs;i;i=i.prevSub)i.sub.notify()&&i.sub.dep.notify()}finally{Ji()}}}function zs(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)zs(s)}const i=e.dep.subs;i!==e&&(e.prevSub=i,i&&(i.nextSub=e)),e.dep.subs=e}}const Ii=new WeakMap,st=Symbol(""),Li=Symbol(""),Lt=Symbol("");function oe(e,t,i){if(Me&&Y){let s=Ii.get(e);s||Ii.set(e,s=new Map);let n=s.get(i);n||(s.set(i,n=new Zi),n.map=s,n.key=i),n.track()}}function We(e,t,i,s,n,o){const r=Ii.get(e);if(!r){It++;return}const l=a=>{a&&a.trigger()};if(Gi(),t==="clear")r.forEach(l);else{const a=D(e),m=a&&Yi(i);if(a&&i==="length"){const d=Number(s);r.forEach((h,_)=>{(_==="length"||_===Lt||!ke(_)&&_>=d)&&l(h)})}else switch((i!==void 0||r.has(void 0))&&l(r.get(i)),m&&l(r.get(Lt)),t){case"add":a?m&&l(r.get("length")):(l(r.get(st)),bt(e)&&l(r.get(Li)));break;case"delete":a||(l(r.get(st)),bt(e)&&l(r.get(Li)));break;case"set":bt(e)&&l(r.get(st));break}}Ji()}function ct(e){const t=B(e);return t===e?t:(oe(t,"iterate",Lt),Te(e)?t:t.map(ce))}function zi(e){return oe(e=B(e),"iterate",Lt),e}const lo={__proto__:null,[Symbol.iterator](){return Ci(this,Symbol.iterator,ce)},concat(...e){return ct(this).concat(...e.map(t=>D(t)?ct(t):t))},entries(){return Ci(this,"entries",e=>(e[1]=ce(e[1]),e))},every(e,t){return He(this,"every",e,t,void 0,arguments)},filter(e,t){return He(this,"filter",e,t,i=>i.map(ce),arguments)},find(e,t){return He(this,"find",e,t,ce,arguments)},findIndex(e,t){return He(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return He(this,"findLast",e,t,ce,arguments)},findLastIndex(e,t){return He(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return He(this,"forEach",e,t,void 0,arguments)},includes(...e){return vi(this,"includes",e)},indexOf(...e){return vi(this,"indexOf",e)},join(e){return ct(this).join(e)},lastIndexOf(...e){return vi(this,"lastIndexOf",e)},map(e,t){return He(this,"map",e,t,void 0,arguments)},pop(){return vt(this,"pop")},push(...e){return vt(this,"push",e)},reduce(e,...t){return hs(this,"reduce",e,t)},reduceRight(e,...t){return hs(this,"reduceRight",e,t)},shift(){return vt(this,"shift")},some(e,t){return He(this,"some",e,t,void 0,arguments)},splice(...e){return vt(this,"splice",e)},toReversed(){return ct(this).toReversed()},toSorted(e){return ct(this).toSorted(e)},toSpliced(...e){return ct(this).toSpliced(...e)},unshift(...e){return vt(this,"unshift",e)},values(){return Ci(this,"values",ce)}};function Ci(e,t,i){const s=zi(e),n=s[t]();return s!==e&&!Te(e)&&(n._next=n.next,n.next=()=>{const o=n._next();return o.value&&(o.value=i(o.value)),o}),n}const co=Array.prototype;function He(e,t,i,s,n,o){const r=zi(e),l=r!==e&&!Te(e),a=r[t];if(a!==co[t]){const h=a.apply(e,o);return l?ce(h):h}let m=i;r!==e&&(l?m=function(h,_){return i.call(this,ce(h),_,e)}:i.length>2&&(m=function(h,_){return i.call(this,h,_,e)}));const d=a.call(r,m,s);return l&&n?n(d):d}function hs(e,t,i,s){const n=zi(e);let o=i;return n!==e&&(Te(e)?i.length>3&&(o=function(r,l,a){return i.call(this,r,l,a,e)}):o=function(r,l,a){return i.call(this,r,ce(l),a,e)}),n[t](o,...s)}function vi(e,t,i){const s=B(e);oe(s,"iterate",Lt);const n=s[t](...i);return(n===-1||n===!1)&&ts(i[0])?(i[0]=B(i[0]),s[t](...i)):n}function vt(e,t,i=[]){Ge(),Gi();const s=B(e)[t].apply(e,i);return Ji(),Je(),s}const ao=Qi("__proto__,__v_isRef,__isVue"),Xs=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ke));function uo(e){ke(e)||(e=String(e));const t=B(this);return oe(t,"has",e),t.hasOwnProperty(e)}class $s{constructor(t=!1,i=!1){this._isReadonly=t,this._isShallow=i}get(t,i,s){const n=this._isReadonly,o=this._isShallow;if(i==="__v_isReactive")return!n;if(i==="__v_isReadonly")return n;if(i==="__v_isShallow")return o;if(i==="__v_raw")return s===(n?o?bo:nn:o?sn:tn).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const r=D(t);if(!n){let a;if(r&&(a=lo[i]))return a;if(i==="hasOwnProperty")return uo}const l=Reflect.get(t,i,re(t)?t:s);return(ke(i)?Xs.has(i):ao(i))||(n||oe(t,"get",i),o)?l:re(l)?r&&Yi(i)?l:l.value:q(l)?n?on(l):$i(l):l}}class en extends $s{constructor(t=!1){super(!1,t)}set(t,i,s,n){let o=t[i];if(!this._isShallow){const a=nt(o);if(!Te(s)&&!nt(s)&&(o=B(o),s=B(s)),!D(t)&&re(o)&&!re(s))return a?!1:(o.value=s,!0)}const r=D(t)&&Yi(i)?Number(i)<t.length:W(t,i),l=Reflect.set(t,i,s,re(t)?t:n);return t===B(n)&&(r?Ye(s,o)&&We(t,"set",i,s):We(t,"add",i,s)),l}deleteProperty(t,i){const s=W(t,i);t[i];const n=Reflect.deleteProperty(t,i);return n&&s&&We(t,"delete",i,void 0),n}has(t,i){const s=Reflect.has(t,i);return(!ke(i)||!Xs.has(i))&&oe(t,"has",i),s}ownKeys(t){return oe(t,"iterate",D(t)?"length":st),Reflect.ownKeys(t)}}class fo extends $s{constructor(t=!1){super(!0,t)}set(t,i){return!0}deleteProperty(t,i){return!0}}const ho=new en,po=new fo,go=new en(!0);const Oi=e=>e,Ut=e=>Reflect.getPrototypeOf(e);function mo(e,t,i){return function(...s){const n=this.__v_raw,o=B(n),r=bt(o),l=e==="entries"||e===Symbol.iterator&&r,a=e==="keys"&&r,m=n[e](...s),d=i?Oi:t?Pi:ce;return!t&&oe(o,"iterate",a?Li:st),{next(){const{value:h,done:_}=m.next();return _?{value:h,done:_}:{value:l?[d(h[0]),d(h[1])]:d(h),done:_}},[Symbol.iterator](){return this}}}}function jt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Co(e,t){const i={get(n){const o=this.__v_raw,r=B(o),l=B(n);e||(Ye(n,l)&&oe(r,"get",n),oe(r,"get",l));const{has:a}=Ut(r),m=t?Oi:e?Pi:ce;if(a.call(r,n))return m(o.get(n));if(a.call(r,l))return m(o.get(l));o!==r&&o.get(n)},get size(){const n=this.__v_raw;return!e&&oe(B(n),"iterate",st),Reflect.get(n,"size",n)},has(n){const o=this.__v_raw,r=B(o),l=B(n);return e||(Ye(n,l)&&oe(r,"has",n),oe(r,"has",l)),n===l?o.has(n):o.has(n)||o.has(l)},forEach(n,o){const r=this,l=r.__v_raw,a=B(l),m=t?Oi:e?Pi:ce;return!e&&oe(a,"iterate",st),l.forEach((d,h)=>n.call(o,m(d),m(h),r))}};return se(i,e?{add:jt("add"),set:jt("set"),delete:jt("delete"),clear:jt("clear")}:{add(n){!t&&!Te(n)&&!nt(n)&&(n=B(n));const o=B(this);return Ut(o).has.call(o,n)||(o.add(n),We(o,"add",n,n)),this},set(n,o){!t&&!Te(o)&&!nt(o)&&(o=B(o));const r=B(this),{has:l,get:a}=Ut(r);let m=l.call(r,n);m||(n=B(n),m=l.call(r,n));const d=a.call(r,n);return r.set(n,o),m?Ye(o,d)&&We(r,"set",n,o):We(r,"add",n,o),this},delete(n){const o=B(this),{has:r,get:l}=Ut(o);let a=r.call(o,n);a||(n=B(n),a=r.call(o,n)),l&&l.call(o,n);const m=o.delete(n);return a&&We(o,"delete",n,void 0),m},clear(){const n=B(this),o=n.size!==0,r=n.clear();return o&&We(n,"clear",void 0,void 0),r}}),["keys","values","entries",Symbol.iterator].forEach(n=>{i[n]=mo(n,e,t)}),i}function Xi(e,t){const i=Co(e,t);return(s,n,o)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?s:Reflect.get(W(i,n)&&n in s?i:s,n,o)}const vo={get:Xi(!1,!1)},yo={get:Xi(!1,!0)},Eo={get:Xi(!0,!1)};const tn=new WeakMap,sn=new WeakMap,nn=new WeakMap,bo=new WeakMap;function xo(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function wo(e){return e.__v_skip||!Object.isExtensible(e)?0:xo(kn(e))}function $i(e){return nt(e)?e:es(e,!1,ho,vo,tn)}function Ao(e){return es(e,!1,go,yo,sn)}function on(e){return es(e,!0,po,Eo,nn)}function es(e,t,i,s,n){if(!q(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=n.get(e);if(o)return o;const r=wo(e);if(r===0)return e;const l=new Proxy(e,r===2?s:i);return n.set(e,l),l}function Mt(e){return nt(e)?Mt(e.__v_raw):!!(e&&e.__v_isReactive)}function nt(e){return!!(e&&e.__v_isReadonly)}function Te(e){return!!(e&&e.__v_isShallow)}function ts(e){return e?!!e.__v_raw:!1}function B(e){const t=e&&e.__v_raw;return t?B(t):e}function Mo(e){return!W(e,"__v_skip")&&Object.isExtensible(e)&&js(e,"__v_skip",!0),e}const ce=e=>q(e)?$i(e):e,Pi=e=>q(e)?on(e):e;function re(e){return e?e.__v_isRef===!0:!1}function ne(e){return To(e,!1)}function To(e,t){return re(e)?e:new So(e,t)}class So{constructor(t,i){this.dep=new Zi,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=i?t:B(t),this._value=i?t:ce(t),this.__v_isShallow=i}get value(){return this.dep.track(),this._value}set value(t){const i=this._rawValue,s=this.__v_isShallow||Te(t)||nt(t);t=s?t:B(t),Ye(t,i)&&(this._rawValue=t,this._value=s?t:ce(t),this.dep.trigger())}}function _o(e){return re(e)?e.value:e}const Io={get:(e,t,i)=>t==="__v_raw"?e:_o(Reflect.get(e,t,i)),set:(e,t,i,s)=>{const n=e[t];return re(n)&&!re(i)?(n.value=i,!0):Reflect.set(e,t,i,s)}};function rn(e){return Mt(e)?e:new Proxy(e,Io)}class Lo{constructor(t,i,s){this.fn=t,this.setter=i,this._value=void 0,this.dep=new Zi(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=It-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!i,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&Y!==this)return Ks(this,!0),!0}get value(){const t=this.dep.track();return qs(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Oo(e,t,i=!1){let s,n;return R(e)?s=e:(s=e.get,n=e.set),new Lo(s,n,i)}const Nt={},zt=new WeakMap;let tt;function Po(e,t=!1,i=tt){if(i){let s=zt.get(i);s||zt.set(i,s=[]),s.push(e)}}function Do(e,t,i=N){const{immediate:s,deep:n,once:o,scheduler:r,augmentJob:l,call:a}=i,m=O=>n?O:Te(O)||n===!1||n===0?Qe(O,1):Qe(O);let d,h,_,L,H=!1,F=!1;if(re(e)?(h=()=>e.value,H=Te(e)):Mt(e)?(h=()=>m(e),H=!0):D(e)?(F=!0,H=e.some(O=>Mt(O)||Te(O)),h=()=>e.map(O=>{if(re(O))return O.value;if(Mt(O))return m(O);if(R(O))return a?a(O,2):O()})):R(e)?t?h=a?()=>a(e,2):e:h=()=>{if(_){Ge();try{_()}finally{Je()}}const O=tt;tt=d;try{return a?a(e,3,[L]):e(L)}finally{tt=O}}:h=De,t&&n){const O=h,J=n===!0?1/0:n;h=()=>Qe(O(),J)}const te=no(),Q=()=>{d.stop(),te&&ji(te.effects,d)};if(o&&t){const O=t;t=(...J)=>{O(...J),Q()}}let K=F?new Array(e.length).fill(Nt):Nt;const U=O=>{if(!(!(d.flags&1)||!d.dirty&&!O))if(t){const J=d.run();if(n||H||(F?J.some((Se,fe)=>Ye(Se,K[fe])):Ye(J,K))){_&&_();const Se=tt;tt=d;try{const fe=[J,K===Nt?void 0:F&&K[0]===Nt?[]:K,L];a?a(t,3,fe):t(...fe),K=J}finally{tt=Se}}}else d.run()};return l&&l(U),d=new Ys(h),d.scheduler=r?()=>r(U,!1):U,L=O=>Po(O,!1,d),_=d.onStop=()=>{const O=zt.get(d);if(O){if(a)a(O,4);else for(const J of O)J();zt.delete(d)}},t?s?U(!0):K=d.run():r?r(U.bind(null,!0),!0):d.run(),Q.pause=d.pause.bind(d),Q.resume=d.resume.bind(d),Q.stop=Q,Q}function Qe(e,t=1/0,i){if(t<=0||!q(e)||e.__v_skip||(i=i||new Set,i.has(e)))return e;if(i.add(e),t--,re(e))Qe(e.value,t,i);else if(D(e))for(let s=0;s<e.length;s++)Qe(e[s],t,i);else if(Ni(e)||bt(e))e.forEach(s=>{Qe(s,t,i)});else if(Kn(e)){for(const s in e)Qe(e[s],t,i);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&Qe(e[s],t,i)}return e}/**
* @vue/runtime-core v3.5.12
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ft(e,t,i,s){try{return s?e(...s):e()}catch(n){ci(n,t,i)}}function Fe(e,t,i,s){if(R(e)){const n=Ft(e,t,i,s);return n&&Qs(n)&&n.catch(o=>{ci(o,t,i)}),n}if(D(e)){const n=[];for(let o=0;o<e.length;o++)n.push(Fe(e[o],t,i,s));return n}}function ci(e,t,i,s=!0){const n=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:r}=t&&t.appContext.config||N;if(t){let l=t.parent;const a=t.proxy,m=`https://vuejs.org/error-reference/#runtime-${i}`;for(;l;){const d=l.ec;if(d){for(let h=0;h<d.length;h++)if(d[h](e,a,m)===!1)return}l=l.parent}if(o){Ge(),Ft(o,null,10,[e,a,m]),Je();return}}Ro(e,i,n,s,r)}function Ro(e,t,i,s=!0,n=!1){if(n)throw e;console.error(e)}const ae=[];let Oe=-1;const ft=[];let je=null,at=0;const ln=Promise.resolve();let Xt=null;function cn(e){const t=Xt||ln;return e?t.then(this?e.bind(this):e):t}function Fo(e){let t=Oe+1,i=ae.length;for(;t<i;){const s=t+i>>>1,n=ae[s],o=Ot(n);o<e||o===e&&n.flags&2?t=s+1:i=s}return t}function is(e){if(!(e.flags&1)){const t=Ot(e),i=ae[ae.length-1];!i||!(e.flags&2)&&t>=Ot(i)?ae.push(e):ae.splice(Fo(t),0,e),e.flags|=1,an()}}function an(){Xt||(Xt=ln.then(fn))}function Ho(e){D(e)?ft.push(...e):je&&e.id===-1?je.splice(at+1,0,e):e.flags&1||(ft.push(e),e.flags|=1),an()}function ds(e,t,i=Oe+1){for(;i<ae.length;i++){const s=ae[i];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;ae.splice(i,1),i--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function un(e){if(ft.length){const t=[...new Set(ft)].sort((i,s)=>Ot(i)-Ot(s));if(ft.length=0,je){je.push(...t);return}for(je=t,at=0;at<je.length;at++){const i=je[at];i.flags&4&&(i.flags&=-2),i.flags&8||i(),i.flags&=-2}je=null,at=0}}const Ot=e=>e.id==null?e.flags&2?-1:1/0:e.id;function fn(e){try{for(Oe=0;Oe<ae.length;Oe++){const t=ae[Oe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ft(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Oe<ae.length;Oe++){const t=ae[Oe];t&&(t.flags&=-2)}Oe=-1,ae.length=0,un(),Xt=null,(ae.length||ft.length)&&fn()}}let be=null,hn=null;function $t(e){const t=be;return be=e,hn=e&&e.type.__scopeId||null,t}function Vo(e,t=be,i){if(!t||e._n)return e;const s=(...n)=>{s._d&&bs(-1);const o=$t(t);let r;try{r=e(...n)}finally{$t(o),s._d&&bs(1)}return r};return s._n=!0,s._c=!0,s._d=!0,s}function yi(e,t){if(be===null)return e;const i=hi(be),s=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[o,r,l,a=N]=t[n];o&&(R(o)&&(o={mounted:o,updated:o}),o.deep&&Qe(r),s.push({dir:o,instance:i,value:r,oldValue:void 0,arg:l,modifiers:a}))}return e}function $e(e,t,i,s){const n=e.dirs,o=t&&t.dirs;for(let r=0;r<n.length;r++){const l=n[r];o&&(l.oldValue=o[r].value);let a=l.dir[s];a&&(Ge(),Fe(a,i,8,[e.el,l,e,t]),Je())}}const Bo=Symbol("_vte"),Wo=e=>e.__isTeleport;function ss(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ss(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function dn(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Di(e,t,i,s,n=!1){if(D(e)){e.forEach((H,F)=>Di(H,t&&(D(t)?t[F]:t),i,s,n));return}if(Tt(s)&&!n)return;const o=s.shapeFlag&4?hi(s.component):s.el,r=n?null:o,{i:l,r:a}=e,m=t&&t.r,d=l.refs===N?l.refs={}:l.refs,h=l.setupState,_=B(h),L=h===N?()=>!1:H=>W(_,H);if(m!=null&&m!==a&&(ee(m)?(d[m]=null,L(m)&&(h[m]=null)):re(m)&&(m.value=null)),R(a))Ft(a,l,12,[r,d]);else{const H=ee(a),F=re(a);if(H||F){const te=()=>{if(e.f){const Q=H?L(a)?h[a]:d[a]:a.value;n?D(Q)&&ji(Q,o):D(Q)?Q.includes(o)||Q.push(o):H?(d[a]=[o],L(a)&&(h[a]=d[a])):(a.value=[o],e.k&&(d[e.k]=a.value))}else H?(d[a]=r,L(a)&&(h[a]=r)):F&&(a.value=r,e.k&&(d[e.k]=r))};r?(te.id=-1,me(te,i)):te()}}}ri().requestIdleCallback;ri().cancelIdleCallback;const Tt=e=>!!e.type.__asyncLoader,pn=e=>e.type.__isKeepAlive;function Qo(e,t){gn(e,"a",t)}function Uo(e,t){gn(e,"da",t)}function gn(e,t,i=ue){const s=e.__wdc||(e.__wdc=()=>{let n=i;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(ai(t,s,i),i){let n=i.parent;for(;n&&n.parent;)pn(n.parent.vnode)&&jo(s,t,i,n),n=n.parent}}function jo(e,t,i,s){const n=ai(t,e,s,!0);vn(()=>{ji(s[t],n)},i)}function ai(e,t,i=ue,s=!1){if(i){const n=i[e]||(i[e]=[]),o=t.__weh||(t.__weh=(...r)=>{Ge();const l=Ht(i),a=Fe(t,i,e,r);return l(),Je(),a});return s?n.unshift(o):n.push(o),o}}const Ue=e=>(t,i=ue)=>{(!Dt||e==="sp")&&ai(e,(...s)=>t(...s),i)},No=Ue("bm"),mn=Ue("m"),Yo=Ue("bu"),ko=Ue("u"),Cn=Ue("bum"),vn=Ue("um"),Ko=Ue("sp"),Go=Ue("rtg"),Jo=Ue("rtc");function qo(e,t=ue){ai("ec",e,t)}const Zo=Symbol.for("v-ndc"),Ri=e=>e?Wn(e)?hi(e):Ri(e.parent):null,St=se(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ri(e.parent),$root:e=>Ri(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ns(e),$forceUpdate:e=>e.f||(e.f=()=>{is(e.update)}),$nextTick:e=>e.n||(e.n=cn.bind(e.proxy)),$watch:e=>vr.bind(e)}),Ei=(e,t)=>e!==N&&!e.__isScriptSetup&&W(e,t),zo={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:i,setupState:s,data:n,props:o,accessCache:r,type:l,appContext:a}=e;let m;if(t[0]!=="$"){const L=r[t];if(L!==void 0)switch(L){case 1:return s[t];case 2:return n[t];case 4:return i[t];case 3:return o[t]}else{if(Ei(s,t))return r[t]=1,s[t];if(n!==N&&W(n,t))return r[t]=2,n[t];if((m=e.propsOptions[0])&&W(m,t))return r[t]=3,o[t];if(i!==N&&W(i,t))return r[t]=4,i[t];Fi&&(r[t]=0)}}const d=St[t];let h,_;if(d)return t==="$attrs"&&oe(e.attrs,"get",""),d(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(i!==N&&W(i,t))return r[t]=4,i[t];if(_=a.config.globalProperties,W(_,t))return _[t]},set({_:e},t,i){const{data:s,setupState:n,ctx:o}=e;return Ei(n,t)?(n[t]=i,!0):s!==N&&W(s,t)?(s[t]=i,!0):W(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=i,!0)},has({_:{data:e,setupState:t,accessCache:i,ctx:s,appContext:n,propsOptions:o}},r){let l;return!!i[r]||e!==N&&W(e,r)||Ei(t,r)||(l=o[0])&&W(l,r)||W(s,r)||W(St,r)||W(n.config.globalProperties,r)},defineProperty(e,t,i){return i.get!=null?e._.accessCache[t]=0:W(i,"value")&&this.set(e,t,i.value,null),Reflect.defineProperty(e,t,i)}};function ps(e){return D(e)?e.reduce((t,i)=>(t[i]=null,t),{}):e}let Fi=!0;function Xo(e){const t=ns(e),i=e.proxy,s=e.ctx;Fi=!1,t.beforeCreate&&gs(t.beforeCreate,e,"bc");const{data:n,computed:o,methods:r,watch:l,provide:a,inject:m,created:d,beforeMount:h,mounted:_,beforeUpdate:L,updated:H,activated:F,deactivated:te,beforeDestroy:Q,beforeUnmount:K,destroyed:U,unmounted:O,render:J,renderTracked:Se,renderTriggered:fe,errorCaptured:ye,serverPrefetch:xe,expose:z,inheritAttrs:he,components:de,directives:pe,filters:we}=t;if(m&&$o(m,s,null),r)for(const k in r){const j=r[k];R(j)&&(s[k]=j.bind(i))}if(n){const k=n.call(i,i);q(k)&&(e.data=$i(k))}if(Fi=!0,o)for(const k in o){const j=o[k],_e=R(j)?j.bind(i,i):R(j.get)?j.get.bind(i,i):De,lt=!R(j)&&R(j.set)?j.set.bind(i):De,Ae=Ur({get:_e,set:lt});Object.defineProperty(s,k,{enumerable:!0,configurable:!0,get:()=>Ae.value,set:Ee=>Ae.value=Ee})}if(l)for(const k in l)yn(l[k],s,i,k);if(a){const k=R(a)?a.call(i):a;Reflect.ownKeys(k).forEach(j=>{or(j,k[j])})}d&&gs(d,e,"c");function Z(k,j){D(j)?j.forEach(_e=>k(_e.bind(i))):j&&k(j.bind(i))}if(Z(No,h),Z(mn,_),Z(Yo,L),Z(ko,H),Z(Qo,F),Z(Uo,te),Z(qo,ye),Z(Jo,Se),Z(Go,fe),Z(Cn,K),Z(vn,O),Z(Ko,xe),D(z))if(z.length){const k=e.exposed||(e.exposed={});z.forEach(j=>{Object.defineProperty(k,j,{get:()=>i[j],set:_e=>i[j]=_e})})}else e.exposed||(e.exposed={});J&&e.render===De&&(e.render=J),he!=null&&(e.inheritAttrs=he),de&&(e.components=de),pe&&(e.directives=pe),xe&&dn(e)}function $o(e,t,i=De){D(e)&&(e=Hi(e));for(const s in e){const n=e[s];let o;q(n)?"default"in n?o=kt(n.from||s,n.default,!0):o=kt(n.from||s):o=kt(n),re(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:r=>o.value=r}):t[s]=o}}function gs(e,t,i){Fe(D(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,i)}function yn(e,t,i,s){let n=s.includes(".")?Dn(i,s):()=>i[s];if(ee(e)){const o=t[e];R(o)&&xi(n,o)}else if(R(e))xi(n,e.bind(i));else if(q(e))if(D(e))e.forEach(o=>yn(o,t,i,s));else{const o=R(e.handler)?e.handler.bind(i):t[e.handler];R(o)&&xi(n,o,e)}}function ns(e){const t=e.type,{mixins:i,extends:s}=t,{mixins:n,optionsCache:o,config:{optionMergeStrategies:r}}=e.appContext,l=o.get(t);let a;return l?a=l:!n.length&&!i&&!s?a=t:(a={},n.length&&n.forEach(m=>ei(a,m,r,!0)),ei(a,t,r)),q(t)&&o.set(t,a),a}function ei(e,t,i,s=!1){const{mixins:n,extends:o}=t;o&&ei(e,o,i,!0),n&&n.forEach(r=>ei(e,r,i,!0));for(const r in t)if(!(s&&r==="expose")){const l=er[r]||i&&i[r];e[r]=l?l(e[r],t[r]):t[r]}return e}const er={data:ms,props:Cs,emits:Cs,methods:Et,computed:Et,beforeCreate:le,created:le,beforeMount:le,mounted:le,beforeUpdate:le,updated:le,beforeDestroy:le,beforeUnmount:le,destroyed:le,unmounted:le,activated:le,deactivated:le,errorCaptured:le,serverPrefetch:le,components:Et,directives:Et,watch:ir,provide:ms,inject:tr};function ms(e,t){return t?e?function(){return se(R(e)?e.call(this,this):e,R(t)?t.call(this,this):t)}:t:e}function tr(e,t){return Et(Hi(e),Hi(t))}function Hi(e){if(D(e)){const t={};for(let i=0;i<e.length;i++)t[e[i]]=e[i];return t}return e}function le(e,t){return e?[...new Set([].concat(e,t))]:t}function Et(e,t){return e?se(Object.create(null),e,t):t}function Cs(e,t){return e?D(e)&&D(t)?[...new Set([...e,...t])]:se(Object.create(null),ps(e),ps(t??{})):t}function ir(e,t){if(!e)return t;if(!t)return e;const i=se(Object.create(null),e);for(const s in t)i[s]=le(e[s],t[s]);return i}function En(){return{app:null,config:{isNativeTag:jn,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let sr=0;function nr(e,t){return function(s,n=null){R(s)||(s=se({},s)),n!=null&&!q(n)&&(n=null);const o=En(),r=new WeakSet,l=[];let a=!1;const m=o.app={_uid:sr++,_component:s,_props:n,_container:null,_context:o,_instance:null,version:jr,get config(){return o.config},set config(d){},use(d,...h){return r.has(d)||(d&&R(d.install)?(r.add(d),d.install(m,...h)):R(d)&&(r.add(d),d(m,...h))),m},mixin(d){return o.mixins.includes(d)||o.mixins.push(d),m},component(d,h){return h?(o.components[d]=h,m):o.components[d]},directive(d,h){return h?(o.directives[d]=h,m):o.directives[d]},mount(d,h,_){if(!a){const L=m._ceVNode||Re(s,n);return L.appContext=o,_===!0?_="svg":_===!1&&(_=void 0),h&&t?t(L,d):e(L,d,_),a=!0,m._container=d,d.__vue_app__=m,hi(L.component)}},onUnmount(d){l.push(d)},unmount(){a&&(Fe(l,m._instance,16),e(null,m._container),delete m._container.__vue_app__)},provide(d,h){return o.provides[d]=h,m},runWithContext(d){const h=ht;ht=m;try{return d()}finally{ht=h}}};return m}}let ht=null;function or(e,t){if(ue){let i=ue.provides;const s=ue.parent&&ue.parent.provides;s===i&&(i=ue.provides=Object.create(s)),i[e]=t}}function kt(e,t,i=!1){const s=ue||be;if(s||ht){const n=ht?ht._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return i&&R(t)?t.call(s&&s.proxy):t}}const bn={},xn=()=>Object.create(bn),wn=e=>Object.getPrototypeOf(e)===bn;function rr(e,t,i,s=!1){const n={},o=xn();e.propsDefaults=Object.create(null),An(e,t,n,o);for(const r in e.propsOptions[0])r in n||(n[r]=void 0);i?e.props=s?n:Ao(n):e.type.props?e.props=n:e.props=o,e.attrs=o}function lr(e,t,i,s){const{props:n,attrs:o,vnode:{patchFlag:r}}=e,l=B(n),[a]=e.propsOptions;let m=!1;if((s||r>0)&&!(r&16)){if(r&8){const d=e.vnode.dynamicProps;for(let h=0;h<d.length;h++){let _=d[h];if(ui(e.emitsOptions,_))continue;const L=t[_];if(a)if(W(o,_))L!==o[_]&&(o[_]=L,m=!0);else{const H=Ke(_);n[H]=Vi(a,l,H,L,e,!1)}else L!==o[_]&&(o[_]=L,m=!0)}}}else{An(e,t,n,o)&&(m=!0);let d;for(const h in l)(!t||!W(t,h)&&((d=rt(h))===h||!W(t,d)))&&(a?i&&(i[h]!==void 0||i[d]!==void 0)&&(n[h]=Vi(a,l,h,void 0,e,!0)):delete n[h]);if(o!==l)for(const h in o)(!t||!W(t,h))&&(delete o[h],m=!0)}m&&We(e.attrs,"set","")}function An(e,t,i,s){const[n,o]=e.propsOptions;let r=!1,l;if(t)for(let a in t){if(xt(a))continue;const m=t[a];let d;n&&W(n,d=Ke(a))?!o||!o.includes(d)?i[d]=m:(l||(l={}))[d]=m:ui(e.emitsOptions,a)||(!(a in s)||m!==s[a])&&(s[a]=m,r=!0)}if(o){const a=B(i),m=l||N;for(let d=0;d<o.length;d++){const h=o[d];i[h]=Vi(n,a,h,m[h],e,!W(m,h))}}return r}function Vi(e,t,i,s,n,o){const r=e[i];if(r!=null){const l=W(r,"default");if(l&&s===void 0){const a=r.default;if(r.type!==Function&&!r.skipFactory&&R(a)){const{propsDefaults:m}=n;if(i in m)s=m[i];else{const d=Ht(n);s=m[i]=a.call(null,t),d()}}else s=a;n.ce&&n.ce._setProp(i,s)}r[0]&&(o&&!l?s=!1:r[1]&&(s===""||s===rt(i))&&(s=!0))}return s}const cr=new WeakMap;function Mn(e,t,i=!1){const s=i?cr:t.propsCache,n=s.get(e);if(n)return n;const o=e.props,r={},l=[];let a=!1;if(!R(e)){const d=h=>{a=!0;const[_,L]=Mn(h,t,!0);se(r,_),L&&l.push(...L)};!i&&t.mixins.length&&t.mixins.forEach(d),e.extends&&d(e.extends),e.mixins&&e.mixins.forEach(d)}if(!o&&!a)return q(e)&&s.set(e,ut),ut;if(D(o))for(let d=0;d<o.length;d++){const h=Ke(o[d]);vs(h)&&(r[h]=N)}else if(o)for(const d in o){const h=Ke(d);if(vs(h)){const _=o[d],L=r[h]=D(_)||R(_)?{type:_}:se({},_),H=L.type;let F=!1,te=!0;if(D(H))for(let Q=0;Q<H.length;++Q){const K=H[Q],U=R(K)&&K.name;if(U==="Boolean"){F=!0;break}else U==="String"&&(te=!1)}else F=R(H)&&H.name==="Boolean";L[0]=F,L[1]=te,(F||W(L,"default"))&&l.push(h)}}const m=[r,l];return q(e)&&s.set(e,m),m}function vs(e){return e[0]!=="$"&&!xt(e)}const Tn=e=>e[0]==="_"||e==="$stable",os=e=>D(e)?e.map(Pe):[Pe(e)],ar=(e,t,i)=>{if(t._n)return t;const s=Vo((...n)=>os(t(...n)),i);return s._c=!1,s},Sn=(e,t,i)=>{const s=e._ctx;for(const n in e){if(Tn(n))continue;const o=e[n];if(R(o))t[n]=ar(n,o,s);else if(o!=null){const r=os(o);t[n]=()=>r}}},_n=(e,t)=>{const i=os(t);e.slots.default=()=>i},In=(e,t,i)=>{for(const s in t)(i||s!=="_")&&(e[s]=t[s])},ur=(e,t,i)=>{const s=e.slots=xn();if(e.vnode.shapeFlag&32){const n=t._;n?(In(s,t,i),i&&js(s,"_",n,!0)):Sn(t,s)}else t&&_n(e,t)},fr=(e,t,i)=>{const{vnode:s,slots:n}=e;let o=!0,r=N;if(s.shapeFlag&32){const l=t._;l?i&&l===1?o=!1:In(n,t,i):(o=!t.$stable,Sn(t,n)),r=t}else t&&(_n(e,t),r={default:1});if(o)for(const l in n)!Tn(l)&&r[l]==null&&delete n[l]},me=Mr;function hr(e){return dr(e)}function dr(e,t){const i=ri();i.__VUE__=!0;const{insert:s,remove:n,patchProp:o,createElement:r,createText:l,createComment:a,setText:m,setElementText:d,parentNode:h,nextSibling:_,setScopeId:L=De,insertStaticContent:H}=e,F=(c,u,g,E=null,v=null,y=null,M=void 0,w=null,x=!!u.dynamicChildren)=>{if(c===u)return;c&&!yt(c,u)&&(E=Ze(c),Ee(c,v,y,!0),c=null),u.patchFlag===-2&&(x=!1,u.dynamicChildren=null);const{type:f,ref:C,shapeFlag:p}=u;switch(f){case fi:te(c,u,g,E);break;case ot:Q(c,u,g,E);break;case Kt:c==null&&K(u,g,E,M);break;case Be:de(c,u,g,E,v,y,M,w,x);break;default:p&1?J(c,u,g,E,v,y,M,w,x):p&6?pe(c,u,g,E,v,y,M,w,x):(p&64||p&128)&&f.process(c,u,g,E,v,y,M,w,x,ze)}C!=null&&v&&Di(C,c&&c.ref,y,u||c,!u)},te=(c,u,g,E)=>{if(c==null)s(u.el=l(u.children),g,E);else{const v=u.el=c.el;u.children!==c.children&&m(v,u.children)}},Q=(c,u,g,E)=>{c==null?s(u.el=a(u.children||""),g,E):u.el=c.el},K=(c,u,g,E)=>{[c.el,c.anchor]=H(c.children,u,g,E,c.el,c.anchor)},U=({el:c,anchor:u},g,E)=>{let v;for(;c&&c!==u;)v=_(c),s(c,g,E),c=v;s(u,g,E)},O=({el:c,anchor:u})=>{let g;for(;c&&c!==u;)g=_(c),n(c),c=g;n(u)},J=(c,u,g,E,v,y,M,w,x)=>{u.type==="svg"?M="svg":u.type==="math"&&(M="mathml"),c==null?Se(u,g,E,v,y,M,w,x):xe(c,u,v,y,M,w,x)},Se=(c,u,g,E,v,y,M,w)=>{let x,f;const{props:C,shapeFlag:p,transition:b,dirs:T}=c;if(x=c.el=r(c.type,y,C&&C.is,C),p&8?d(x,c.children):p&16&&ye(c.children,x,null,E,v,bi(c,y),M,w),T&&$e(c,null,E,"created"),fe(x,c,c.scopeId,M,E),C){for(const A in C)A!=="value"&&!xt(A)&&o(x,A,null,C[A],y,E);"value"in C&&o(x,"value",null,C.value,y),(f=C.onVnodeBeforeMount)&&Le(f,E,c)}T&&$e(c,null,E,"beforeMount");const S=pr(v,b);S&&b.beforeEnter(x),s(x,u,g),((f=C&&C.onVnodeMounted)||S||T)&&me(()=>{f&&Le(f,E,c),S&&b.enter(x),T&&$e(c,null,E,"mounted")},v)},fe=(c,u,g,E,v)=>{if(g&&L(c,g),E)for(let y=0;y<E.length;y++)L(c,E[y]);if(v){let y=v.subTree;if(u===y||Fn(y.type)&&(y.ssContent===u||y.ssFallback===u)){const M=v.vnode;fe(c,M,M.scopeId,M.slotScopeIds,v.parent)}}},ye=(c,u,g,E,v,y,M,w,x=0)=>{for(let f=x;f<c.length;f++){const C=c[f]=w?Ne(c[f]):Pe(c[f]);F(null,C,u,g,E,v,y,M,w)}},xe=(c,u,g,E,v,y,M)=>{const w=u.el=c.el;let{patchFlag:x,dynamicChildren:f,dirs:C}=u;x|=c.patchFlag&16;const p=c.props||N,b=u.props||N;let T;if(g&&et(g,!1),(T=b.onVnodeBeforeUpdate)&&Le(T,g,u,c),C&&$e(u,c,g,"beforeUpdate"),g&&et(g,!0),(p.innerHTML&&b.innerHTML==null||p.textContent&&b.textContent==null)&&d(w,""),f?z(c.dynamicChildren,f,w,g,E,bi(u,v),y):M||j(c,u,w,null,g,E,bi(u,v),y,!1),x>0){if(x&16)he(w,p,b,g,v);else if(x&2&&p.class!==b.class&&o(w,"class",null,b.class,v),x&4&&o(w,"style",p.style,b.style,v),x&8){const S=u.dynamicProps;for(let A=0;A<S.length;A++){const I=S[A],P=p[I],V=b[I];(V!==P||I==="value")&&o(w,I,P,V,v,g)}}x&1&&c.children!==u.children&&d(w,u.children)}else!M&&f==null&&he(w,p,b,g,v);((T=b.onVnodeUpdated)||C)&&me(()=>{T&&Le(T,g,u,c),C&&$e(u,c,g,"updated")},E)},z=(c,u,g,E,v,y,M)=>{for(let w=0;w<u.length;w++){const x=c[w],f=u[w],C=x.el&&(x.type===Be||!yt(x,f)||x.shapeFlag&70)?h(x.el):g;F(x,f,C,null,E,v,y,M,!0)}},he=(c,u,g,E,v)=>{if(u!==g){if(u!==N)for(const y in u)!xt(y)&&!(y in g)&&o(c,y,u[y],null,v,E);for(const y in g){if(xt(y))continue;const M=g[y],w=u[y];M!==w&&y!=="value"&&o(c,y,w,M,v,E)}"value"in g&&o(c,"value",u.value,g.value,v)}},de=(c,u,g,E,v,y,M,w,x)=>{const f=u.el=c?c.el:l(""),C=u.anchor=c?c.anchor:l("");let{patchFlag:p,dynamicChildren:b,slotScopeIds:T}=u;T&&(w=w?w.concat(T):T),c==null?(s(f,g,E),s(C,g,E),ye(u.children||[],g,C,v,y,M,w,x)):p>0&&p&64&&b&&c.dynamicChildren?(z(c.dynamicChildren,b,g,v,y,M,w),(u.key!=null||v&&u===v.subTree)&&Ln(c,u,!0)):j(c,u,g,C,v,y,M,w,x)},pe=(c,u,g,E,v,y,M,w,x)=>{u.slotScopeIds=w,c==null?u.shapeFlag&512?v.ctx.activate(u,g,E,M,x):we(u,g,E,v,y,M,x):gt(c,u,x)},we=(c,u,g,E,v,y,M)=>{const w=c.component=Fr(c,E,v);if(pn(c)&&(w.ctx.renderer=ze),Hr(w,!1,M),w.asyncDep){if(v&&v.registerDep(w,Z,M),!c.el){const x=w.subTree=Re(ot);Q(null,x,u,g)}}else Z(w,c,u,g,v,y,M)},gt=(c,u,g)=>{const E=u.component=c.component;if(wr(c,u,g))if(E.asyncDep&&!E.asyncResolved){k(E,u,g);return}else E.next=u,E.update();else u.el=c.el,E.vnode=u},Z=(c,u,g,E,v,y,M)=>{const w=()=>{if(c.isMounted){let{next:p,bu:b,u:T,parent:S,vnode:A}=c;{const X=On(c);if(X){p&&(p.el=A.el,k(c,p,M)),X.asyncDep.then(()=>{c.isUnmounted||w()});return}}let I=p,P;et(c,!1),p?(p.el=A.el,k(c,p,M)):p=A,b&&Yt(b),(P=p.props&&p.props.onVnodeBeforeUpdate)&&Le(P,S,p,A),et(c,!0);const V=wi(c),G=c.subTree;c.subTree=V,F(G,V,h(G.el),Ze(G),c,v,y),p.el=V.el,I===null&&Ar(c,V.el),T&&me(T,v),(P=p.props&&p.props.onVnodeUpdated)&&me(()=>Le(P,S,p,A),v)}else{let p;const{el:b,props:T}=u,{bm:S,m:A,parent:I,root:P,type:V}=c,G=Tt(u);if(et(c,!1),S&&Yt(S),!G&&(p=T&&T.onVnodeBeforeMount)&&Le(p,I,u),et(c,!0),b&&Qt){const X=()=>{c.subTree=wi(c),Qt(b,c.subTree,c,v,null)};G&&V.__asyncHydrate?V.__asyncHydrate(b,c,X):X()}else{P.ce&&P.ce._injectChildStyle(V);const X=c.subTree=wi(c);F(null,X,g,E,c,v,y),u.el=X.el}if(A&&me(A,v),!G&&(p=T&&T.onVnodeMounted)){const X=u;me(()=>Le(p,I,X),v)}(u.shapeFlag&256||I&&Tt(I.vnode)&&I.vnode.shapeFlag&256)&&c.a&&me(c.a,v),c.isMounted=!0,u=g=E=null}};c.scope.on();const x=c.effect=new Ys(w);c.scope.off();const f=c.update=x.run.bind(x),C=c.job=x.runIfDirty.bind(x);C.i=c,C.id=c.uid,x.scheduler=()=>is(C),et(c,!0),f()},k=(c,u,g)=>{u.component=c;const E=c.vnode.props;c.vnode=u,c.next=null,lr(c,u.props,E,g),fr(c,u.children,g),Ge(),ds(c),Je()},j=(c,u,g,E,v,y,M,w,x=!1)=>{const f=c&&c.children,C=c?c.shapeFlag:0,p=u.children,{patchFlag:b,shapeFlag:T}=u;if(b>0){if(b&128){lt(f,p,g,E,v,y,M,w,x);return}else if(b&256){_e(f,p,g,E,v,y,M,w,x);return}}T&8?(C&16&&qe(f,v,y),p!==f&&d(g,p)):C&16?T&16?lt(f,p,g,E,v,y,M,w,x):qe(f,v,y,!0):(C&8&&d(g,""),T&16&&ye(p,g,E,v,y,M,w,x))},_e=(c,u,g,E,v,y,M,w,x)=>{c=c||ut,u=u||ut;const f=c.length,C=u.length,p=Math.min(f,C);let b;for(b=0;b<p;b++){const T=u[b]=x?Ne(u[b]):Pe(u[b]);F(c[b],T,g,null,v,y,M,w,x)}f>C?qe(c,v,y,!0,!1,p):ye(u,g,E,v,y,M,w,x,p)},lt=(c,u,g,E,v,y,M,w,x)=>{let f=0;const C=u.length;let p=c.length-1,b=C-1;for(;f<=p&&f<=b;){const T=c[f],S=u[f]=x?Ne(u[f]):Pe(u[f]);if(yt(T,S))F(T,S,g,null,v,y,M,w,x);else break;f++}for(;f<=p&&f<=b;){const T=c[p],S=u[b]=x?Ne(u[b]):Pe(u[b]);if(yt(T,S))F(T,S,g,null,v,y,M,w,x);else break;p--,b--}if(f>p){if(f<=b){const T=b+1,S=T<C?u[T].el:E;for(;f<=b;)F(null,u[f]=x?Ne(u[f]):Pe(u[f]),g,S,v,y,M,w,x),f++}}else if(f>b)for(;f<=p;)Ee(c[f],v,y,!0),f++;else{const T=f,S=f,A=new Map;for(f=S;f<=b;f++){const ge=u[f]=x?Ne(u[f]):Pe(u[f]);ge.key!=null&&A.set(ge.key,f)}let I,P=0;const V=b-S+1;let G=!1,X=0;const Xe=new Array(V);for(f=0;f<V;f++)Xe[f]=0;for(f=T;f<=p;f++){const ge=c[f];if(P>=V){Ee(ge,v,y,!0);continue}let Ie;if(ge.key!=null)Ie=A.get(ge.key);else for(I=S;I<=b;I++)if(Xe[I-S]===0&&yt(ge,u[I])){Ie=I;break}Ie===void 0?Ee(ge,v,y,!0):(Xe[Ie-S]=f+1,Ie>=X?X=Ie:G=!0,F(ge,u[Ie],g,null,v,y,M,w,x),P++)}const ls=G?gr(Xe):ut;for(I=ls.length-1,f=V-1;f>=0;f--){const ge=S+f,Ie=u[ge],cs=ge+1<C?u[ge+1].el:E;Xe[f]===0?F(null,Ie,g,cs,v,y,M,w,x):G&&(I<0||f!==ls[I]?Ae(Ie,g,cs,2):I--)}}},Ae=(c,u,g,E,v=null)=>{const{el:y,type:M,transition:w,children:x,shapeFlag:f}=c;if(f&6){Ae(c.component.subTree,u,g,E);return}if(f&128){c.suspense.move(u,g,E);return}if(f&64){M.move(c,u,g,ze);return}if(M===Be){s(y,u,g);for(let p=0;p<x.length;p++)Ae(x[p],u,g,E);s(c.anchor,u,g);return}if(M===Kt){U(c,u,g);return}if(E!==2&&f&1&&w)if(E===0)w.beforeEnter(y),s(y,u,g),me(()=>w.enter(y),v);else{const{leave:p,delayLeave:b,afterLeave:T}=w,S=()=>s(y,u,g),A=()=>{p(y,()=>{S(),T&&T()})};b?b(y,S,A):A()}else s(y,u,g)},Ee=(c,u,g,E=!1,v=!1)=>{const{type:y,props:M,ref:w,children:x,dynamicChildren:f,shapeFlag:C,patchFlag:p,dirs:b,cacheIndex:T}=c;if(p===-2&&(v=!1),w!=null&&Di(w,null,g,c,!0),T!=null&&(u.renderCache[T]=void 0),C&256){u.ctx.deactivate(c);return}const S=C&1&&b,A=!Tt(c);let I;if(A&&(I=M&&M.onVnodeBeforeUnmount)&&Le(I,u,c),C&6)mt(c.component,g,E);else{if(C&128){c.suspense.unmount(g,E);return}S&&$e(c,null,u,"beforeUnmount"),C&64?c.type.remove(c,u,g,ze,E):f&&!f.hasOnce&&(y!==Be||p>0&&p&64)?qe(f,u,g,!1,!0):(y===Be&&p&384||!v&&C&16)&&qe(x,u,g),E&&Vt(c)}(A&&(I=M&&M.onVnodeUnmounted)||S)&&me(()=>{I&&Le(I,u,c),S&&$e(c,null,u,"unmounted")},g)},Vt=c=>{const{type:u,el:g,anchor:E,transition:v}=c;if(u===Be){pi(g,E);return}if(u===Kt){O(c);return}const y=()=>{n(g),v&&!v.persisted&&v.afterLeave&&v.afterLeave()};if(c.shapeFlag&1&&v&&!v.persisted){const{leave:M,delayLeave:w}=v,x=()=>M(g,y);w?w(c.el,y,x):x()}else y()},pi=(c,u)=>{let g;for(;c!==u;)g=_(c),n(c),c=g;n(u)},mt=(c,u,g)=>{const{bum:E,scope:v,job:y,subTree:M,um:w,m:x,a:f}=c;ys(x),ys(f),E&&Yt(E),v.stop(),y&&(y.flags|=8,Ee(M,c,u,g)),w&&me(w,u),me(()=>{c.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&c.asyncDep&&!c.asyncResolved&&c.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},qe=(c,u,g,E=!1,v=!1,y=0)=>{for(let M=y;M<c.length;M++)Ee(c[M],u,g,E,v)},Ze=c=>{if(c.shapeFlag&6)return Ze(c.component.subTree);if(c.shapeFlag&128)return c.suspense.next();const u=_(c.anchor||c.el),g=u&&u[Bo];return g?_(g):u};let Ct=!1;const Bt=(c,u,g)=>{c==null?u._vnode&&Ee(u._vnode,null,null,!0):F(u._vnode||null,c,u,null,null,null,g),u._vnode=c,Ct||(Ct=!0,ds(),un(),Ct=!1)},ze={p:F,um:Ee,m:Ae,r:Vt,mt:we,mc:ye,pc:j,pbc:z,n:Ze,o:e};let Wt,Qt;return{render:Bt,hydrate:Wt,createApp:nr(Bt,Wt)}}function bi({type:e,props:t},i){return i==="svg"&&e==="foreignObject"||i==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:i}function et({effect:e,job:t},i){i?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function pr(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ln(e,t,i=!1){const s=e.children,n=t.children;if(D(s)&&D(n))for(let o=0;o<s.length;o++){const r=s[o];let l=n[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=n[o]=Ne(n[o]),l.el=r.el),!i&&l.patchFlag!==-2&&Ln(r,l)),l.type===fi&&(l.el=r.el)}}function gr(e){const t=e.slice(),i=[0];let s,n,o,r,l;const a=e.length;for(s=0;s<a;s++){const m=e[s];if(m!==0){if(n=i[i.length-1],e[n]<m){t[s]=n,i.push(s);continue}for(o=0,r=i.length-1;o<r;)l=o+r>>1,e[i[l]]<m?o=l+1:r=l;m<e[i[o]]&&(o>0&&(t[s]=i[o-1]),i[o]=s)}}for(o=i.length,r=i[o-1];o-- >0;)i[o]=r,r=t[r];return i}function On(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:On(t)}function ys(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const mr=Symbol.for("v-scx"),Cr=()=>kt(mr);function xi(e,t,i){return Pn(e,t,i)}function Pn(e,t,i=N){const{immediate:s,deep:n,flush:o,once:r}=i,l=se({},i),a=t&&s||!t&&o!=="post";let m;if(Dt){if(o==="sync"){const L=Cr();m=L.__watcherHandles||(L.__watcherHandles=[])}else if(!a){const L=()=>{};return L.stop=De,L.resume=De,L.pause=De,L}}const d=ue;l.call=(L,H,F)=>Fe(L,d,H,F);let h=!1;o==="post"?l.scheduler=L=>{me(L,d&&d.suspense)}:o!=="sync"&&(h=!0,l.scheduler=(L,H)=>{H?L():is(L)}),l.augmentJob=L=>{t&&(L.flags|=4),h&&(L.flags|=2,d&&(L.id=d.uid,L.i=d))};const _=Do(e,t,l);return Dt&&(m?m.push(_):a&&_()),_}function vr(e,t,i){const s=this.proxy,n=ee(e)?e.includes(".")?Dn(s,e):()=>s[e]:e.bind(s,s);let o;R(t)?o=t:(o=t.handler,i=t);const r=Ht(this),l=Pn(n,o.bind(s),i);return r(),l}function Dn(e,t){const i=t.split(".");return()=>{let s=e;for(let n=0;n<i.length&&s;n++)s=s[i[n]];return s}}const yr=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ke(t)}Modifiers`]||e[`${rt(t)}Modifiers`];function Er(e,t,...i){if(e.isUnmounted)return;const s=e.vnode.props||N;let n=i;const o=t.startsWith("update:"),r=o&&yr(s,t.slice(7));r&&(r.trim&&(n=i.map(d=>ee(d)?d.trim():d)),r.number&&(n=i.map(Zt)));let l,a=s[l=gi(t)]||s[l=gi(Ke(t))];!a&&o&&(a=s[l=gi(rt(t))]),a&&Fe(a,e,6,n);const m=s[l+"Once"];if(m){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Fe(m,e,6,n)}}function Rn(e,t,i=!1){const s=t.emitsCache,n=s.get(e);if(n!==void 0)return n;const o=e.emits;let r={},l=!1;if(!R(e)){const a=m=>{const d=Rn(m,t,!0);d&&(l=!0,se(r,d))};!i&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(q(e)&&s.set(e,null),null):(D(o)?o.forEach(a=>r[a]=null):se(r,o),q(e)&&s.set(e,r),r)}function ui(e,t){return!e||!ni(t)?!1:(t=t.slice(2).replace(/Once$/,""),W(e,t[0].toLowerCase()+t.slice(1))||W(e,rt(t))||W(e,t))}function wi(e){const{type:t,vnode:i,proxy:s,withProxy:n,propsOptions:[o],slots:r,attrs:l,emit:a,render:m,renderCache:d,props:h,data:_,setupState:L,ctx:H,inheritAttrs:F}=e,te=$t(e);let Q,K;try{if(i.shapeFlag&4){const O=n||s,J=O;Q=Pe(m.call(J,O,d,h,L,_,H)),K=l}else{const O=t;Q=Pe(O.length>1?O(h,{attrs:l,slots:r,emit:a}):O(h,null)),K=t.props?l:br(l)}}catch(O){_t.length=0,ci(O,e,1),Q=Re(ot)}let U=Q;if(K&&F!==!1){const O=Object.keys(K),{shapeFlag:J}=U;O.length&&J&7&&(o&&O.some(Ui)&&(K=xr(K,o)),U=pt(U,K,!1,!0))}return i.dirs&&(U=pt(U,null,!1,!0),U.dirs=U.dirs?U.dirs.concat(i.dirs):i.dirs),i.transition&&ss(U,i.transition),Q=U,$t(te),Q}const br=e=>{let t;for(const i in e)(i==="class"||i==="style"||ni(i))&&((t||(t={}))[i]=e[i]);return t},xr=(e,t)=>{const i={};for(const s in e)(!Ui(s)||!(s.slice(9)in t))&&(i[s]=e[s]);return i};function wr(e,t,i){const{props:s,children:n,component:o}=e,{props:r,children:l,patchFlag:a}=t,m=o.emitsOptions;if(t.dirs||t.transition)return!0;if(i&&a>=0){if(a&1024)return!0;if(a&16)return s?Es(s,r,m):!!r;if(a&8){const d=t.dynamicProps;for(let h=0;h<d.length;h++){const _=d[h];if(r[_]!==s[_]&&!ui(m,_))return!0}}}else return(n||l)&&(!l||!l.$stable)?!0:s===r?!1:s?r?Es(s,r,m):!0:!!r;return!1}function Es(e,t,i){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let n=0;n<s.length;n++){const o=s[n];if(t[o]!==e[o]&&!ui(i,o))return!0}return!1}function Ar({vnode:e,parent:t},i){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=i,t=t.parent;else break}}const Fn=e=>e.__isSuspense;function Mr(e,t){t&&t.pendingBranch?D(e)?t.effects.push(...e):t.effects.push(e):Ho(e)}const Be=Symbol.for("v-fgt"),fi=Symbol.for("v-txt"),ot=Symbol.for("v-cmt"),Kt=Symbol.for("v-stc"),_t=[];let ve=null;function Gt(e=!1){_t.push(ve=e?null:[])}function Tr(){_t.pop(),ve=_t[_t.length-1]||null}let Pt=1;function bs(e){Pt+=e,e<0&&ve&&(ve.hasOnce=!0)}function Hn(e){return e.dynamicChildren=Pt>0?ve||ut:null,Tr(),Pt>0&&ve&&ve.push(e),e}function Ai(e,t,i,s,n,o){return Hn(ie(e,t,i,s,n,o,!0))}function Sr(e,t,i,s,n){return Hn(Re(e,t,i,s,n,!0))}function Vn(e){return e?e.__v_isVNode===!0:!1}function yt(e,t){return e.type===t.type&&e.key===t.key}const Bn=({key:e})=>e??null,Jt=({ref:e,ref_key:t,ref_for:i})=>(typeof e=="number"&&(e=""+e),e!=null?ee(e)||re(e)||R(e)?{i:be,r:e,k:t,f:!!i}:e:null);function ie(e,t=null,i=null,s=0,n=null,o=e===Be?0:1,r=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Bn(t),ref:t&&Jt(t),scopeId:hn,slotScopeIds:null,children:i,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:be};return l?(rs(a,i),o&128&&e.normalize(a)):i&&(a.shapeFlag|=ee(i)?8:16),Pt>0&&!r&&ve&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&ve.push(a),a}const Re=_r;function _r(e,t=null,i=null,s=0,n=null,o=!1){if((!e||e===Zo)&&(e=ot),Vn(e)){const l=pt(e,t,!0);return i&&rs(l,i),Pt>0&&!o&&ve&&(l.shapeFlag&6?ve[ve.indexOf(e)]=l:ve.push(l)),l.patchFlag=-2,l}if(Qr(e)&&(e=e.__vccOpts),t){t=Ir(t);let{class:l,style:a}=t;l&&!ee(l)&&(t.class=Ki(l)),q(a)&&(ts(a)&&!D(a)&&(a=se({},a)),t.style=ki(a))}const r=ee(e)?1:Fn(e)?128:Wo(e)?64:q(e)?4:R(e)?2:0;return ie(e,t,i,s,n,r,o,!0)}function Ir(e){return e?ts(e)||wn(e)?se({},e):e:null}function pt(e,t,i=!1,s=!1){const{props:n,ref:o,patchFlag:r,children:l,transition:a}=e,m=t?Pr(n||{},t):n,d={__v_isVNode:!0,__v_skip:!0,type:e.type,props:m,key:m&&Bn(m),ref:t&&t.ref?i&&o?D(o)?o.concat(Jt(t)):[o,Jt(t)]:Jt(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Be?r===-1?16:r|16:r,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&pt(e.ssContent),ssFallback:e.ssFallback&&pt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&s&&ss(d,a.clone(d)),d}function Lr(e=" ",t=0){return Re(fi,null,e,t)}function Or(e,t){const i=Re(Kt,null,e);return i.staticCount=t,i}function Mi(e="",t=!1){return t?(Gt(),Sr(ot,null,e)):Re(ot,null,e)}function Pe(e){return e==null||typeof e=="boolean"?Re(ot):D(e)?Re(Be,null,e.slice()):Vn(e)?Ne(e):Re(fi,null,String(e))}function Ne(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:pt(e)}function rs(e,t){let i=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(D(t))i=16;else if(typeof t=="object")if(s&65){const n=t.default;n&&(n._c&&(n._d=!1),rs(e,n()),n._c&&(n._d=!0));return}else{i=32;const n=t._;!n&&!wn(t)?t._ctx=be:n===3&&be&&(be.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else R(t)?(t={default:t,_ctx:be},i=32):(t=String(t),s&64?(i=16,t=[Lr(t)]):i=8);e.children=t,e.shapeFlag|=i}function Pr(...e){const t={};for(let i=0;i<e.length;i++){const s=e[i];for(const n in s)if(n==="class")t.class!==s.class&&(t.class=Ki([t.class,s.class]));else if(n==="style")t.style=ki([t.style,s.style]);else if(ni(n)){const o=t[n],r=s[n];r&&o!==r&&!(D(o)&&o.includes(r))&&(t[n]=o?[].concat(o,r):r)}else n!==""&&(t[n]=s[n])}return t}function Le(e,t,i,s=null){Fe(e,t,7,[i,s])}const Dr=En();let Rr=0;function Fr(e,t,i){const s=e.type,n=(t?t.appContext:e.appContext)||Dr,o={uid:Rr++,vnode:e,type:s,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new so(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Mn(s,n),emitsOptions:Rn(s,n),emit:null,emitted:null,propsDefaults:N,inheritAttrs:s.inheritAttrs,ctx:N,data:N,props:N,attrs:N,slots:N,refs:N,setupState:N,setupContext:null,suspense:i,suspenseId:i?i.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Er.bind(null,o),e.ce&&e.ce(o),o}let ue=null,ti,Bi;{const e=ri(),t=(i,s)=>{let n;return(n=e[i])||(n=e[i]=[]),n.push(s),o=>{n.length>1?n.forEach(r=>r(o)):n[0](o)}};ti=t("__VUE_INSTANCE_SETTERS__",i=>ue=i),Bi=t("__VUE_SSR_SETTERS__",i=>Dt=i)}const Ht=e=>{const t=ue;return ti(e),e.scope.on(),()=>{e.scope.off(),ti(t)}},xs=()=>{ue&&ue.scope.off(),ti(null)};function Wn(e){return e.vnode.shapeFlag&4}let Dt=!1;function Hr(e,t=!1,i=!1){t&&Bi(t);const{props:s,children:n}=e.vnode,o=Wn(e);rr(e,s,o,t),ur(e,n,i);const r=o?Vr(e,t):void 0;return t&&Bi(!1),r}function Vr(e,t){const i=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,zo);const{setup:s}=i;if(s){Ge();const n=e.setupContext=s.length>1?Wr(e):null,o=Ht(e),r=Ft(s,e,0,[e.props,n]),l=Qs(r);if(Je(),o(),(l||e.sp)&&!Tt(e)&&dn(e),l){if(r.then(xs,xs),t)return r.then(a=>{ws(e,a,t)}).catch(a=>{ci(a,e,0)});e.asyncDep=r}else ws(e,r,t)}else Qn(e,t)}function ws(e,t,i){R(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:q(t)&&(e.setupState=rn(t)),Qn(e,i)}let As;function Qn(e,t,i){const s=e.type;if(!e.render){if(!t&&As&&!s.render){const n=s.template||ns(e).template;if(n){const{isCustomElement:o,compilerOptions:r}=e.appContext.config,{delimiters:l,compilerOptions:a}=s,m=se(se({isCustomElement:o,delimiters:l},r),a);s.render=As(n,m)}}e.render=s.render||De}{const n=Ht(e);Ge();try{Xo(e)}finally{Je(),n()}}}const Br={get(e,t){return oe(e,"get",""),e[t]}};function Wr(e){const t=i=>{e.exposed=i||{}};return{attrs:new Proxy(e.attrs,Br),slots:e.slots,emit:e.emit,expose:t}}function hi(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(rn(Mo(e.exposed)),{get(t,i){if(i in t)return t[i];if(i in St)return St[i](e)},has(t,i){return i in t||i in St}})):e.proxy}function Qr(e){return R(e)&&"__vccOpts"in e}const Ur=(e,t)=>Oo(e,t,Dt),jr="3.5.12";/**
* @vue/runtime-dom v3.5.12
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Wi;const Ms=typeof window<"u"&&window.trustedTypes;if(Ms)try{Wi=Ms.createPolicy("vue",{createHTML:e=>e})}catch{}const Un=Wi?e=>Wi.createHTML(e):e=>e,Nr="http://www.w3.org/2000/svg",Yr="http://www.w3.org/1998/Math/MathML",Ve=typeof document<"u"?document:null,Ts=Ve&&Ve.createElement("template"),kr={insert:(e,t,i)=>{t.insertBefore(e,i||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,i,s)=>{const n=t==="svg"?Ve.createElementNS(Nr,e):t==="mathml"?Ve.createElementNS(Yr,e):i?Ve.createElement(e,{is:i}):Ve.createElement(e);return e==="select"&&s&&s.multiple!=null&&n.setAttribute("multiple",s.multiple),n},createText:e=>Ve.createTextNode(e),createComment:e=>Ve.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ve.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,i,s,n,o){const r=i?i.previousSibling:t.lastChild;if(n&&(n===o||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),i),!(n===o||!(n=n.nextSibling)););else{Ts.innerHTML=Un(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Ts.content;if(s==="svg"||s==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,i)}return[r?r.nextSibling:t.firstChild,i?i.previousSibling:t.lastChild]}},Kr=Symbol("_vtc");function Gr(e,t,i){const s=e[Kr];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):i?e.setAttribute("class",t):e.className=t}const Ss=Symbol("_vod"),Jr=Symbol("_vsh"),qr=Symbol(""),Zr=/(^|;)\s*display\s*:/;function zr(e,t,i){const s=e.style,n=ee(i);let o=!1;if(i&&!n){if(t)if(ee(t))for(const r of t.split(";")){const l=r.slice(0,r.indexOf(":")).trim();i[l]==null&&qt(s,l,"")}else for(const r in t)i[r]==null&&qt(s,r,"");for(const r in i)r==="display"&&(o=!0),qt(s,r,i[r])}else if(n){if(t!==i){const r=s[qr];r&&(i+=";"+r),s.cssText=i,o=Zr.test(i)}}else t&&e.removeAttribute("style");Ss in e&&(e[Ss]=o?s.display:"",e[Jr]&&(s.display="none"))}const _s=/\s*!important$/;function qt(e,t,i){if(D(i))i.forEach(s=>qt(e,t,s));else if(i==null&&(i=""),t.startsWith("--"))e.setProperty(t,i);else{const s=Xr(e,t);_s.test(i)?e.setProperty(rt(s),i.replace(_s,""),"important"):e[s]=i}}const Is=["Webkit","Moz","ms"],Ti={};function Xr(e,t){const i=Ti[t];if(i)return i;let s=Ke(t);if(s!=="filter"&&s in e)return Ti[t]=s;s=Us(s);for(let n=0;n<Is.length;n++){const o=Is[n]+s;if(o in e)return Ti[t]=o}return t}const Ls="http://www.w3.org/1999/xlink";function Os(e,t,i,s,n,o=eo(t)){s&&t.startsWith("xlink:")?i==null?e.removeAttributeNS(Ls,t.slice(6,t.length)):e.setAttributeNS(Ls,t,i):i==null||o&&!Ns(i)?e.removeAttribute(t):e.setAttribute(t,o?"":ke(i)?String(i):i)}function Ps(e,t,i,s,n){if(t==="innerHTML"||t==="textContent"){i!=null&&(e[t]=t==="innerHTML"?Un(i):i);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=i==null?e.type==="checkbox"?"on":"":String(i);(l!==a||!("_value"in e))&&(e.value=a),i==null&&e.removeAttribute(t),e._value=i;return}let r=!1;if(i===""||i==null){const l=typeof e[t];l==="boolean"?i=Ns(i):i==null&&l==="string"?(i="",r=!0):l==="number"&&(i=0,r=!0)}try{e[t]=i}catch{}r&&e.removeAttribute(n||t)}function it(e,t,i,s){e.addEventListener(t,i,s)}function $r(e,t,i,s){e.removeEventListener(t,i,s)}const Ds=Symbol("_vei");function el(e,t,i,s,n=null){const o=e[Ds]||(e[Ds]={}),r=o[t];if(s&&r)r.value=s;else{const[l,a]=tl(t);if(s){const m=o[t]=nl(s,n);it(e,l,m,a)}else r&&($r(e,l,r,a),o[t]=void 0)}}const Rs=/(?:Once|Passive|Capture)$/;function tl(e){let t;if(Rs.test(e)){t={};let s;for(;s=e.match(Rs);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):rt(e.slice(2)),t]}let Si=0;const il=Promise.resolve(),sl=()=>Si||(il.then(()=>Si=0),Si=Date.now());function nl(e,t){const i=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=i.attached)return;Fe(ol(s,i.value),t,5,[s])};return i.value=e,i.attached=sl(),i}function ol(e,t){if(D(t)){const i=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{i.call(e),e._stopped=!0},t.map(s=>n=>!n._stopped&&s&&s(n))}else return t}const Fs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,rl=(e,t,i,s,n,o)=>{const r=n==="svg";t==="class"?Gr(e,s,r):t==="style"?zr(e,i,s):ni(t)?Ui(t)||el(e,t,i,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ll(e,t,s,r))?(Ps(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Os(e,t,s,r,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ee(s))?Ps(e,Ke(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Os(e,t,s,r))};function ll(e,t,i,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Fs(t)&&R(i));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return Fs(t)&&ee(i)?!1:t in e}const ii=e=>{const t=e.props["onUpdate:modelValue"]||!1;return D(t)?i=>Yt(t,i):t};function cl(e){e.target.composing=!0}function Hs(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const dt=Symbol("_assign"),Vs={created(e,{modifiers:{lazy:t,trim:i,number:s}},n){e[dt]=ii(n);const o=s||n.props&&n.props.type==="number";it(e,t?"change":"input",r=>{if(r.target.composing)return;let l=e.value;i&&(l=l.trim()),o&&(l=Zt(l)),e[dt](l)}),i&&it(e,"change",()=>{e.value=e.value.trim()}),t||(it(e,"compositionstart",cl),it(e,"compositionend",Hs),it(e,"change",Hs))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:i,modifiers:{lazy:s,trim:n,number:o}},r){if(e[dt]=ii(r),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?Zt(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(s&&t===i||n&&e.value.trim()===a)||(e.value=a))}},al={deep:!0,created(e,{value:t,modifiers:{number:i}},s){const n=Ni(t);it(e,"change",()=>{const o=Array.prototype.filter.call(e.options,r=>r.selected).map(r=>i?Zt(si(r)):si(r));e[dt](e.multiple?n?new Set(o):o:o[0]),e._assigning=!0,cn(()=>{e._assigning=!1})}),e[dt]=ii(s)},mounted(e,{value:t}){Bs(e,t)},beforeUpdate(e,t,i){e[dt]=ii(i)},updated(e,{value:t}){e._assigning||Bs(e,t)}};function Bs(e,t){const i=e.multiple,s=D(t);if(!(i&&!s&&!Ni(t))){for(let n=0,o=e.options.length;n<o;n++){const r=e.options[n],l=si(r);if(i)if(s){const a=typeof l;a==="string"||a==="number"?r.selected=t.some(m=>String(m)===String(l)):r.selected=io(t,l)>-1}else r.selected=t.has(l);else if(li(si(r),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!i&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function si(e){return"_value"in e?e._value:e.value}const ul=se({patchProp:rl},kr);let Ws;function fl(){return Ws||(Ws=hr(ul))}const hl=(...e)=>{const t=fl().createApp(...e),{mount:i}=t;return t.mount=s=>{const n=pl(s);if(!n)return;const o=t._component;!R(o)&&!o.render&&!o.template&&(o.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const r=i(n,!1,dl(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),r},t};function dl(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function pl(e){return ee(e)?document.querySelector(e):e}const $="data:image/png;base64,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";class di{constructor(t){this._definitionChanged=new Cesium.Event,this._color=void 0,this.color=t.color}get isConstant(){return!1}get definitionChanged(){return this._definitionChanged}getType(t){return Cesium.Material.WallDiffuseMaterialType}getValue(t,i){return Cesium.defined(i)||(i={}),i.color=Cesium.Property.getValueOrDefault(this._color,t,Cesium.Color.RED,i.color),i}equals(t){return this===t||t instanceof di&&Cesium.Property.equals(this._color,t._color)}}Object.defineProperties(di.prototype,{color:Cesium.createPropertyDescriptor("color")});Cesium.Property.WallDiffuseMaterialProperty=di;Cesium.Material.WallDiffuseMaterialProperty="WallDiffuseMaterialProperty";Cesium.Material.WallDiffuseMaterialType="WallDiffuseMaterialType";Cesium.Material.WallDiffuseMaterialSource=`
    uniform vec4 color;
    czm_material czm_getMaterial(czm_materialInput materialInput){
    czm_material material = czm_getDefaultMaterial(materialInput);
    vec2 st = materialInput.st;

    material.diffuse = color.rgb * 2.0;
    material.alpha = color.a * (1.0 - fract(st.t)) * 0.8 ;
    return material;
    }
                                            
    `;Cesium.Material._materialCache.addMaterial(Cesium.Material.WallDiffuseMaterialType,{fabric:{type:Cesium.Material.WallDiffuseMaterialType,uniforms:{color:new Cesium.Color(1,0,0,1),scanLineHeight:0},source:Cesium.Material.WallDiffuseMaterialSource},translucent:function(e){return!0}});class gl{constructor(t){this.viewer=t,this.initEvents(),this.positions=[],this.tempPositions=[],this.vertexEntities=[],this.labelEntity=void 0,this.moveVertexEntity=void 0,this.measureDistance=0}initEvents(){this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.MeasureStartEvent=new Cesium.Event,this.MeasureEndEvent=new Cesium.Event}activate(){this.deactivate(),this.registerEvents(),this.viewer.enableCursorStyle=!1,this.viewer._element.style.cursor="default",this.isMeasure=!0,this.measureDistance=0}deactivate(){this.isMeasure&&(this.unRegisterEvents(),this.viewer._element.style.cursor="pointer",this.viewer.enableCursorStyle=!0,this.isMeasure=!1,this.tempPositions=[],this.positions=[])}clear(){this.viewer.entities.remove(this.lineEntity),this.lineEntity=void 0,this.vertexEntities.forEach(t=>{this.viewer.entities.remove(t)}),this.vertexEntities=[],this.moveVertexEntity&&(this.viewer.entities.remove(this.moveVertexEntity),this.moveVertexEntity=void 0)}createLineEntity(){this.lineEntity=this.viewer.entities.add({polyline:{positions:new Cesium.CallbackProperty(t=>this.tempPositions,!1),width:2,material:Cesium.Color.YELLOW,depthFailMaterial:Cesium.Color.YELLOW}})}createVertex(){let t=this.viewer.entities.add({position:this.positions[this.positions.length-1],id:"MeasureDistanceVertex"+this.positions.length,type:"MeasureDistanceVertex",label:{text:this.getSpaceDistance(this.positions)+"米",scale:.5,font:"normal 24px MicroSoft YaHei",distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,5e3),scaleByDistance:new Cesium.NearFarScalar(1e3,1,3e3,.4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM,style:Cesium.LabelStyle.FILL_AND_OUTLINE,pixelOffset:new Cesium.Cartesian2(0,-30),outlineWidth:9,outlineColor:Cesium.Color.WHITE},point:{color:Cesium.Color.FUCHSIA,pixelSize:8,disableDepthTestDistance:500}});this.vertexEntities.push(t)}createStartEntity(){let t=this.viewer.entities.add({position:this.positions[0],type:"MeasureDistanceVertex",billboard:{image:"data:image/png;base64,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",scaleByDistance:new Cesium.NearFarScalar(300,1,1200,.4),distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,1e4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM},point:{color:Cesium.Color.FUCHSIA,pixelSize:6}});this.vertexEntities.push(t)}createEndEntity(){let t=this.viewer.entities.getById("MeasureDistanceVertex"+this.positions.length);t&&this.viewer.entities.remove(t),this.moveVertexEntity&&this.viewer.entities.remove(this.moveVertexEntity);let i=this.viewer.entities.add({position:this.positions[this.positions.length-1],type:"MeasureDistanceVertex",label:{text:"总距离："+this.getSpaceDistance(this.positions)+"米",scale:.5,font:"normal 26px MicroSoft YaHei",distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,5e3),scaleByDistance:new Cesium.NearFarScalar(1e3,1,3e3,.4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM,style:Cesium.LabelStyle.FILL_AND_OUTLINE,pixelOffset:new Cesium.Cartesian2(0,-50),outlineWidth:9,outlineColor:Cesium.Color.WHITE,eyeOffset:new Cesium.Cartesian3(0,0,-10)},billboard:{image:"data:image/png;base64,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",scaleByDistance:new Cesium.NearFarScalar(300,1,1200,.4),distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,1e4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM},point:{color:Cesium.Color.FUCHSIA,pixelSize:6}});this.vertexEntities.push(i)}registerEvents(){this.leftClickEvent(),this.rightClickEvent(),this.mouseMoveEvent()}leftClickEvent(){this.handler.setInputAction(t=>{this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.position);if(!i){const s=this.viewer.scene.globe.ellipsoid;i=this.viewer.scene.camera.pickEllipsoid(t.position,s)}if(i){if(this.positions.push(i),this.positions.length==1){this.createLineEntity(),this.createStartEntity();return}this.createVertex()}},Cesium.ScreenSpaceEventType.LEFT_CLICK)}mouseMoveEvent(){this.handler.setInputAction(t=>{if(!this.isMeasure)return;this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.endPosition);i||(i=this.viewer.scene.camera.pickEllipsoid(t.endPosition,this.viewer.scene.globe.ellipsoid)),i&&this.handleMoveEvent(i)},Cesium.ScreenSpaceEventType.MOUSE_MOVE)}handleMoveEvent(t){this.positions.length<1||(this.tempPositions=this.positions.concat([t]),!this.moveVertexEntity&&this.positions.length>=1&&(this.moveVertexEntity=this.viewer.entities.add({position:new Cesium.CallbackProperty(()=>this.tempPositions.length>0?this.tempPositions[this.tempPositions.length-1]:null,!1),point:{color:Cesium.Color.YELLOW,pixelSize:8,disableDepthTestDistance:500},label:{text:new Cesium.CallbackProperty(()=>this.tempPositions.length>1?this.getSpaceDistance(this.tempPositions)+"米":"",!1),scale:.5,font:"normal 24px MicroSoft YaHei",distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,5e3),scaleByDistance:new Cesium.NearFarScalar(1e3,1,3e3,.4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM,style:Cesium.LabelStyle.FILL_AND_OUTLINE,pixelOffset:new Cesium.Cartesian2(0,-30),outlineWidth:9,outlineColor:Cesium.Color.WHITE}})))}rightClickEvent(){this.handler.setInputAction(t=>{!this.isMeasure||this.positions.length<1?(this.deactivate(),this.clear()):(this.createEndEntity(),this.lineEntity.polyline={positions:this.positions,width:2,material:Cesium.Color.YELLOW,depthFailMaterial:Cesium.Color.YELLOW},this.measureEnd())},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}measureEnd(){this.deactivate(),this.measureDistance=this.getSpaceDistance(this.positions),this.MeasureEndEvent.raiseEvent(this.measureDistance)}unRegisterEvents(){this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)}getSpaceDistance(t){let i=0;for(let s=0;s<t.length-1;s++){const n=Cesium.Cartographic.fromCartesian(t[s]),o=Cesium.Cartographic.fromCartesian(t[s+1]),r=new Cesium.EllipsoidGeodesic;r.setEndPoints(n,o);let l=r.surfaceDistance;l=Math.sqrt(Math.pow(l,2)+Math.pow(o.height-n.height,2)),i=i+l}return i.toFixed(2)}}class ml{constructor(t){this.viewer=t,this.initEvents(),this.positions=[],this.vertexEntities=[],this.labelEntity=void 0,this.measureHeight=0}initEvents(){this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.MeasureStartEvent=new Cesium.Event,this.MeasureEndEvent=new Cesium.Event}activate(){this.deactivate(),this.registerEvents(),this.viewer.enableCursorStyle=!1,this.viewer._element.style.cursor="default",this.isMeasure=!0,this.circleRadius=.1,this.measureHeight=0,this.positions=[]}deactivate(){this.isMeasure&&(this.unRegisterEvents(),this.viewer._element.style.cursor="pointer",this.viewer.enableCursorStyle=!0,this.isMeasure=!1)}clear(){this.viewer.entities.remove(this.lineEntity),this.lineEntity=void 0,this.viewer.entities.remove(this.labelEntity),this.labelEntity=void 0,this.removeCircleEntity(),this.vertexEntities.forEach(t=>{this.viewer.entities.remove(t)}),this.vertexEntities=[]}createLineEntity(){this.lineEntity=this.viewer.entities.add({polyline:{positions:new Cesium.CallbackProperty(t=>this.positions,!1),width:2,material:Cesium.Color.YELLOW,depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.RED})}})}createLabel(){this.labelEntity=this.viewer.entities.add({position:new Cesium.CallbackProperty(t=>this.positions[this.positions.length-1],!1),label:{text:"",scale:.5,font:"normal 40px MicroSoft YaHei",distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,5e3),scaleByDistance:new Cesium.NearFarScalar(500,1,1500,.4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM,style:Cesium.LabelStyle.FILL_AND_OUTLINE,pixelOffset:new Cesium.Cartesian2(0,-30),outlineWidth:9,outlineColor:Cesium.Color.WHITE}})}createVertex(t){let i=this.viewer.entities.add({position:new Cesium.CallbackProperty(s=>this.positions[t],!1),type:"MeasureHeightVertex",point:{color:Cesium.Color.FUCHSIA,pixelSize:6}});this.vertexEntities.push(i)}createCircleEntitiy(){this.circleEntity=this.viewer.entities.add({position:new Cesium.CallbackProperty(t=>this.positions[this.positions.length-1],!1),ellipse:{height:new Cesium.CallbackProperty(t=>this.getPositionHeight(this.positions[this.positions.length-1]),!1),semiMinorAxis:new Cesium.CallbackProperty(t=>this.circleRadius,!1),semiMajorAxis:new Cesium.CallbackProperty(t=>this.circleRadius,!1),material:Cesium.Color.YELLOW.withAlpha(.5)}})}removeCircleEntity(){this.viewer.entities.remove(this.circleEntity),this.circleEntity=void 0}registerEvents(){this.leftClickEvent(),this.rightClickEvent(),this.mouseMoveEvent()}leftClickEvent(){this.handler.setInputAction(t=>{this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.position);if(!i){const s=this.viewer.scene.globe.ellipsoid;i=this.viewer.scene.camera.pickEllipsoid(t.position,s)}i&&(this.positions.length==0?(this.positions.push(i),this.createVertex(0),this.createLineEntity(),this.createCircleEntitiy(),this.createLabel()):this.measureEnd())},Cesium.ScreenSpaceEventType.LEFT_CLICK)}mouseMoveEvent(){this.handler.setInputAction(t=>{if(!this.isMeasure)return;this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.endPosition);i||(i=this.viewer.scene.camera.pickEllipsoid(t.endPosition,this.viewer.scene.globe.ellipsoid)),i&&this.handleMoveEvent(i)},Cesium.ScreenSpaceEventType.MOUSE_MOVE)}handleMoveEvent(t){if(this.positions.length<1)return;let i=this.cartesian3ToPoint3D(this.positions[0]),s=this.cartesian3ToPoint3D(t);const n=s.z-i.z;i.z=s.z;const o=Cesium.Cartesian3.fromDegrees(i.x,i.y,s.z);this.positions.length<2?(this.positions.push(o),this.createVertex(1)):(this.positions[1]=o,this.measureHeight=Math.abs(n).toFixed(3),this.labelEntity.label.text="高度："+this.measureHeight+" 米"),this.circleRadius=this.getDistanceH(this.positions[0],t)}rightClickEvent(){this.handler.setInputAction(t=>{this.isMeasure&&(this.deactivate(),this.clear())},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}measureEnd(){this.deactivate(),this.MeasureEndEvent.raiseEvent(this.measureHeight)}unRegisterEvents(){this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)}getPositionHeight(t){return Cesium.Cartographic.fromCartesian(t).height}cartesian3ToPoint3D(t){const i=Cesium.Cartographic.fromCartesian(t),s=Cesium.Math.toDegrees(i.longitude),n=Cesium.Math.toDegrees(i.latitude),o=i.height;return{x:s,y:n,z:o}}getDistanceH(t,i){const s=this.cartesian3ToPoint3D(t),n=this.cartesian3ToPoint3D(i),o=new Cesium.EllipsoidGeodesic,r=Cesium.Cartographic.fromDegrees(s.x,s.y),l=Cesium.Cartographic.fromDegrees(n.x,n.y);return o.setEndPoints(r,l),o.surfaceDistance}}class Cl{constructor(t){this.viewer=t,this.initEvents(),this.positions=[],this.tempPositions=[],this.vertexEntities=[],this.labelEntity=void 0,this.measureArea=0}initEvents(){this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.MeasureStartEvent=new Cesium.Event,this.MeasureEndEvent=new Cesium.Event}activate(){this.deactivate(),this.registerEvents(),this.viewer.enableCursorStyle=!1,this.viewer._element.style.cursor="default",this.isMeasure=!0,this.measureArea=0}deactivate(){this.isMeasure&&(this.unRegisterEvents(),this.viewer._element.style.cursor="pointer",this.viewer.enableCursorStyle=!0,this.isMeasure=!1,this.tempPositions=[],this.positions=[],this.height=void 0)}clear(){this.viewer.entities.remove(this.polygonEntity),this.polygonEntity=void 0,this.vertexEntities.forEach(t=>{this.viewer.entities.remove(t)}),this.vertexEntities=[],this.viewer.entities.remove(this.mesureResultEntity),this.mesureResultEntity=void 0,this.height=void 0}createPolygonEntity(){this.polygonEntity=this.viewer.entities.add({polygon:{hierarchy:new Cesium.CallbackProperty(t=>new Cesium.PolygonHierarchy(this.tempPositions),!1),material:Cesium.Color.RED.withAlpha(.4),perPositionHeight:!0},polyline:{positions:new Cesium.CallbackProperty(t=>this.tempPositions.concat(this.tempPositions[0]),!1),width:1,material:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.YELLOW}),depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.YELLOW})}})}createVertex(){let t=this.viewer.entities.add({position:this.positions[this.positions.length-1],type:"MeasureAreaVertex",point:{color:Cesium.Color.FUCHSIA,pixelSize:8,disableDepthTestDistance:500}});this.vertexEntities.push(t)}createResultLabel(){this.mesureResultEntity=this.viewer.entities.add({position:new Cesium.CallbackProperty(t=>this.getCenterPosition(),!1),type:"MeasureAreaResult",label:{text:new Cesium.CallbackProperty(t=>"面积："+this.computeArea(this.tempPositions)+"平方米",!1),scale:.5,font:"normal 28px MicroSoft YaHei",distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,5e3),scaleByDistance:new Cesium.NearFarScalar(1e3,1,3e3,.4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM,style:Cesium.LabelStyle.FILL_AND_OUTLINE,pixelOffset:new Cesium.Cartesian2(0,-30),outlineWidth:9,outlineColor:Cesium.Color.YELLOW}})}getCenterPosition(){if(this.tempPositions.length<3)return this.tempPositions[0];let t=0,i=0,s=0;for(let n=0;n<this.tempPositions.length;n++){const o=this.cartesian3ToPoint3D(this.tempPositions[n]);t+=o.x,i+=o.y,s+=o.z}return t/=this.tempPositions.length,i/=this.tempPositions.length,s/=this.tempPositions.length,Cesium.Cartesian3.fromDegrees(t,i,this.height||s)}registerEvents(){this.leftClickEvent(),this.rightClickEvent(),this.mouseMoveEvent()}leftClickEvent(){this.handler.setInputAction(t=>{this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.position);if(!i){const s=this.viewer.scene.globe.ellipsoid;i=this.viewer.scene.camera.pickEllipsoid(t.position,s)}i&&(this.positions.push(i),this.height=this.unifiedHeight(this.positions,this.height),this.positions.length==1&&this.createPolygonEntity(),this.createVertex())},Cesium.ScreenSpaceEventType.LEFT_CLICK)}mouseMoveEvent(){this.handler.setInputAction(t=>{if(!this.isMeasure)return;this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.endPosition);i||(i=this.viewer.scene.camera.pickEllipsoid(t.endPosition,this.viewer.scene.globe.ellipsoid)),i&&this.handleMoveEvent(i)},Cesium.ScreenSpaceEventType.MOUSE_MOVE)}handleMoveEvent(t){this.positions.length<1||(this.height=this.unifiedHeight(this.positions,this.height),this.tempPositions=this.positions.concat([t]),this.tempPositions.length>=3&&!this.mesureResultEntity&&this.createResultLabel())}unifiedHeight(t,i){i||(i=this.getPositionHeight(t[0]));let s;for(let n=0;n<t.length;n++){const o=t[n];s=this.cartesian3ToPoint3D(o),t[n]=Cesium.Cartesian3.fromDegrees(s.x,s.y,i)}return i}getPositionHeight(t){return Cesium.Cartographic.fromCartesian(t).height}cartesian3ToPoint3D(t){const i=Cesium.Cartographic.fromCartesian(t),s=Cesium.Math.toDegrees(i.longitude),n=Cesium.Math.toDegrees(i.latitude);return{x:s,y:n,z:i.height}}computeArea(t){if(t.length<3)return 0;let i=[];for(let r=0;r<t.length;r++){const l=this.cartesian3ToPoint3D(t[r]);i.push([l.x,l.y])}let s=0,n=i.length-1;for(let r=0;r<i.length;r++){const l=i[r],a=i[n],m=l[0]*Math.PI/180,d=l[1]*Math.PI/180,h=a[0]*Math.PI/180;a[1]*Math.PI/180,s+=(h-m)*Math.sin(d),n=r}const o=6378137;return s=Math.abs(s*o*o/2),s.toFixed(2)}rightClickEvent(){this.handler.setInputAction(t=>{!this.isMeasure||this.positions.length<3?(this.deactivate(),this.clear()):(this.tempPositions=[...this.positions],this.polygonEntity.polyline={positions:this.positions.concat(this.positions[0]),width:2,material:Cesium.Color.YELLOW,depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.YELLOW})},this.polygonEntity.polygon.hierarchy=new Cesium.PolygonHierarchy(this.tempPositions),this.mesureResultEntity.position=this.getCenterPosition(),this.mesureResultEntity.label.text="总面积："+this.computeArea(this.positions)+"平方米",this.measureArea=this.computeArea(this.positions),this.measureEnd())},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}measureEnd(){this.deactivate(),this.MeasureEndEvent.raiseEvent(this.measureArea)}unRegisterEvents(){this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)}}const vl=(e,t)=>{const i=e.__vccOpts||e;for(const[s,n]of t)i[s]=n;return i},yl={id:"app"},El=["innerHTML"],bl={key:2,id:"pointModal",class:"modal"},xl={class:"modal-content"},wl={class:"form-group"},Al={class:"form-group"},Ml={class:"form-group"},Tl={__name:"App",setup(e){const t=["对象015","对象022"],i=["对象007","对象012","Md_DianTi_006","对象031"],s=["对象006","对象011","对象021","对象030"],n=["对象005","对象010","对象020","对象029"],o=["对象004","对象013","对象019","对象028"],r=["对象003","对象014","对象018","对象027"],l=["对象002","对象009","对象017","对象026"],a=["对象001","对象008","对象016","对象025"],m=["Md_WuDing_006","Md_WuDing_005"];Cesium.Ion.defaultAccessToken="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJiOThlNzQ2Zi1jNWZhLTQwYmUtOWRiYi01Y2MzM2YyYzgxODMiLCJpZCI6MjU0NDE5LCJpYXQiOjE3MzEzMjU3OTV9.P4W0WtGb9-78VX5akp-p731bjn2XeUWwKueOHOlVduY";const d=ne(null);let h=null,_=null;const L=ne(120.194911),H=ne(33.352104),F=ne(55),te=ne(0),Q=ne(120.18057615702192),K=ne(33.34459424874954),U=ne(55),O=ne(1217.345908737083),J=ne(!1),Se=ne(""),fe=ne(!1),ye=ne(!1),xe=ne(null),z=ne({name:"",description:"",color:"YELLOW"});let he=null,de=null,pe=null,we=null;const gt=f=>{console.log(f),f.key==="w"&&h.camera.moveForward(10),f.key==="s"&&h.camera.moveBackward(10),f.key==="a"&&h.camera.moveLeft(10),f.key==="d"&&h.camera.moveRight(10),f.key==="e"&&h.camera.moveUp(10),f.key==="q"&&h.camera.moveDown(10);const C=h.camera.position,p=Cesium.Cartographic.fromCartesian(C),b=Cesium.Math.toDegrees(p.longitude),T=Cesium.Math.toDegrees(p.latitude),S=p.height;console.log(`longitude:${b},  latitude: ${T}, 高度: ${S}`)},Z=ne([{name:"标点1",longitude:120.1891222671583,latitude:33.35383753423389,height:62.426616633977744,labelText:"A1#西伏河菁英公寓",font:"500 30px Helvetica",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是A1#西伏河菁英公寓的详细信息。</p>"},{name:"标点2",longitude:120.18954221604284,latitude:33.35413596675203,height:62.426616633977744,labelText:"A2#西伏河菁英公寓",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层2的详细信息。</p>"},{name:"标点3",longitude:120.18948117113337,latitude:33.35330813501644,height:46.120346620566785,labelText:"B1#现代农业科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层3的详细信息。</p>"},{name:"标点4",longitude:120.19034916408134,latitude:33.35362420093073,height:35.23555939324899,labelText:"B2#未来产业培育中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层4的详细信息。</p>"},{name:"标点5",longitude:120.19004041902282,latitude:33.352841788303266,height:35.23555939324899,labelText:"B3#未来产业培育中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层5的详细信息。</p>"},{name:"标点6",longitude:120.1906372887605,latitude:33.35309779486584,height:35.23555939324899,labelText:"B4#光伏科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层6的详细信息。</p>"},{name:"标点7",longitude:120.18829716205744,latitude:33.35227913709945,height:41.25962076008002,labelText:"B5#氢能科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层7的详细信息。</p>"},{name:"标点8",longitude:120.18904148021537,latitude:33.35142431593994,height:41.25962076008002,labelText:"B6#储能科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层8的详细信息。</p>"},{name:"标点9",longitude:120.18983718178865,latitude:33.350653487205946,height:41.25962076008002,labelText:"B7#海洋经济科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层9的详细信息。</p>"},{name:"标点10",longitude:120.19046266145415,latitude:33.34974211313412,height:41.25962076008002,labelText:"B8#风电科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点11",longitude:120.19023587561483,latitude:33.35233355356273,height:41.25962076008002,labelText:"B9#江苏沿海零碳服务中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点12",longitude:120.19088448005887,latitude:33.35253998220558,height:41.25962076008002,labelText:"B10#中科院电工所到功率电力电子实验室",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点13",longitude:120.19080896797266,latitude:33.35166701022569,height:41.25962076008002,labelText:"B11#江苏可再生能源技术创新中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点14",longitude:120.19147190031437,latitude:33.3521318769472,height:41.25962076008002,labelText:"楼层14",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点15",longitude:120.19125063747177,latitude:33.35118226992003,height:41.25962076008002,labelText:"楼层15",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点16",longitude:120.1917953866177,latitude:33.350730614356515,height:41.25962076008002,labelText:"楼层16",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点17",longitude:120.19212424737852,latitude:33.35128999758356,height:94.15576542541079,labelText:"楼层17",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点18",longitude:120.1915056877939,latitude:33.349329946676534,height:22.23163099390385,labelText:"楼层18",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"}]);mn(async()=>{try{window.addEventListener("message",_e,!1);const f=await Cesium.createWorldTerrainAsync();h=new Cesium.Viewer(d.value,{timeline:!1,animation:!1,geocoder:!1,homeButton:!1,sceneModePicker:!1,baseLayerPicker:!1,navigationHelpButton:!1,infoBox:!1,selectionIndicator:!1,fullscreenButton:!1,shadows:!1,shouldAnimate:!0,terrainProvider:f}),await h.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(Q.value,K.value,O.value),orientation:{heading:Cesium.Math.toRadians(U.value),pitch:Cesium.Math.toRadians(-45),roll:0},duration:1}),_=await Ee(L.value,H.value,te.value,F.value);const C=Cesium.Cartesian3.fromDegreesArray([120.189771,33.354834,120.186688,33.352799,120.191011,33.347168,120.194683,33.349288,120.189771,33.354834]),p=h.entities.add({name:"立体墙效果",wall:{positions:C,maximumHeights:new Array(C.length).fill(50),minimunHeights:new Array(C.length).fill(0),material:new Cesium.Property.WallDiffuseMaterialProperty({color:new Cesium.Color.fromBytes(57.197,187),scanLineHeight:0})}});Z.value.forEach(T=>{Ze(T)}),j(),new Cesium.ScreenSpaceEventHandler(h.scene.canvas).setInputAction(function(T){const S=h.scene.pick(T.position);if(ye.value){const A=h.scene.pickPosition(T.position);if(Cesium.defined(A)){const I=Cesium.Cartographic.fromCartesian(A),P=Cesium.Math.toDegrees(I.longitude),V=Cesium.Math.toDegrees(I.latitude),G=I.height;console.log(`点击位置: 经度=${P}, 纬度=${V}, 高度=${G}`),xe.value={longitude:P,latitude:V,height:G},fe.value=!0;return}}if(Cesium.defined(S)&&Cesium.defined(S.id)){const A=S.id;if(A.name){const I=A.name,P=A.label?A.label.text.getValue(h.clock.currentTime):I;u({type:"cesiumEntityClicked",data:{name:I,labelText:P}})}if(A.position&&typeof A.position.getValue=="function"){const I=A.position.getValue(h.clock.currentTime);if(I){let P;A.boundingVolume?P=Cesium.BoundingSphere.fromBoundingVolume(A.boundingVolume,h.scene.globe.ellipsoid):P=new Cesium.BoundingSphere(I,100);const V=P.radius*2.5,G=h.camera.heading,X=Cesium.Math.toRadians(-45),Xe=new Cesium.HeadingPitchRange(G,X,V);h.flyTo(A,{duration:2,offset:Xe})}else console.warn(`实体 ${A.name} 没有有效的位置。`)}else console.warn(`实体 ${A.name} 没有有效的位置。`)}else J.value=!1},Cesium.ScreenSpaceEventType.LEFT_CLICK),document.addEventListener("keydown",gt),h.cesiumWidget&&h.cesiumWidget.creditContainer&&(h.cesiumWidget.creditContainer.style.display="none"),k()}catch(f){console.error("Cesium Viewer initialization failed:",f)}});function k(){if(!h){console.error("Viewer未初始化，无法创建测量工具");return}try{console.log("正在初始化测量工具..."),he=new gl(h),de=new ml(h),pe=new Cl(h),he&&he.MeasureEndEvent&&he.MeasureEndEvent.addEventListener(f=>{console.log("距离测量结果:",f,"米")}),de&&de.MeasureEndEvent&&de.MeasureEndEvent.addEventListener(f=>{console.log("高度测量结果:",f,"米")}),pe&&pe.MeasureEndEvent&&pe.MeasureEndEvent.addEventListener(f=>{console.log("面积测量结果:",f,"平方米")}),console.log("测量工具初始化完成")}catch(f){console.error("测量工具初始化失败:",f,f.stack),he=null,de=null,pe=null}}function j(){Z.value.forEach(f=>{console.log(`名称: ${f.name}`),console.log(`描述: ${f.description}`),console.log("-----------")})}Cn(()=>{h&&(h.destroy(),h=null),window.removeEventListener("message",_e),document.removeEventListener("keydown",gt)});const _e=f=>{if(!f.data||typeof f.data!="object")return;const{type:C,data:p}=f.data;console.log("[Cesium] 收到消息:",{type:C,data:p}),C==="cesiumCommand"&&lt(p)};function lt(f){const{command:C,params:p}=f||{};try{switch(C){case"jumpToLocation1":p&&p.camLongitude&&p.camLatitude&&Ct(p.camLongitude,p.camLatitude,p.camHeight||1e3);break;case"jumpToLocation0":Bt();break;case"jumpToLocation2":ze();break;case"jumpToLocation3":Wt();break;case"toggleFloor":(p==null?void 0:p.floor)!==void 0&&(console.log(`[Cesium] 切换到楼层: ${p.floor}`),mt(Ae("reset")),p.floor!=="reset"&&Vt(Ae(p.floor)));break;case"resetFloor":console.log("[Cesium] 重置所有楼层"),mt(Ae("reset"));break;case"jumpToEntity":p!=null&&p.entityName&&Qt(p.entityName);break;case"addPoint":console.log("[Cesium] 接收到添加标签命令"),(p==null?void 0:p.action)==="activate"&&M();break;case"distanceMeasure":console.log("[Cesium] 接收到距离测量命令"),(p==null?void 0:p.action)==="activate"&&g();break;case"heightMeasure":console.log("[Cesium] 接收到高度测量命令"),(p==null?void 0:p.action)==="activate"&&E();break;case"areaMeasure":console.log("[Cesium] 接收到面积测量命令"),(p==null?void 0:p.action)==="activate"&&v();break;case"clearMeasure":console.log("[Cesium] 接收到清除测量命令"),y();break;default:console.warn(`[Cesium] 未识别的命令: ${C}`)}}catch(b){console.error(`[Cesium] 命令执行失败: ${C}`,b)}}function Ae(f){return console.log(`[Cesium] 获取楼层节点: ${f}`),{0:[...t],1:[...i,...s,...n,...o,...r,...l,...a,...m],2:[...s,...n,...o,...r,...l,...a,...m],3:[...n,...o,...r,...l,...a,...m],4:[...o,...r,...l,...a,...m],5:[...r,...l,...a,...m],6:[...l,...a,...m],7:[...a,...m],all:[...t,...i,...s,...n,...o,...r,...l,...a,...m],reset:[...t,...i,...s,...n,...o,...r,...l,...a,...m]}[f]||[]}async function Ee(f,C,p,b){const T=Cesium.Cartesian3.fromDegrees(f,C,p),S=Cesium.Math.toRadians(b),A=Cesium.Math.toRadians(0),I=Cesium.Math.toRadians(0),P=new Cesium.HeadingPitchRoll(S,A,I),V=Cesium.Transforms.headingPitchRollToFixedFrame(T,P),G=await Cesium.Model.fromGltfAsync({url:"model/xifu3.glb",modelMatrix:V,minimumPixelSize:64,maximumScale:2e4,scale:.5});return h.scene.primitives.add(G),G}function Vt(f){mt([...i,...s,...n,...o,...r,...l,...a,...m]),f.forEach(C=>{pi(C)})}function pi(f){const C=_.getNode(f);let p=20,b=0;const T=p/(1*60),S=Cesium.Matrix4.clone(C.matrix);function A(){b<p?b+=T:(b=p,h.scene.preRender.removeEventListener(A));const I=Cesium.Cartesian3.fromElements(0,0,b),P=Cesium.Matrix4.fromTranslation(I);C.matrix=Cesium.Matrix4.multiply(S,P,new Cesium.Matrix4)}h.scene.preRender.addEventListener(A)}function mt(f){f.forEach(C=>{qe(C)})}function qe(f){const C=_.getNode(f);C.matrix=Cesium.Matrix4.clone(C.originalMatrix)}function Ze(f){const{longitude:C,latitude:p,height:b,labelText:T,font:S,labelColor:A,iconImage:I,description:P}=f;return h.entities.add({name:f.name,position:Cesium.Cartesian3.fromDegrees(C,p,b),label:{text:T,font:S,scale:.5,style:Cesium.LabelStyle.FILL,fillColor:A,pixelOffset:new Cesium.Cartesian2(0,-45),distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,1500)},billboard:{width:20,height:20,image:I,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,verticalOrigin:Cesium.VerticalOrigin.BOTTOM,distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,2e3)},description:P||""})}function Ct(f,C,p){h.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(f,C,p),orientation:{heading:Cesium.Math.toRadians(U.value),pitch:Cesium.Math.toRadians(-45),roll:0},duration:2})}function Bt(){h.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(120.18057615702192,33.34459424874954,1217.3459087375948),orientation:{heading:Cesium.Math.toRadians(U.value),pitch:Cesium.Math.toRadians(-45),roll:0},duration:2})}function ze(){h.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(120.18521125510068,33.347694998604126,631.0488822977416),orientation:{heading:Cesium.Math.toRadians(U.value),pitch:Cesium.Math.toRadians(-45),roll:0},duration:2})}function Wt(){h.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(120.18749039820405,33.351791030928524,118.79728977059871),orientation:{heading:Cesium.Math.toRadians(U.value),pitch:Cesium.Math.toRadians(-45),roll:0},duration:2})}function Qt(f){console.log(`[Cesium] 尝试跳转到实体: ${f}`);let C=c(f);if(!C){if(console.log("[Cesium] 未找到精确匹配实体，尝试查找相似名称..."),f.includes("-")&&f.includes("#")){const p=f.split("#"),T=p[0].replace(/-/g,"")+"#"+(p[1]||"");console.log(`[Cesium] 尝试查找替代名称: ${T}`),C=c(T)}if(!C&&/\d+/.test(f)){const p=f.match(/\d+/g);if(p&&p.length>0){console.log(`[Cesium] 尝试查找包含数字 ${p[0]} 的实体`);const b=h.entities.values;for(let T=0;T<b.length;T++){const S=b[T];if(S.label&&S.label.text){const A=S.label.text.getValue(h.clock.currentTime);if(A.includes(p[0])){console.log(`[Cesium] 找到包含相同数字的实体: ${A}`),C=S;break}}}}}}if(C){console.log("[Cesium] 找到实体，准备跳转...");const p=C.position.getValue(h.clock.currentTime);if(p){const T=new Cesium.BoundingSphere(p,100).radius*2.5,S=h.camera.heading,A=Cesium.Math.toRadians(-45),I=new Cesium.HeadingPitchRange(S,A,T);return h.flyTo(C,{duration:2,offset:I}),console.log(`[Cesium] 成功跳转到实体: ${C.label.text.getValue(h.clock.currentTime)}`),!0}else console.error("[Cesium] 实体没有有效位置")}else{console.warn("[Cesium] 未找到实体，尝试使用默认视角...");const p=h.entities.values;let b=null,T=0;for(let S=0;S<p.length;S++){const A=p[S];if(A.label&&A.label.text){const I=A.label.text.getValue(h.clock.currentTime);let P=0;const V=f.toLowerCase(),G=I.toLowerCase();for(let X=0;X<V.length;X++)G.includes(V[X])&&P++;P>T&&(T=P,b=A)}}if(b&&T>f.length/2){console.log(`[Cesium] 找到最佳匹配实体: ${b.label.text.getValue(h.clock.currentTime)}`);const S=b.position.getValue(h.clock.currentTime);if(S){const I=new Cesium.BoundingSphere(S,100).radius*2.5,P=h.camera.heading,V=Cesium.Math.toRadians(-45),G=new Cesium.HeadingPitchRange(P,V,I);return h.flyTo(b,{duration:2,offset:G}),console.log("[Cesium] 成功跳转到最佳匹配实体"),!0}}else console.error("[Cesium] 未找到任何匹配实体，无法跳转")}return console.warn(`[Cesium] 未找到实体或无法跳转: ${f}`),!1}function c(f){console.log(`[Cesium] 开始查找实体: "${f}"`);const C=f.toLowerCase().replace(/\s+/g,""),p=[],b=h.entities.values;console.log(`[Cesium] 实体总数: ${b.length}`);for(let S=0;S<b.length;S++){const A=b[S];if(A.label&&A.label.text){const I=A.label.text.getValue(h.clock.currentTime);if(p.push(I),I.toLowerCase().replace(/\s+/g,""),I===f)return console.log(`[Cesium] 找到完全匹配: "${I}"`),A}}for(let S=0;S<b.length;S++){const A=b[S];if(A.label&&A.label.text){const I=A.label.text.getValue(h.clock.currentTime),P=I.toLowerCase().replace(/\s+/g,"");if(P.includes(C))return console.log(`[Cesium] 找到包含匹配: "${I}" 包含 "${f}"`),A;if(C.includes(P))return console.log(`[Cesium] 找到反向包含匹配: "${f}" 包含 "${I}"`),A}}const T=C.replace(/-/g,"");for(let S=0;S<b.length;S++){const A=b[S];if(A.label&&A.label.text){const I=A.label.text.getValue(h.clock.currentTime),P=I.toLowerCase().replace(/\s+/g,"").replace(/-/g,"");if(P.includes(T)||T.includes(P))return console.log(`[Cesium] 找到移除连字符后的匹配: "${I}" 与 "${f}"`),A}}if(C.includes("#")){const A=C.split("#")[0].replace(/-/g,"");for(let I=0;I<b.length;I++){const P=b[I];if(P.label&&P.label.text){const V=P.label.text.getValue(h.clock.currentTime);if(V.toLowerCase().replace(/\s+/g,"").includes(A+"#"))return console.log(`[Cesium] 找到前缀匹配: "${V}" 与 "${f}"`),P}}}return console.log("[Cesium] 未找到匹配实体。可用的实体标签:",p),null}function u(f){try{window.parent!==window?(window.parent.postMessage(f,"*"),console.log("[Cesium] 已发送消息到父窗口:",f)):console.log("[Cesium] 不在iframe中，无法发送消息:",f)}catch(C){console.error("[Cesium] 发送消息失败:",C)}}function g(){h&&he?(y(),he.activate(),we=he):console.error("距离测量工具未初始化")}function E(){h&&de?(y(),de.activate(),we=de):console.error("高度测量工具未初始化")}function v(){h&&pe?(y(),pe.activate(),we=pe):console.error("面积测量工具未初始化")}function y(){we&&(we.deactivate(),we.clear(),we=null)}function M(){y(),ye.value=!0,h&&h.container&&(h.container.style.cursor="crosshair")}function w(){fe.value=!1,ye.value=!1,xe.value=null,z.value={name:"",description:"",color:"YELLOW"},h&&h.container&&(h.container.style.cursor="default")}function x(){if(!xe.value){alert("位置无效，请重新选择");return}if(!z.value.name){alert("请输入标签名称");return}const f={name:`标点_${Z.value.length+1}`,longitude:xe.value.longitude,latitude:xe.value.latitude,height:xe.value.height,labelText:z.value.name,font:"400 24px Arial",labelColor:Cesium.Color[z.value.color],iconImage:$,description:z.value.description||""};Z.value.push(f),Ze(f),w(),console.log("已添加自定义标签:",f)}return(f,C)=>(Gt(),Ai("div",yl,[Mi("",!0),ie("div",{id:"cesiumContainer",ref_key:"cesiumContainer",ref:d},null,512),J.value?(Gt(),Ai("div",{key:1,id:"infoBox",innerHTML:Se.value},null,8,El)):Mi("",!0),fe.value?(Gt(),Ai("div",bl,[ie("div",xl,[C[25]||(C[25]=ie("h3",null,"添加自定义标签",-1)),ie("div",wl,[C[21]||(C[21]=ie("label",{for:"pointName"},"标签名称:",-1)),yi(ie("input",{type:"text",id:"pointName","onUpdate:modelValue":C[12]||(C[12]=p=>z.value.name=p)},null,512),[[Vs,z.value.name]])]),ie("div",Al,[C[22]||(C[22]=ie("label",{for:"pointDesc"},"标签描述:",-1)),yi(ie("textarea",{id:"pointDesc","onUpdate:modelValue":C[13]||(C[13]=p=>z.value.description=p)},null,512),[[Vs,z.value.description]])]),ie("div",Ml,[C[24]||(C[24]=ie("label",{for:"pointColor"},"标签颜色:",-1)),yi(ie("select",{id:"pointColor","onUpdate:modelValue":C[14]||(C[14]=p=>z.value.color=p)},C[23]||(C[23]=[Or('<option value="YELLOW" data-v-dcb76ed1>黄色</option><option value="RED" data-v-dcb76ed1>红色</option><option value="GREEN" data-v-dcb76ed1>绿色</option><option value="BLUE" data-v-dcb76ed1>蓝色</option><option value="WHITE" data-v-dcb76ed1>白色</option>',5)]),512),[[al,z.value.color]])]),ie("div",{class:"modal-buttons"},[ie("button",{onClick:x},"保存"),ie("button",{onClick:w},"取消")])])])):Mi("",!0)]))}},Sl=vl(Tl,[["__scopeId","data-v-dcb76ed1"]]);hl(Sl).mount("#app");
