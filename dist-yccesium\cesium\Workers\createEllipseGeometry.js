/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.123.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as o}from"./chunk-SVKJ7LU4.js";import"./chunk-R64NBSVA.js";import"./chunk-IKCC3Y2P.js";import"./chunk-6PSYLVHH.js";import"./chunk-AXZROGOU.js";import"./chunk-FBGWLBZF.js";import"./chunk-DUJALUGY.js";import"./chunk-E3JWUR3Y.js";import"./chunk-NDAGFHEH.js";import"./chunk-UONIHL6J.js";import"./chunk-MMUM2K3W.js";import"./chunk-KYQV7GBQ.js";import"./chunk-QAZHJDBR.js";import"./chunk-DHJSNIID.js";import"./chunk-TZA2EBXA.js";import"./chunk-N2BNZIW6.js";import{a as t,d as c}from"./chunk-HCEU6S2D.js";import"./chunk-CU56QGQW.js";import"./chunk-D6INCCYP.js";import"./chunk-2M3RUB3T.js";import"./chunk-FA3XILPW.js";import"./chunk-IEHYVM5Q.js";import{e as n}from"./chunk-RT4BZSKE.js";function a(r,i){return n(i)&&(r=o.unpack(r,i)),r._center=t.clone(r._center),r._ellipsoid=c.clone(r._ellipsoid),o.createGeometry(r)}var u=a;export{u as default};
