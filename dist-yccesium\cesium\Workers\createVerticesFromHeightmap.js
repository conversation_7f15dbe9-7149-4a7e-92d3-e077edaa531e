/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.123.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as sr,b as fr}from"./chunk-FBYPWC3D.js";import{a as tr}from"./chunk-S2ETKY43.js";import{a as Ue}from"./chunk-VTROAMAT.js";import{a as ar}from"./chunk-WPL65D7Y.js";import"./chunk-AXZROGOU.js";import"./chunk-3DTIN3PX.js";import{a as nr}from"./chunk-54256N4H.js";import"./chunk-NDAGFHEH.js";import"./chunk-UONIHL6J.js";import{d as ir}from"./chunk-DHJSNIID.js";import{b as xe,g as rr,i as ae}from"./chunk-TZA2EBXA.js";import"./chunk-N2BNZIW6.js";import{a as ie,c as er,d as Ie}from"./chunk-HCEU6S2D.js";import{a as ge}from"./chunk-CU56QGQW.js";import"./chunk-D6INCCYP.js";import{a as He}from"./chunk-2M3RUB3T.js";import{a as Y}from"./chunk-FA3XILPW.js";import{a as he}from"./chunk-IEHYVM5Q.js";import{c as Ir,d as xr,e as G}from"./chunk-RT4BZSKE.js";var ur=Ir((qr,Te)=>{/* Copyright 2015-2018 Esri. Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 @preserve */(function(){var D=function(){var A={};A.defaultNoDataValue=-34027999387901484e22,A.decode=function(r,a){a=a||{};var t=a.encodedMaskData||a.encodedMaskData===null,i=n(r,a.inputOffset||0,t),o=a.noDataValue!==null?a.noDataValue:A.defaultNoDataValue,s=V(i,a.pixelType||Float32Array,a.encodedMaskData,o,a.returnMask),u={width:i.width,height:i.height,pixelData:s.resultPixels,minValue:s.minValue,maxValue:i.pixels.maxValue,noDataValue:o};return s.resultMask&&(u.maskData=s.resultMask),a.returnEncodedMask&&i.mask&&(u.encodedMaskData=i.mask.bitset?i.mask.bitset:null),a.returnFileInfo&&(u.fileInfo=b(i),a.computeUsedBitDepths&&(u.fileInfo.bitDepths=X(i))),u};var V=function(r,a,t,i,o){var s=0,u=r.pixels.numBlocksX,m=r.pixels.numBlocksY,l=Math.floor(r.width/u),f=Math.floor(r.height/m),h=2*r.maxZError,c=Number.MAX_VALUE,g;t=t||(r.mask?r.mask.bitset:null);var v,U;v=new a(r.width*r.height),o&&t&&(U=new Uint8Array(r.width*r.height));for(var M=new Float32Array(l*f),S,I,k=0;k<=m;k++){var w=k!==m?f:r.height%m;if(w!==0)for(var d=0;d<=u;d++){var x=d!==u?l:r.width%u;if(x!==0){var T=k*r.width*f+d*l,y=r.width-x,p=r.pixels.blocks[s],L,B,E;p.encoding<2?(p.encoding===0?L=p.rawData:(e(p.stuffedData,p.bitsPerPixel,p.numValidPixels,p.offset,h,M,r.pixels.maxValue),L=M),B=0):p.encoding===2?E=0:E=p.offset;var O;if(t)for(I=0;I<w;I++){for(T&7&&(O=t[T>>3],O<<=T&7),S=0;S<x;S++)T&7||(O=t[T>>3]),O&128?(U&&(U[T]=1),g=p.encoding<2?L[B++]:E,c=c>g?g:c,v[T++]=g):(U&&(U[T]=0),v[T++]=i),O<<=1;T+=y}else if(p.encoding<2)for(I=0;I<w;I++){for(S=0;S<x;S++)g=L[B++],c=c>g?g:c,v[T++]=g;T+=y}else for(c=c>E?E:c,I=0;I<w;I++){for(S=0;S<x;S++)v[T++]=E;T+=y}if(p.encoding===1&&B!==p.numValidPixels)throw"Block and Mask do not match";s++}}}return{resultPixels:v,resultMask:U,minValue:c}},b=function(r){return{fileIdentifierString:r.fileIdentifierString,fileVersion:r.fileVersion,imageType:r.imageType,height:r.height,width:r.width,maxZError:r.maxZError,eofOffset:r.eofOffset,mask:r.mask?{numBlocksX:r.mask.numBlocksX,numBlocksY:r.mask.numBlocksY,numBytes:r.mask.numBytes,maxValue:r.mask.maxValue}:null,pixels:{numBlocksX:r.pixels.numBlocksX,numBlocksY:r.pixels.numBlocksY,numBytes:r.pixels.numBytes,maxValue:r.pixels.maxValue,noDataValue:r.noDataValue}}},X=function(r){for(var a=r.pixels.numBlocksX*r.pixels.numBlocksY,t={},i=0;i<a;i++){var o=r.pixels.blocks[i];o.encoding===0?t.float32=!0:o.encoding===1?t[o.bitsPerPixel]=!0:t[0]=!0}return Object.keys(t)},n=function(r,a,t){var i={},o=new Uint8Array(r,a,10);if(i.fileIdentifierString=String.fromCharCode.apply(null,o),i.fileIdentifierString.trim()!=="CntZImage")throw"Unexpected file identifier string: "+i.fileIdentifierString;a+=10;var s=new DataView(r,a,24);if(i.fileVersion=s.getInt32(0,!0),i.imageType=s.getInt32(4,!0),i.height=s.getUint32(8,!0),i.width=s.getUint32(12,!0),i.maxZError=s.getFloat64(16,!0),a+=24,!t)if(s=new DataView(r,a,16),i.mask={},i.mask.numBlocksY=s.getUint32(0,!0),i.mask.numBlocksX=s.getUint32(4,!0),i.mask.numBytes=s.getUint32(8,!0),i.mask.maxValue=s.getFloat32(12,!0),a+=16,i.mask.numBytes>0){var u=new Uint8Array(Math.ceil(i.width*i.height/8));s=new DataView(r,a,i.mask.numBytes);var m=s.getInt16(0,!0),l=2,f=0;do{if(m>0)for(;m--;)u[f++]=s.getUint8(l++);else{var h=s.getUint8(l++);for(m=-m;m--;)u[f++]=h}m=s.getInt16(l,!0),l+=2}while(l<i.mask.numBytes);if(m!==-32768||f<u.length)throw"Unexpected end of mask RLE encoding";i.mask.bitset=u,a+=i.mask.numBytes}else i.mask.numBytes|i.mask.numBlocksY|i.mask.maxValue||(i.mask.bitset=new Uint8Array(Math.ceil(i.width*i.height/8)));s=new DataView(r,a,16),i.pixels={},i.pixels.numBlocksY=s.getUint32(0,!0),i.pixels.numBlocksX=s.getUint32(4,!0),i.pixels.numBytes=s.getUint32(8,!0),i.pixels.maxValue=s.getFloat32(12,!0),a+=16;var c=i.pixels.numBlocksX,g=i.pixels.numBlocksY,v=c+(i.width%c>0?1:0),U=g+(i.height%g>0?1:0);i.pixels.blocks=new Array(v*U);for(var M=0,S=0;S<U;S++)for(var I=0;I<v;I++){var k=0,w=r.byteLength-a;s=new DataView(r,a,Math.min(10,w));var d={};i.pixels.blocks[M++]=d;var x=s.getUint8(0);if(k++,d.encoding=x&63,d.encoding>3)throw"Invalid block encoding ("+d.encoding+")";if(d.encoding===2){a++;continue}if(x!==0&&x!==2){if(x>>=6,d.offsetType=x,x===2)d.offset=s.getInt8(1),k++;else if(x===1)d.offset=s.getInt16(1,!0),k+=2;else if(x===0)d.offset=s.getFloat32(1,!0),k+=4;else throw"Invalid block offset type";if(d.encoding===1)if(x=s.getUint8(k),k++,d.bitsPerPixel=x&63,x>>=6,d.numValidPixelsType=x,x===2)d.numValidPixels=s.getUint8(k),k++;else if(x===1)d.numValidPixels=s.getUint16(k,!0),k+=2;else if(x===0)d.numValidPixels=s.getUint32(k,!0),k+=4;else throw"Invalid valid pixel count type"}if(a+=k,d.encoding!==3){var T,y;if(d.encoding===0){var p=(i.pixels.numBytes-1)/4;if(p!==Math.floor(p))throw"uncompressed block has invalid length";T=new ArrayBuffer(p*4),y=new Uint8Array(T),y.set(new Uint8Array(r,a,p*4));var L=new Float32Array(T);d.rawData=L,a+=p*4}else if(d.encoding===1){var B=Math.ceil(d.numValidPixels*d.bitsPerPixel/8),E=Math.ceil(B/4);T=new ArrayBuffer(E*4),y=new Uint8Array(T),y.set(new Uint8Array(r,a,B)),d.stuffedData=new Uint32Array(T),a+=B}}}return i.eofOffset=a,i},e=function(r,a,t,i,o,s,u){var m=(1<<a)-1,l=0,f,h=0,c,g,v=Math.ceil((u-i)/o),U=r.length*4-Math.ceil(a*t/8);for(r[r.length-1]<<=8*U,f=0;f<t;f++){if(h===0&&(g=r[l++],h=32),h>=a)c=g>>>h-a&m,h-=a;else{var M=a-h;c=(g&m)<<M&m,g=r[l++],h=32-M,c+=g>>>h}s[f]=c<v?i+c*o:u}return s};return A}(),se=function(){"use strict";var A={unstuff:function(n,e,r,a,t,i,o,s){var u=(1<<r)-1,m=0,l,f=0,h,c,g,v,U=n.length*4-Math.ceil(r*a/8);if(n[n.length-1]<<=8*U,t)for(l=0;l<a;l++)f===0&&(c=n[m++],f=32),f>=r?(h=c>>>f-r&u,f-=r):(g=r-f,h=(c&u)<<g&u,c=n[m++],f=32-g,h+=c>>>f),e[l]=t[h];else for(v=Math.ceil((s-i)/o),l=0;l<a;l++)f===0&&(c=n[m++],f=32),f>=r?(h=c>>>f-r&u,f-=r):(g=r-f,h=(c&u)<<g&u,c=n[m++],f=32-g,h+=c>>>f),e[l]=h<v?i+h*o:s},unstuffLUT:function(n,e,r,a,t,i){var o=(1<<e)-1,s=0,u=0,m=0,l=0,f=0,h,c=[],g=n.length*4-Math.ceil(e*r/8);n[n.length-1]<<=8*g;var v=Math.ceil((i-a)/t);for(u=0;u<r;u++)l===0&&(h=n[s++],l=32),l>=e?(f=h>>>l-e&o,l-=e):(m=e-l,f=(h&o)<<m&o,h=n[s++],l=32-m,f+=h>>>l),c[u]=f<v?a+f*t:i;return c.unshift(a),c},unstuff2:function(n,e,r,a,t,i,o,s){var u=(1<<r)-1,m=0,l,f=0,h=0,c,g,v;if(t)for(l=0;l<a;l++)f===0&&(g=n[m++],f=32,h=0),f>=r?(c=g>>>h&u,f-=r,h+=r):(v=r-f,c=g>>>h&u,g=n[m++],f=32-v,c|=(g&(1<<v)-1)<<r-v,h=v),e[l]=t[c];else{var U=Math.ceil((s-i)/o);for(l=0;l<a;l++)f===0&&(g=n[m++],f=32,h=0),f>=r?(c=g>>>h&u,f-=r,h+=r):(v=r-f,c=g>>>h&u,g=n[m++],f=32-v,c|=(g&(1<<v)-1)<<r-v,h=v),e[l]=c<U?i+c*o:s}return e},unstuffLUT2:function(n,e,r,a,t,i){var o=(1<<e)-1,s=0,u=0,m=0,l=0,f=0,h=0,c,g=[],v=Math.ceil((i-a)/t);for(u=0;u<r;u++)l===0&&(c=n[s++],l=32,h=0),l>=e?(f=c>>>h&o,l-=e,h+=e):(m=e-l,f=c>>>h&o,c=n[s++],l=32-m,f|=(c&(1<<m)-1)<<e-m,h=m),g[u]=f<v?a+f*t:i;return g.unshift(a),g},originalUnstuff:function(n,e,r,a){var t=(1<<r)-1,i=0,o,s=0,u,m,l,f=n.length*4-Math.ceil(r*a/8);for(n[n.length-1]<<=8*f,o=0;o<a;o++)s===0&&(m=n[i++],s=32),s>=r?(u=m>>>s-r&t,s-=r):(l=r-s,u=(m&t)<<l&t,m=n[i++],s=32-l,u+=m>>>s),e[o]=u;return e},originalUnstuff2:function(n,e,r,a){var t=(1<<r)-1,i=0,o,s=0,u=0,m,l,f;for(o=0;o<a;o++)s===0&&(l=n[i++],s=32,u=0),s>=r?(m=l>>>u&t,s-=r,u+=r):(f=r-s,m=l>>>u&t,l=n[i++],s=32-f,m|=(l&(1<<f)-1)<<r-f,u=f),e[o]=m;return e}},V={HUFFMAN_LUT_BITS_MAX:12,computeChecksumFletcher32:function(n){for(var e=65535,r=65535,a=n.length,t=Math.floor(a/2),i=0;t;){var o=t>=359?359:t;t-=o;do e+=n[i++]<<8,r+=e+=n[i++];while(--o);e=(e&65535)+(e>>>16),r=(r&65535)+(r>>>16)}return a&1&&(r+=e+=n[i]<<8),e=(e&65535)+(e>>>16),r=(r&65535)+(r>>>16),(r<<16|e)>>>0},readHeaderInfo:function(n,e){var r=e.ptr,a=new Uint8Array(n,r,6),t={};if(t.fileIdentifierString=String.fromCharCode.apply(null,a),t.fileIdentifierString.lastIndexOf("Lerc2",0)!==0)throw"Unexpected file identifier string (expect Lerc2 ): "+t.fileIdentifierString;r+=6;var i=new DataView(n,r,8),o=i.getInt32(0,!0);t.fileVersion=o,r+=4,o>=3&&(t.checksum=i.getUint32(4,!0),r+=4),i=new DataView(n,r,12),t.height=i.getUint32(0,!0),t.width=i.getUint32(4,!0),r+=8,o>=4?(t.numDims=i.getUint32(8,!0),r+=4):t.numDims=1,i=new DataView(n,r,40),t.numValidPixel=i.getUint32(0,!0),t.microBlockSize=i.getInt32(4,!0),t.blobSize=i.getInt32(8,!0),t.imageType=i.getInt32(12,!0),t.maxZError=i.getFloat64(16,!0),t.zMin=i.getFloat64(24,!0),t.zMax=i.getFloat64(32,!0),r+=40,e.headerInfo=t,e.ptr=r;var s,u;if(o>=3&&(u=o>=4?52:48,s=this.computeChecksumFletcher32(new Uint8Array(n,r-u,t.blobSize-14)),s!==t.checksum))throw"Checksum failed.";return!0},checkMinMaxRanges:function(n,e){var r=e.headerInfo,a=this.getDataTypeArray(r.imageType),t=r.numDims*this.getDataTypeSize(r.imageType),i=this.readSubArray(n,e.ptr,a,t),o=this.readSubArray(n,e.ptr+t,a,t);e.ptr+=2*t;var s,u=!0;for(s=0;s<r.numDims;s++)if(i[s]!==o[s]){u=!1;break}return r.minValues=i,r.maxValues=o,u},readSubArray:function(n,e,r,a){var t;if(r===Uint8Array)t=new Uint8Array(n,e,a);else{var i=new ArrayBuffer(a),o=new Uint8Array(i);o.set(new Uint8Array(n,e,a)),t=new r(i)}return t},readMask:function(n,e){var r=e.ptr,a=e.headerInfo,t=a.width*a.height,i=a.numValidPixel,o=new DataView(n,r,4),s={};if(s.numBytes=o.getUint32(0,!0),r+=4,(i===0||t===i)&&s.numBytes!==0)throw"invalid mask";var u,m;if(i===0)u=new Uint8Array(Math.ceil(t/8)),s.bitset=u,m=new Uint8Array(t),e.pixels.resultMask=m,r+=s.numBytes;else if(s.numBytes>0){u=new Uint8Array(Math.ceil(t/8)),o=new DataView(n,r,s.numBytes);var l=o.getInt16(0,!0),f=2,h=0,c=0;do{if(l>0)for(;l--;)u[h++]=o.getUint8(f++);else for(c=o.getUint8(f++),l=-l;l--;)u[h++]=c;l=o.getInt16(f,!0),f+=2}while(f<s.numBytes);if(l!==-32768||h<u.length)throw"Unexpected end of mask RLE encoding";m=new Uint8Array(t);var g=0,v=0;for(v=0;v<t;v++)v&7?(g=u[v>>3],g<<=v&7):g=u[v>>3],g&128&&(m[v]=1);e.pixels.resultMask=m,s.bitset=u,r+=s.numBytes}return e.ptr=r,e.mask=s,!0},readDataOneSweep:function(n,e,r){var a=e.ptr,t=e.headerInfo,i=t.numDims,o=t.width*t.height,s=t.imageType,u=t.numValidPixel*V.getDataTypeSize(s)*i,m,l=e.pixels.resultMask;if(r===Uint8Array)m=new Uint8Array(n,a,u);else{var f=new ArrayBuffer(u),h=new Uint8Array(f);h.set(new Uint8Array(n,a,u)),m=new r(f)}if(m.length===o*i)e.pixels.resultPixels=m;else{e.pixels.resultPixels=new r(o*i);var c=0,g=0,v=0,U=0;if(i>1)for(v=0;v<i;v++)for(U=v*o,g=0;g<o;g++)l[g]&&(e.pixels.resultPixels[U+g]=m[c++]);else for(g=0;g<o;g++)l[g]&&(e.pixels.resultPixels[g]=m[c++])}return a+=u,e.ptr=a,!0},readHuffmanTree:function(n,e){var r=this.HUFFMAN_LUT_BITS_MAX,a=new DataView(n,e.ptr,16);e.ptr+=16;var t=a.getInt32(0,!0);if(t<2)throw"unsupported Huffman version";var i=a.getInt32(4,!0),o=a.getInt32(8,!0),s=a.getInt32(12,!0);if(o>=s)return!1;var u=new Uint32Array(s-o);V.decodeBits(n,e,u);var m=[],l,f,h,c;for(l=o;l<s;l++)f=l-(l<i?0:i),m[f]={first:u[l-o],second:null};var g=n.byteLength-e.ptr,v=Math.ceil(g/4),U=new ArrayBuffer(v*4),M=new Uint8Array(U);M.set(new Uint8Array(n,e.ptr,g));var S=new Uint32Array(U),I=0,k,w=0;for(k=S[0],l=o;l<s;l++)f=l-(l<i?0:i),c=m[f].first,c>0&&(m[f].second=k<<I>>>32-c,32-I>=c?(I+=c,I===32&&(I=0,w++,k=S[w])):(I+=c-32,w++,k=S[w],m[f].second|=k>>>32-I));var d=0,x=0,T=new b;for(l=0;l<m.length;l++)m[l]!==void 0&&(d=Math.max(d,m[l].first));d>=r?x=r:x=d,d>=30&&console.log("WARning, large NUM LUT BITS IS "+d);var y=[],p,L,B,E,O,F;for(l=o;l<s;l++)if(f=l-(l<i?0:i),c=m[f].first,c>0)if(p=[c,f],c<=x)for(L=m[f].second<<x-c,B=1<<x-c,h=0;h<B;h++)y[L|h]=p;else for(L=m[f].second,F=T,E=c-1;E>=0;E--)O=L>>>E&1,O?(F.right||(F.right=new b),F=F.right):(F.left||(F.left=new b),F=F.left),E===0&&!F.val&&(F.val=p[1]);return{decodeLut:y,numBitsLUTQick:x,numBitsLUT:d,tree:T,stuffedData:S,srcPtr:w,bitPos:I}},readHuffman:function(n,e,r){var a=e.headerInfo,t=a.numDims,i=e.headerInfo.height,o=e.headerInfo.width,s=o*i,u=this.readHuffmanTree(n,e),m=u.decodeLut,l=u.tree,f=u.stuffedData,h=u.srcPtr,c=u.bitPos,g=u.numBitsLUTQick,v=u.numBitsLUT,U=e.headerInfo.imageType===0?128:0,M,S,I,k=e.pixels.resultMask,w,d,x,T,y,p,L,B=0;c>0&&(h++,c=0);var E=f[h],O=e.encodeMode===1,F=new r(s*t),N=F,z;for(z=0;z<a.numDims;z++){if(t>1&&(N=new r(F.buffer,s*z,s),B=0),e.headerInfo.numValidPixel===o*i)for(p=0,T=0;T<i;T++)for(y=0;y<o;y++,p++){if(S=0,w=E<<c>>>32-g,d=w,32-c<g&&(w|=f[h+1]>>>64-c-g,d=w),m[d])S=m[d][1],c+=m[d][0];else for(w=E<<c>>>32-v,d=w,32-c<v&&(w|=f[h+1]>>>64-c-v,d=w),M=l,L=0;L<v;L++)if(x=w>>>v-L-1&1,M=x?M.right:M.left,!(M.left||M.right)){S=M.val,c=c+L+1;break}c>=32&&(c-=32,h++,E=f[h]),I=S-U,O?(y>0?I+=B:T>0?I+=N[p-o]:I+=B,I&=255,N[p]=I,B=I):N[p]=I}else for(p=0,T=0;T<i;T++)for(y=0;y<o;y++,p++)if(k[p]){if(S=0,w=E<<c>>>32-g,d=w,32-c<g&&(w|=f[h+1]>>>64-c-g,d=w),m[d])S=m[d][1],c+=m[d][0];else for(w=E<<c>>>32-v,d=w,32-c<v&&(w|=f[h+1]>>>64-c-v,d=w),M=l,L=0;L<v;L++)if(x=w>>>v-L-1&1,M=x?M.right:M.left,!(M.left||M.right)){S=M.val,c=c+L+1;break}c>=32&&(c-=32,h++,E=f[h]),I=S-U,O?(y>0&&k[p-1]?I+=B:T>0&&k[p-o]?I+=N[p-o]:I+=B,I&=255,N[p]=I,B=I):N[p]=I}e.ptr=e.ptr+(h+1)*4+(c>0?4:0)}e.pixels.resultPixels=F},decodeBits:function(n,e,r,a,t){{var i=e.headerInfo,o=i.fileVersion,s=0,u=new DataView(n,e.ptr,5),m=u.getUint8(0);s++;var l=m>>6,f=l===0?4:3-l,h=(m&32)>0,c=m&31,g=0;if(f===1)g=u.getUint8(s),s++;else if(f===2)g=u.getUint16(s,!0),s+=2;else if(f===4)g=u.getUint32(s,!0),s+=4;else throw"Invalid valid pixel count type";var v=2*i.maxZError,U,M,S,I,k,w,d,x,T,y,p=i.numDims>1?i.maxValues[t]:i.zMax;if(h){for(e.counter.lut++,x=u.getUint8(s),T=c,s++,I=Math.ceil((x-1)*c/8),k=Math.ceil(I/4),M=new ArrayBuffer(k*4),S=new Uint8Array(M),e.ptr+=s,S.set(new Uint8Array(n,e.ptr,I)),d=new Uint32Array(M),e.ptr+=I,y=0;x-1>>>y;)y++;I=Math.ceil(g*y/8),k=Math.ceil(I/4),M=new ArrayBuffer(k*4),S=new Uint8Array(M),S.set(new Uint8Array(n,e.ptr,I)),U=new Uint32Array(M),e.ptr+=I,o>=3?w=A.unstuffLUT2(d,c,x-1,a,v,p):w=A.unstuffLUT(d,c,x-1,a,v,p),o>=3?A.unstuff2(U,r,y,g,w):A.unstuff(U,r,y,g,w)}else e.counter.bitstuffer++,y=c,e.ptr+=s,y>0&&(I=Math.ceil(g*y/8),k=Math.ceil(I/4),M=new ArrayBuffer(k*4),S=new Uint8Array(M),S.set(new Uint8Array(n,e.ptr,I)),U=new Uint32Array(M),e.ptr+=I,o>=3?a==null?A.originalUnstuff2(U,r,y,g):A.unstuff2(U,r,y,g,!1,a,v,p):a==null?A.originalUnstuff(U,r,y,g):A.unstuff(U,r,y,g,!1,a,v,p))}},readTiles:function(n,e,r){var a=e.headerInfo,t=a.width,i=a.height,o=a.microBlockSize,s=a.imageType,u=V.getDataTypeSize(s),m=Math.ceil(t/o),l=Math.ceil(i/o);e.pixels.numBlocksY=l,e.pixels.numBlocksX=m,e.pixels.ptr=0;var f=0,h=0,c=0,g=0,v=0,U=0,M=0,S=0,I=0,k=0,w=0,d=0,x=0,T=0,y=0,p=0,L,B,E,O,F,N,z=new r(o*o),ke=i%o||o,ye=t%o||o,J,Z,ue=a.numDims,te,j=e.pixels.resultMask,q=e.pixels.resultPixels;for(c=0;c<l;c++)for(v=c!==l-1?o:ke,g=0;g<m;g++)for(U=g!==m-1?o:ye,w=c*t*o+g*o,d=t-U,te=0;te<ue;te++){if(ue>1&&(q=new r(e.pixels.resultPixels.buffer,t*i*te*u,t*i)),M=n.byteLength-e.ptr,L=new DataView(n,e.ptr,Math.min(10,M)),B={},p=0,S=L.getUint8(0),p++,I=S>>6&255,k=S>>2&15,k!==(g*o>>3&15))throw"integrity issue";if(N=S&3,N>3)throw e.ptr+=p,"Invalid block encoding ("+N+")";if(N===2){e.counter.constant++,e.ptr+=p;continue}else if(N===0){if(e.counter.uncompressed++,e.ptr+=p,x=v*U*u,T=n.byteLength-e.ptr,x=x<T?x:T,E=new ArrayBuffer(x%u===0?x:x+u-x%u),O=new Uint8Array(E),O.set(new Uint8Array(n,e.ptr,x)),F=new r(E),y=0,j)for(f=0;f<v;f++){for(h=0;h<U;h++)j[w]&&(q[w]=F[y++]),w++;w+=d}else for(f=0;f<v;f++){for(h=0;h<U;h++)q[w++]=F[y++];w+=d}e.ptr+=y*u}else if(J=V.getDataTypeUsed(s,I),Z=V.getOnePixel(B,p,J,L),p+=V.getDataTypeSize(J),N===3)if(e.ptr+=p,e.counter.constantoffset++,j)for(f=0;f<v;f++){for(h=0;h<U;h++)j[w]&&(q[w]=Z),w++;w+=d}else for(f=0;f<v;f++){for(h=0;h<U;h++)q[w++]=Z;w+=d}else if(e.ptr+=p,V.decodeBits(n,e,z,Z,te),p=0,j)for(f=0;f<v;f++){for(h=0;h<U;h++)j[w]&&(q[w]=z[p++]),w++;w+=d}else for(f=0;f<v;f++){for(h=0;h<U;h++)q[w++]=z[p++];w+=d}}},formatFileInfo:function(n){return{fileIdentifierString:n.headerInfo.fileIdentifierString,fileVersion:n.headerInfo.fileVersion,imageType:n.headerInfo.imageType,height:n.headerInfo.height,width:n.headerInfo.width,numValidPixel:n.headerInfo.numValidPixel,microBlockSize:n.headerInfo.microBlockSize,blobSize:n.headerInfo.blobSize,maxZError:n.headerInfo.maxZError,pixelType:V.getPixelType(n.headerInfo.imageType),eofOffset:n.eofOffset,mask:n.mask?{numBytes:n.mask.numBytes}:null,pixels:{numBlocksX:n.pixels.numBlocksX,numBlocksY:n.pixels.numBlocksY,maxValue:n.headerInfo.zMax,minValue:n.headerInfo.zMin,noDataValue:n.noDataValue}}},constructConstantSurface:function(n){var e=n.headerInfo.zMax,r=n.headerInfo.numDims,a=n.headerInfo.height*n.headerInfo.width,t=a*r,i=0,o=0,s=0,u=n.pixels.resultMask;if(u)if(r>1)for(i=0;i<r;i++)for(s=i*a,o=0;o<a;o++)u[o]&&(n.pixels.resultPixels[s+o]=e);else for(o=0;o<a;o++)u[o]&&(n.pixels.resultPixels[o]=e);else if(n.pixels.resultPixels.fill)n.pixels.resultPixels.fill(e);else for(o=0;o<t;o++)n.pixels.resultPixels[o]=e},getDataTypeArray:function(n){var e;switch(n){case 0:e=Int8Array;break;case 1:e=Uint8Array;break;case 2:e=Int16Array;break;case 3:e=Uint16Array;break;case 4:e=Int32Array;break;case 5:e=Uint32Array;break;case 6:e=Float32Array;break;case 7:e=Float64Array;break;default:e=Float32Array}return e},getPixelType:function(n){var e;switch(n){case 0:e="S8";break;case 1:e="U8";break;case 2:e="S16";break;case 3:e="U16";break;case 4:e="S32";break;case 5:e="U32";break;case 6:e="F32";break;case 7:e="F64";break;default:e="F32"}return e},isValidPixelValue:function(n,e){if(e==null)return!1;var r;switch(n){case 0:r=e>=-128&&e<=127;break;case 1:r=e>=0&&e<=255;break;case 2:r=e>=-32768&&e<=32767;break;case 3:r=e>=0&&e<=65536;break;case 4:r=e>=-2147483648&&e<=2147483647;break;case 5:r=e>=0&&e<=4294967296;break;case 6:r=e>=-34027999387901484e22&&e<=34027999387901484e22;break;case 7:r=e>=5e-324&&e<=17976931348623157e292;break;default:r=!1}return r},getDataTypeSize:function(n){var e=0;switch(n){case 0:case 1:e=1;break;case 2:case 3:e=2;break;case 4:case 5:case 6:e=4;break;case 7:e=8;break;default:e=n}return e},getDataTypeUsed:function(n,e){var r=n;switch(n){case 2:case 4:r=n-e;break;case 3:case 5:r=n-2*e;break;case 6:e===0?r=n:e===1?r=2:r=1;break;case 7:e===0?r=n:r=n-2*e+1;break;default:r=n;break}return r},getOnePixel:function(n,e,r,a){var t=0;switch(r){case 0:t=a.getInt8(e);break;case 1:t=a.getUint8(e);break;case 2:t=a.getInt16(e,!0);break;case 3:t=a.getUint16(e,!0);break;case 4:t=a.getInt32(e,!0);break;case 5:t=a.getUInt32(e,!0);break;case 6:t=a.getFloat32(e,!0);break;case 7:t=a.getFloat64(e,!0);break;default:throw"the decoder does not understand this pixel type"}return t}},b=function(n,e,r){this.val=n,this.left=e,this.right=r},X={decode:function(n,e){e=e||{};var r=e.noDataValue,a=0,t={};if(t.ptr=e.inputOffset||0,t.pixels={},!!V.readHeaderInfo(n,t)){var i=t.headerInfo,o=i.fileVersion,s=V.getDataTypeArray(i.imageType);V.readMask(n,t),i.numValidPixel!==i.width*i.height&&!t.pixels.resultMask&&(t.pixels.resultMask=e.maskData);var u=i.width*i.height;if(t.pixels.resultPixels=new s(u*i.numDims),t.counter={onesweep:0,uncompressed:0,lut:0,bitstuffer:0,constant:0,constantoffset:0},i.numValidPixel!==0)if(i.zMax===i.zMin)V.constructConstantSurface(t);else if(o>=4&&V.checkMinMaxRanges(n,t))V.constructConstantSurface(t);else{var m=new DataView(n,t.ptr,2),l=m.getUint8(0);if(t.ptr++,l)V.readDataOneSweep(n,t,s);else if(o>1&&i.imageType<=1&&Math.abs(i.maxZError-.5)<1e-5){var f=m.getUint8(1);if(t.ptr++,t.encodeMode=f,f>2||o<4&&f>1)throw"Invalid Huffman flag "+f;f?V.readHuffman(n,t,s):V.readTiles(n,t,s)}else V.readTiles(n,t,s)}t.eofOffset=t.ptr;var h;e.inputOffset?(h=t.headerInfo.blobSize+e.inputOffset-t.ptr,Math.abs(h)>=1&&(t.eofOffset=e.inputOffset+t.headerInfo.blobSize)):(h=t.headerInfo.blobSize-t.ptr,Math.abs(h)>=1&&(t.eofOffset=t.headerInfo.blobSize));var c={width:i.width,height:i.height,pixelData:t.pixels.resultPixels,minValue:i.zMin,maxValue:i.zMax,validPixelCount:i.numValidPixel,dimCount:i.numDims,dimStats:{minValues:i.minValues,maxValues:i.maxValues},maskData:t.pixels.resultMask};if(t.pixels.resultMask&&V.isValidPixelValue(i.imageType,r)){var g=t.pixels.resultMask;for(a=0;a<u;a++)g[a]||(c.pixelData[a]=r);c.noDataValue=r}return t.noDataValue=r,e.returnFileInfo&&(c.fileInfo=V.formatFileInfo(t)),c}},getBandCount:function(n){var e=0,r=0,a={};for(a.ptr=0,a.pixels={};r<n.byteLength-58;)V.readHeaderInfo(n,a),r+=a.headerInfo.blobSize,e++,a.ptr=r;return e}};return X}(),C=function(){var A=new ArrayBuffer(4),V=new Uint8Array(A),b=new Uint32Array(A);return b[0]=1,V[0]===1}(),re={decode:function(A,V){if(!C)throw"Big endian system is not supported.";V=V||{};var b=V.inputOffset||0,X=new Uint8Array(A,b,10),n=String.fromCharCode.apply(null,X),e,r;if(n.trim()==="CntZImage")e=D,r=1;else if(n.substring(0,5)==="Lerc2")e=se,r=2;else throw"Unexpected file identifier string: "+n;for(var a=0,t=A.byteLength-10,i,o=[],s,u,m={width:0,height:0,pixels:[],pixelType:V.pixelType,mask:null,statistics:[]};b<t;){var l=e.decode(A,{inputOffset:b,encodedMaskData:i,maskData:u,returnMask:a===0,returnEncodedMask:a===0,returnFileInfo:!0,pixelType:V.pixelType||null,noDataValue:V.noDataValue||null});b=l.fileInfo.eofOffset,a===0&&(i=l.encodedMaskData,u=l.maskData,m.width=l.width,m.height=l.height,m.dimCount=l.dimCount||1,m.pixelType=l.pixelType||l.fileInfo.pixelType,m.mask=l.maskData),r>1&&l.fileInfo.mask&&l.fileInfo.mask.numBytes>0&&o.push(l.maskData),a++,m.pixels.push(l.pixelData),m.statistics.push({minValue:l.minValue,maxValue:l.maxValue,noDataValue:l.noDataValue,dimStats:l.dimStats})}var f,h,c;if(r>1&&o.length>1){for(c=m.width*m.height,m.bandMasks=o,u=new Uint8Array(c),u.set(o[0]),f=1;f<o.length;f++)for(s=o[f],h=0;h<c;h++)u[h]=u[h]&s[h];m.maskData=u}return m}};typeof define=="function"&&define.amd?define([],function(){return re}):typeof Te<"u"&&Te.exports?Te.exports=re:this.Lerc=re})()});var Ur={NONE:0,LERC:1},or=Object.freeze(Ur);var Q={};Q.DEFAULT_STRUCTURE=Object.freeze({heightScale:1,heightOffset:0,elementsPerHeight:1,stride:1,elementMultiplier:256,isBigEndian:!1});var Ye=new ie,Tr=new xe,kr=new ie,yr=new ie;Q.computeVertices=function(D){if(!G(D)||!G(D.heightmap))throw new he("options.heightmap is required.");if(!G(D.width)||!G(D.height))throw new he("options.width and options.height are required.");if(!G(D.nativeRectangle))throw new he("options.nativeRectangle is required.");if(!G(D.skirtHeight))throw new he("options.skirtHeight is required.");let se=Math.cos,C=Math.sin,re=Math.sqrt,A=Math.atan,V=Math.exp,b=ge.PI_OVER_TWO,X=ge.toRadians,n=D.heightmap,e=D.width,r=D.height,a=D.skirtHeight,t=a>0,i=Y(D.isGeographic,!0),o=Y(D.ellipsoid,Ie.default),s=1/o.maximumRadius,u=ae.clone(D.nativeRectangle),m=ae.clone(D.rectangle),l,f,h,c;G(m)?(l=m.west,f=m.south,h=m.east,c=m.north):i?(l=X(u.west),f=X(u.south),h=X(u.east),c=X(u.north)):(l=u.west*s,f=b-2*A(V(-u.south*s)),h=u.east*s,c=b-2*A(V(-u.north*s)));let g=D.relativeToCenter,v=G(g);g=v?g:ie.ZERO;let U=Y(D.includeWebMercatorT,!1),M=Y(D.exaggeration,1),S=Y(D.exaggerationRelativeHeight,0),k=M!==1,w=Y(D.structure,Q.DEFAULT_STRUCTURE),d=Y(w.heightScale,Q.DEFAULT_STRUCTURE.heightScale),x=Y(w.heightOffset,Q.DEFAULT_STRUCTURE.heightOffset),T=Y(w.elementsPerHeight,Q.DEFAULT_STRUCTURE.elementsPerHeight),y=Y(w.stride,Q.DEFAULT_STRUCTURE.stride),p=Y(w.elementMultiplier,Q.DEFAULT_STRUCTURE.elementMultiplier),L=Y(w.isBigEndian,Q.DEFAULT_STRUCTURE.isBigEndian),B=ae.computeWidth(u),E=ae.computeHeight(u),O=B/(e-1),F=E/(r-1);i||(B*=s,E*=s);let N=o.radiiSquared,z=N.x,ke=N.y,ye=N.z,J=65536,Z=-65536,ue=rr.eastNorthUpToFixedFrame(g,o),te=xe.inverseTransformation(ue,Tr),j,q;U&&(j=Ue.geodeticLatitudeToMercatorAngle(f),q=1/(Ue.geodeticLatitudeToMercatorAngle(c)-j));let fe=kr;fe.x=Number.POSITIVE_INFINITY,fe.y=Number.POSITIVE_INFINITY,fe.z=Number.POSITIVE_INFINITY;let oe=yr;oe.x=Number.NEGATIVE_INFINITY,oe.y=Number.NEGATIVE_INFINITY,oe.z=Number.NEGATIVE_INFINITY;let Me=Number.POSITIVE_INFINITY,ce=e*r,mr=a>0?e*2+r*2:0,ne=ce+mr,ve=new Array(ne),Xe=new Array(ne),_e=new Array(ne),ze=U?new Array(ne):[],Ze=k?new Array(ne):[],Se=0,Ve=r,De=0,Ae=e;t&&(--Se,++Ve,--De,++Ae);let de=1e-5;for(let H=Se;H<Ve;++H){let K=H;K<0&&(K=0),K>=r&&(K=r-1);let W=u.north-F*K;i?W=X(W):W=b-2*A(V(-W*s));let Le=(W-f)/(c-f);Le=ge.clamp(Le,0,1);let we=H===Se,pe=H===Ve-1;a>0&&(we?W+=de*E:pe&&(W-=de*E));let Qe=se(W),Be=C(W),Je=ye*Be,Ke;U&&(Ke=(Ue.geodeticLatitudeToMercatorAngle(W)-j)*q);for(let me=De;me<Ae;++me){let $=me;$<0&&($=0),$>=e&&($=e-1);let Fe=K*(e*y)+$*y,R;if(T===1)R=n[Fe];else{R=0;let _;if(L)for(_=0;_<T;++_)R=R*p+n[Fe+_];else for(_=T-1;_>=0;--_)R=R*p+n[Fe+_]}R=R*d+x,Z=Math.max(Z,R),J=Math.min(J,R);let P=u.west+O*$;i?P=X(P):P=P*s;let be=(P-l)/(h-l);be=ge.clamp(be,0,1);let ee=K*e+$;if(a>0){let _=me===De,Re=me===Ae-1,pr=we||pe||_||Re;if((we||pe)&&(_||Re))continue;pr&&(R-=a,_?(ee=ce+(r-K-1),P-=de*B):pe?ee=ce+r+(e-$-1):Re?(ee=ce+r+e+K,P+=de*B):we&&(ee=ce+r+e+r+$))}let Ne=Qe*se(P),Ce=Qe*C(P),$e=z*Ne,Pe=ke*Ce,Oe=1/re($e*Ne+Pe*Ce+Je*Be),vr=$e*Oe,dr=Pe*Oe,wr=Je*Oe,le=new ie;le.x=vr+Ne*R,le.y=dr+Ce*R,le.z=wr+Be*R,xe.multiplyByPoint(te,le,Ye),ie.minimumByComponent(Ye,fe,fe),ie.maximumByComponent(Ye,oe,oe),Me=Math.min(Me,R),ve[ee]=le,_e[ee]=new er(be,Le),Xe[ee]=R,U&&(ze[ee]=Ke),k&&(Ze[ee]=o.geodeticSurfaceNormal(le))}}let hr=ir.fromPoints(ve),je;G(m)&&(je=ar.fromRectangle(m,J,Z,o));let qe;v&&(qe=new sr(o).computeHorizonCullingPointPossiblyUnderEllipsoid(g,ve,J));let gr=new nr(fe,oe,g),Ee=new fr(g,gr,Me,Z,ue,!1,U,k,M,S),We=new Float32Array(ne*Ee.stride),Ge=0;for(let H=0;H<ne;++H)Ge=Ee.encode(We,Ge,ve[H],_e[H],Xe[H],void 0,ze[H],Ze[H]);return{vertices:We,maximumHeight:Z,minimumHeight:J,encoding:Ee,boundingSphere3D:hr,orientedBoundingBox:je,occludeePointInScaledSpace:qe}};var lr=Q;var cr=xr(ur(),1);function Mr(D,se){if(D.encoding===or.LERC){let A;try{A=cr.default.decode(D.heightmap)}catch(b){throw new He(b)}if(A.statistics[0].minValue===Number.MAX_VALUE)throw new He("Invalid tile data");D.heightmap=A.pixels[0],D.width=A.width,D.height=A.height}D.ellipsoid=Ie.clone(D.ellipsoid),D.rectangle=ae.clone(D.rectangle);let C=lr.computeVertices(D),re=C.vertices;return se.push(re.buffer),{vertices:re.buffer,numberOfAttributes:C.encoding.stride,minimumHeight:C.minimumHeight,maximumHeight:C.maximumHeight,gridWidth:D.width,gridHeight:D.height,boundingSphere3D:C.boundingSphere3D,orientedBoundingBox:C.orientedBoundingBox,occludeePointInScaledSpace:C.occludeePointInScaledSpace,encoding:C.encoding,westIndicesSouthToNorth:C.westIndicesSouthToNorth,southIndicesEastToWest:C.southIndicesEastToWest,eastIndicesNorthToSouth:C.eastIndicesNorthToSouth,northIndicesWestToEast:C.northIndicesWestToEast}}var Pr=tr(Mr);export{Pr as default};
