/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.123.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as e}from"./chunk-BO2GXTRN.js";import{a as r}from"./chunk-S2ETKY43.js";import"./chunk-VTROAMAT.js";import"./chunk-6PSYLVHH.js";import"./chunk-AXZROGOU.js";import"./chunk-FBGWLBZF.js";import"./chunk-NDAGFHEH.js";import"./chunk-UONIHL6J.js";import"./chunk-MMUM2K3W.js";import"./chunk-KYQV7GBQ.js";import"./chunk-QAZHJDBR.js";import"./chunk-DHJSNIID.js";import"./chunk-TZA2EBXA.js";import"./chunk-N2BNZIW6.js";import"./chunk-HCEU6S2D.js";import"./chunk-CU56QGQW.js";import"./chunk-D6INCCYP.js";import"./chunk-2M3RUB3T.js";import"./chunk-FA3XILPW.js";import"./chunk-IEHYVM5Q.js";import"./chunk-RT4BZSKE.js";function n(o,t){let m=e.unpackCombineGeometryParameters(o),i=e.combineGeometry(m);return e.packCombineGeometryResults(i,t)}var c=r(n);export{c as default};
