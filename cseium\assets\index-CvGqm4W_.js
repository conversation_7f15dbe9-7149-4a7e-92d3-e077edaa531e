(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const o of n)if(o.type==="childList")for(const r of o.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&s(r)}).observe(document,{childList:!0,subtree:!0});function i(n){const o={};return n.integrity&&(o.integrity=n.integrity),n.referrerPolicy&&(o.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?o.credentials="include":n.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(n){if(n.ep)return;n.ep=!0;const o=i(n);fetch(n.href,o)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Bi(e){const t=Object.create(null);for(const i of e.split(","))t[i]=1;return i=>i in t}const k={},ut=[],Re=()=>{},Qn=()=>!1,ni=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Wi=e=>e.startsWith("onUpdate:"),ue=Object.assign,Qi=(e,t)=>{const i=e.indexOf(t);i>-1&&e.splice(i,1)},Un=Object.prototype.hasOwnProperty,W=(e,t)=>Un.call(e,t),R=Array.isArray,bt=e=>Rt(e)==="[object Map]",Ui=e=>Rt(e)==="[object Set]",rs=e=>Rt(e)==="[object Date]",F=e=>typeof e=="function",te=e=>typeof e=="string",Ke=e=>typeof e=="symbol",Z=e=>e!==null&&typeof e=="object",Vs=e=>(Z(e)||F(e))&&F(e.then)&&F(e.catch),jn=Object.prototype.toString,Rt=e=>jn.call(e),kn=e=>Rt(e).slice(8,-1),Yn=e=>Rt(e)==="[object Object]",ji=e=>te(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,xt=Bi(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),oi=e=>{const t=Object.create(null);return i=>t[i]||(t[i]=e(i))},Nn=/-(\w)/g,Ge=oi(e=>e.replace(Nn,(t,i)=>i?i.toUpperCase():"")),Kn=/\B([A-Z])/g,rt=oi(e=>e.replace(Kn,"-$1").toLowerCase()),Bs=oi(e=>e.charAt(0).toUpperCase()+e.slice(1)),gi=oi(e=>e?`on${Bs(e)}`:""),Ne=(e,t)=>!Object.is(e,t),kt=(e,...t)=>{for(let i=0;i<e.length;i++)e[i](...t)},Ws=(e,t,i,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:i})},qt=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ls;const ri=()=>ls||(ls=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ki(e){if(R(e)){const t={};for(let i=0;i<e.length;i++){const s=e[i],n=te(s)?Zn(s):ki(s);if(n)for(const o in n)t[o]=n[o]}return t}else if(te(e)||Z(e))return e}const Gn=/;(?![^(]*\))/g,Jn=/:([^]+)/,qn=/\/\*[^]*?\*\//g;function Zn(e){const t={};return e.replace(qn,"").split(Gn).forEach(i=>{if(i){const s=i.split(Jn);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Yi(e){let t="";if(te(e))t=e;else if(R(e))for(let i=0;i<e.length;i++){const s=Yi(e[i]);s&&(t+=s+" ")}else if(Z(e))for(const i in e)e[i]&&(t+=i+" ");return t.trim()}const zn="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Xn=Bi(zn);function Qs(e){return!!e||e===""}function $n(e,t){if(e.length!==t.length)return!1;let i=!0;for(let s=0;i&&s<e.length;s++)i=li(e[s],t[s]);return i}function li(e,t){if(e===t)return!0;let i=rs(e),s=rs(t);if(i||s)return i&&s?e.getTime()===t.getTime():!1;if(i=Ke(e),s=Ke(t),i||s)return e===t;if(i=R(e),s=R(t),i||s)return i&&s?$n(e,t):!1;if(i=Z(e),s=Z(t),i||s){if(!i||!s)return!1;const n=Object.keys(e).length,o=Object.keys(t).length;if(n!==o)return!1;for(const r in e){const l=e.hasOwnProperty(r),a=t.hasOwnProperty(r);if(l&&!a||!l&&a||!li(e[r],t[r]))return!1}}return String(e)===String(t)}function eo(e,t){return e.findIndex(i=>li(i,t))}/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ce;class to{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ce,!t&&Ce&&(this.index=(Ce.scopes||(Ce.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,i;if(this.scopes)for(t=0,i=this.scopes.length;t<i;t++)this.scopes[t].pause();for(t=0,i=this.effects.length;t<i;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,i;if(this.scopes)for(t=0,i=this.scopes.length;t<i;t++)this.scopes[t].resume();for(t=0,i=this.effects.length;t<i;t++)this.effects[t].resume()}}run(t){if(this._active){const i=Ce;try{return Ce=this,t()}finally{Ce=i}}}on(){Ce=this}off(){Ce=this.parent}stop(t){if(this._active){this._active=!1;let i,s;for(i=0,s=this.effects.length;i<s;i++)this.effects[i].stop();for(this.effects.length=0,i=0,s=this.cleanups.length;i<s;i++)this.cleanups[i]();if(this.cleanups.length=0,this.scopes){for(i=0,s=this.scopes.length;i<s;i++)this.scopes[i].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function io(){return Ce}let N;const mi=new WeakSet;class Us{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ce&&Ce.active&&Ce.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,mi.has(this)&&(mi.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ks(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,cs(this),Ys(this);const t=N,i=Te;N=this,Te=!0;try{return this.fn()}finally{Ns(this),N=t,Te=i,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Gi(t);this.deps=this.depsTail=void 0,cs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?mi.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Si(this)&&this.run()}get dirty(){return Si(this)}}let js=0,wt,At;function ks(e,t=!1){if(e.flags|=8,t){e.next=At,At=e;return}e.next=wt,wt=e}function Ni(){js++}function Ki(){if(--js>0)return;if(At){let t=At;for(At=void 0;t;){const i=t.next;t.next=void 0,t.flags&=-9,t=i}}let e;for(;wt;){let t=wt;for(wt=void 0;t;){const i=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=i}}if(e)throw e}function Ys(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ns(e){let t,i=e.depsTail,s=i;for(;s;){const n=s.prevDep;s.version===-1?(s===i&&(i=n),Gi(s),so(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=n}e.deps=t,e.depsTail=i}function Si(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ks(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ks(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===It))return;e.globalVersion=It;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Si(e)){e.flags&=-3;return}const i=N,s=Te;N=e,Te=!0;try{Ys(e);const n=e.fn(e._value);(t.version===0||Ne(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{N=i,Te=s,Ns(e),e.flags&=-3}}function Gi(e,t=!1){const{dep:i,prevSub:s,nextSub:n}=e;if(s&&(s.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=s,e.nextSub=void 0),i.subs===e&&(i.subs=s,!s&&i.computed)){i.computed.flags&=-5;for(let o=i.computed.deps;o;o=o.nextDep)Gi(o,!0)}!t&&!--i.sc&&i.map&&i.map.delete(i.key)}function so(e){const{prevDep:t,nextDep:i}=e;t&&(t.nextDep=i,e.prevDep=void 0),i&&(i.prevDep=t,e.nextDep=void 0)}let Te=!0;const Gs=[];function Je(){Gs.push(Te),Te=!1}function qe(){const e=Gs.pop();Te=e===void 0?!0:e}function cs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const i=N;N=void 0;try{t()}finally{N=i}}}let It=0;class no{constructor(t,i){this.sub=t,this.dep=i,this.version=i.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ji{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!N||!Te||N===this.computed)return;let i=this.activeLink;if(i===void 0||i.sub!==N)i=this.activeLink=new no(N,this),N.deps?(i.prevDep=N.depsTail,N.depsTail.nextDep=i,N.depsTail=i):N.deps=N.depsTail=i,Js(i);else if(i.version===-1&&(i.version=this.version,i.nextDep)){const s=i.nextDep;s.prevDep=i.prevDep,i.prevDep&&(i.prevDep.nextDep=s),i.prevDep=N.depsTail,i.nextDep=void 0,N.depsTail.nextDep=i,N.depsTail=i,N.deps===i&&(N.deps=s)}return i}trigger(t){this.version++,It++,this.notify(t)}notify(t){Ni();try{for(let i=this.subs;i;i=i.prevSub)i.sub.notify()&&i.sub.dep.notify()}finally{Ki()}}}function Js(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Js(s)}const i=e.dep.subs;i!==e&&(e.prevSub=i,i&&(i.nextSub=e)),e.dep.subs=e}}const _i=new WeakMap,st=Symbol(""),Ii=Symbol(""),Lt=Symbol("");function ne(e,t,i){if(Te&&N){let s=_i.get(e);s||_i.set(e,s=new Map);let n=s.get(i);n||(s.set(i,n=new Ji),n.map=s,n.key=i),n.track()}}function Qe(e,t,i,s,n,o){const r=_i.get(e);if(!r){It++;return}const l=a=>{a&&a.trigger()};if(Ni(),t==="clear")r.forEach(l);else{const a=R(e),m=a&&ji(i);if(a&&i==="length"){const h=Number(s);r.forEach((f,S)=>{(S==="length"||S===Lt||!Ke(S)&&S>=h)&&l(f)})}else switch((i!==void 0||r.has(void 0))&&l(r.get(i)),m&&l(r.get(Lt)),t){case"add":a?m&&l(r.get("length")):(l(r.get(st)),bt(e)&&l(r.get(Ii)));break;case"delete":a||(l(r.get(st)),bt(e)&&l(r.get(Ii)));break;case"set":bt(e)&&l(r.get(st));break}}Ki()}function ct(e){const t=B(e);return t===e?t:(ne(t,"iterate",Lt),Se(e)?t:t.map(le))}function qi(e){return ne(e=B(e),"iterate",Lt),e}const oo={__proto__:null,[Symbol.iterator](){return Ci(this,Symbol.iterator,le)},concat(...e){return ct(this).concat(...e.map(t=>R(t)?ct(t):t))},entries(){return Ci(this,"entries",e=>(e[1]=le(e[1]),e))},every(e,t){return Ve(this,"every",e,t,void 0,arguments)},filter(e,t){return Ve(this,"filter",e,t,i=>i.map(le),arguments)},find(e,t){return Ve(this,"find",e,t,le,arguments)},findIndex(e,t){return Ve(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ve(this,"findLast",e,t,le,arguments)},findLastIndex(e,t){return Ve(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ve(this,"forEach",e,t,void 0,arguments)},includes(...e){return vi(this,"includes",e)},indexOf(...e){return vi(this,"indexOf",e)},join(e){return ct(this).join(e)},lastIndexOf(...e){return vi(this,"lastIndexOf",e)},map(e,t){return Ve(this,"map",e,t,void 0,arguments)},pop(){return vt(this,"pop")},push(...e){return vt(this,"push",e)},reduce(e,...t){return as(this,"reduce",e,t)},reduceRight(e,...t){return as(this,"reduceRight",e,t)},shift(){return vt(this,"shift")},some(e,t){return Ve(this,"some",e,t,void 0,arguments)},splice(...e){return vt(this,"splice",e)},toReversed(){return ct(this).toReversed()},toSorted(e){return ct(this).toSorted(e)},toSpliced(...e){return ct(this).toSpliced(...e)},unshift(...e){return vt(this,"unshift",e)},values(){return Ci(this,"values",le)}};function Ci(e,t,i){const s=qi(e),n=s[t]();return s!==e&&!Se(e)&&(n._next=n.next,n.next=()=>{const o=n._next();return o.value&&(o.value=i(o.value)),o}),n}const ro=Array.prototype;function Ve(e,t,i,s,n,o){const r=qi(e),l=r!==e&&!Se(e),a=r[t];if(a!==ro[t]){const f=a.apply(e,o);return l?le(f):f}let m=i;r!==e&&(l?m=function(f,S){return i.call(this,le(f),S,e)}:i.length>2&&(m=function(f,S){return i.call(this,f,S,e)}));const h=a.call(r,m,s);return l&&n?n(h):h}function as(e,t,i,s){const n=qi(e);let o=i;return n!==e&&(Se(e)?i.length>3&&(o=function(r,l,a){return i.call(this,r,l,a,e)}):o=function(r,l,a){return i.call(this,r,le(l),a,e)}),n[t](o,...s)}function vi(e,t,i){const s=B(e);ne(s,"iterate",Lt);const n=s[t](...i);return(n===-1||n===!1)&&$i(i[0])?(i[0]=B(i[0]),s[t](...i)):n}function vt(e,t,i=[]){Je(),Ni();const s=B(e)[t].apply(e,i);return Ki(),qe(),s}const lo=Bi("__proto__,__v_isRef,__isVue"),qs=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ke));function co(e){Ke(e)||(e=String(e));const t=B(this);return ne(t,"has",e),t.hasOwnProperty(e)}class Zs{constructor(t=!1,i=!1){this._isReadonly=t,this._isShallow=i}get(t,i,s){if(i==="__v_skip")return t.__v_skip;const n=this._isReadonly,o=this._isShallow;if(i==="__v_isReactive")return!n;if(i==="__v_isReadonly")return n;if(i==="__v_isShallow")return o;if(i==="__v_raw")return s===(n?o?yo:en:o?$s:Xs).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const r=R(t);if(!n){let a;if(r&&(a=oo[i]))return a;if(i==="hasOwnProperty")return co}const l=Reflect.get(t,i,oe(t)?t:s);return(Ke(i)?qs.has(i):lo(i))||(n||ne(t,"get",i),o)?l:oe(l)?r&&ji(i)?l:l.value:Z(l)?n?tn(l):zi(l):l}}class zs extends Zs{constructor(t=!1){super(!1,t)}set(t,i,s,n){let o=t[i];if(!this._isShallow){const a=nt(o);if(!Se(s)&&!nt(s)&&(o=B(o),s=B(s)),!R(t)&&oe(o)&&!oe(s))return a?!1:(o.value=s,!0)}const r=R(t)&&ji(i)?Number(i)<t.length:W(t,i),l=Reflect.set(t,i,s,oe(t)?t:n);return t===B(n)&&(r?Ne(s,o)&&Qe(t,"set",i,s):Qe(t,"add",i,s)),l}deleteProperty(t,i){const s=W(t,i);t[i];const n=Reflect.deleteProperty(t,i);return n&&s&&Qe(t,"delete",i,void 0),n}has(t,i){const s=Reflect.has(t,i);return(!Ke(i)||!qs.has(i))&&ne(t,"has",i),s}ownKeys(t){return ne(t,"iterate",R(t)?"length":st),Reflect.ownKeys(t)}}class ao extends Zs{constructor(t=!1){super(!0,t)}set(t,i){return!0}deleteProperty(t,i){return!0}}const uo=new zs,fo=new ao,ho=new zs(!0);const Li=e=>e,Qt=e=>Reflect.getPrototypeOf(e);function po(e,t,i){return function(...s){const n=this.__v_raw,o=B(n),r=bt(o),l=e==="entries"||e===Symbol.iterator&&r,a=e==="keys"&&r,m=n[e](...s),h=i?Li:t?Oi:le;return!t&&ne(o,"iterate",a?Ii:st),{next(){const{value:f,done:S}=m.next();return S?{value:f,done:S}:{value:l?[h(f[0]),h(f[1])]:h(f),done:S}},[Symbol.iterator](){return this}}}}function Ut(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function go(e,t){const i={get(n){const o=this.__v_raw,r=B(o),l=B(n);e||(Ne(n,l)&&ne(r,"get",n),ne(r,"get",l));const{has:a}=Qt(r),m=t?Li:e?Oi:le;if(a.call(r,n))return m(o.get(n));if(a.call(r,l))return m(o.get(l));o!==r&&o.get(n)},get size(){const n=this.__v_raw;return!e&&ne(B(n),"iterate",st),Reflect.get(n,"size",n)},has(n){const o=this.__v_raw,r=B(o),l=B(n);return e||(Ne(n,l)&&ne(r,"has",n),ne(r,"has",l)),n===l?o.has(n):o.has(n)||o.has(l)},forEach(n,o){const r=this,l=r.__v_raw,a=B(l),m=t?Li:e?Oi:le;return!e&&ne(a,"iterate",st),l.forEach((h,f)=>n.call(o,m(h),m(f),r))}};return ue(i,e?{add:Ut("add"),set:Ut("set"),delete:Ut("delete"),clear:Ut("clear")}:{add(n){!t&&!Se(n)&&!nt(n)&&(n=B(n));const o=B(this);return Qt(o).has.call(o,n)||(o.add(n),Qe(o,"add",n,n)),this},set(n,o){!t&&!Se(o)&&!nt(o)&&(o=B(o));const r=B(this),{has:l,get:a}=Qt(r);let m=l.call(r,n);m||(n=B(n),m=l.call(r,n));const h=a.call(r,n);return r.set(n,o),m?Ne(o,h)&&Qe(r,"set",n,o):Qe(r,"add",n,o),this},delete(n){const o=B(this),{has:r,get:l}=Qt(o);let a=r.call(o,n);a||(n=B(n),a=r.call(o,n)),l&&l.call(o,n);const m=o.delete(n);return a&&Qe(o,"delete",n,void 0),m},clear(){const n=B(this),o=n.size!==0,r=n.clear();return o&&Qe(n,"clear",void 0,void 0),r}}),["keys","values","entries",Symbol.iterator].forEach(n=>{i[n]=po(n,e,t)}),i}function Zi(e,t){const i=go(e,t);return(s,n,o)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?s:Reflect.get(W(i,n)&&n in s?i:s,n,o)}const mo={get:Zi(!1,!1)},Co={get:Zi(!1,!0)},vo={get:Zi(!0,!1)};const Xs=new WeakMap,$s=new WeakMap,en=new WeakMap,yo=new WeakMap;function Eo(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function bo(e){return e.__v_skip||!Object.isExtensible(e)?0:Eo(kn(e))}function zi(e){return nt(e)?e:Xi(e,!1,uo,mo,Xs)}function xo(e){return Xi(e,!1,ho,Co,$s)}function tn(e){return Xi(e,!0,fo,vo,en)}function Xi(e,t,i,s,n){if(!Z(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=n.get(e);if(o)return o;const r=bo(e);if(r===0)return e;const l=new Proxy(e,r===2?s:i);return n.set(e,l),l}function Mt(e){return nt(e)?Mt(e.__v_raw):!!(e&&e.__v_isReactive)}function nt(e){return!!(e&&e.__v_isReadonly)}function Se(e){return!!(e&&e.__v_isShallow)}function $i(e){return e?!!e.__v_raw:!1}function B(e){const t=e&&e.__v_raw;return t?B(t):e}function wo(e){return!W(e,"__v_skip")&&Object.isExtensible(e)&&Ws(e,"__v_skip",!0),e}const le=e=>Z(e)?zi(e):e,Oi=e=>Z(e)?tn(e):e;function oe(e){return e?e.__v_isRef===!0:!1}function se(e){return Ao(e,!1)}function Ao(e,t){return oe(e)?e:new Mo(e,t)}class Mo{constructor(t,i){this.dep=new Ji,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=i?t:B(t),this._value=i?t:le(t),this.__v_isShallow=i}get value(){return this.dep.track(),this._value}set value(t){const i=this._rawValue,s=this.__v_isShallow||Se(t)||nt(t);t=s?t:B(t),Ne(t,i)&&(this._rawValue=t,this._value=s?t:le(t),this.dep.trigger())}}function To(e){return oe(e)?e.value:e}const So={get:(e,t,i)=>t==="__v_raw"?e:To(Reflect.get(e,t,i)),set:(e,t,i,s)=>{const n=e[t];return oe(n)&&!oe(i)?(n.value=i,!0):Reflect.set(e,t,i,s)}};function sn(e){return Mt(e)?e:new Proxy(e,So)}class _o{constructor(t,i,s){this.fn=t,this.setter=i,this._value=void 0,this.dep=new Ji(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=It-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!i,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&N!==this)return ks(this,!0),!0}get value(){const t=this.dep.track();return Ks(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Io(e,t,i=!1){let s,n;return F(e)?s=e:(s=e.get,n=e.set),new _o(s,n,i)}const jt={},Zt=new WeakMap;let tt;function Lo(e,t=!1,i=tt){if(i){let s=Zt.get(i);s||Zt.set(i,s=[]),s.push(e)}}function Oo(e,t,i=k){const{immediate:s,deep:n,once:o,scheduler:r,augmentJob:l,call:a}=i,m=P=>n?P:Se(P)||n===!1||n===0?Ue(P,1):Ue(P);let h,f,S,L,V=!1,H=!1;if(oe(e)?(f=()=>e.value,V=Se(e)):Mt(e)?(f=()=>m(e),V=!0):R(e)?(H=!0,V=e.some(P=>Mt(P)||Se(P)),f=()=>e.map(P=>{if(oe(P))return P.value;if(Mt(P))return m(P);if(F(P))return a?a(P,2):P()})):F(e)?t?f=a?()=>a(e,2):e:f=()=>{if(S){Je();try{S()}finally{qe()}}const P=tt;tt=h;try{return a?a(e,3,[L]):e(L)}finally{tt=P}}:f=Re,t&&n){const P=f,q=n===!0?1/0:n;f=()=>Ue(P(),q)}const ee=io(),Q=()=>{h.stop(),ee&&ee.active&&Qi(ee.effects,h)};if(o&&t){const P=t;t=(...q)=>{P(...q),Q()}}let G=H?new Array(e.length).fill(jt):jt;const U=P=>{if(!(!(h.flags&1)||!h.dirty&&!P))if(t){const q=h.run();if(n||V||(H?q.some((_e,fe)=>Ne(_e,G[fe])):Ne(q,G))){S&&S();const _e=tt;tt=h;try{const fe=[q,G===jt?void 0:H&&G[0]===jt?[]:G,L];a?a(t,3,fe):t(...fe),G=q}finally{tt=_e}}}else h.run()};return l&&l(U),h=new Us(f),h.scheduler=r?()=>r(U,!1):U,L=P=>Lo(P,!1,h),S=h.onStop=()=>{const P=Zt.get(h);if(P){if(a)a(P,4);else for(const q of P)q();Zt.delete(h)}},t?s?U(!0):G=h.run():r?r(U.bind(null,!0),!0):h.run(),Q.pause=h.pause.bind(h),Q.resume=h.resume.bind(h),Q.stop=Q,Q}function Ue(e,t=1/0,i){if(t<=0||!Z(e)||e.__v_skip||(i=i||new Set,i.has(e)))return e;if(i.add(e),t--,oe(e))Ue(e.value,t,i);else if(R(e))for(let s=0;s<e.length;s++)Ue(e[s],t,i);else if(Ui(e)||bt(e))e.forEach(s=>{Ue(s,t,i)});else if(Yn(e)){for(const s in e)Ue(e[s],t,i);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&Ue(e[s],t,i)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ft(e,t,i,s){try{return s?e(...s):e()}catch(n){ci(n,t,i)}}function He(e,t,i,s){if(F(e)){const n=Ft(e,t,i,s);return n&&Vs(n)&&n.catch(o=>{ci(o,t,i)}),n}if(R(e)){const n=[];for(let o=0;o<e.length;o++)n.push(He(e[o],t,i,s));return n}}function ci(e,t,i,s=!0){const n=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:r}=t&&t.appContext.config||k;if(t){let l=t.parent;const a=t.proxy,m=`https://vuejs.org/error-reference/#runtime-${i}`;for(;l;){const h=l.ec;if(h){for(let f=0;f<h.length;f++)if(h[f](e,a,m)===!1)return}l=l.parent}if(o){Je(),Ft(o,null,10,[e,a,m]),qe();return}}Po(e,i,n,s,r)}function Po(e,t,i,s=!0,n=!1){if(n)throw e;console.error(e)}const ce=[];let Pe=-1;const ft=[];let ke=null,at=0;const nn=Promise.resolve();let zt=null;function on(e){const t=zt||nn;return e?t.then(this?e.bind(this):e):t}function Do(e){let t=Pe+1,i=ce.length;for(;t<i;){const s=t+i>>>1,n=ce[s],o=Ot(n);o<e||o===e&&n.flags&2?t=s+1:i=s}return t}function es(e){if(!(e.flags&1)){const t=Ot(e),i=ce[ce.length-1];!i||!(e.flags&2)&&t>=Ot(i)?ce.push(e):ce.splice(Do(t),0,e),e.flags|=1,rn()}}function rn(){zt||(zt=nn.then(cn))}function Ro(e){R(e)?ft.push(...e):ke&&e.id===-1?ke.splice(at+1,0,e):e.flags&1||(ft.push(e),e.flags|=1),rn()}function us(e,t,i=Pe+1){for(;i<ce.length;i++){const s=ce[i];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;ce.splice(i,1),i--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function ln(e){if(ft.length){const t=[...new Set(ft)].sort((i,s)=>Ot(i)-Ot(s));if(ft.length=0,ke){ke.push(...t);return}for(ke=t,at=0;at<ke.length;at++){const i=ke[at];i.flags&4&&(i.flags&=-2),i.flags&8||i(),i.flags&=-2}ke=null,at=0}}const Ot=e=>e.id==null?e.flags&2?-1:1/0:e.id;function cn(e){try{for(Pe=0;Pe<ce.length;Pe++){const t=ce[Pe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ft(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Pe<ce.length;Pe++){const t=ce[Pe];t&&(t.flags&=-2)}Pe=-1,ce.length=0,ln(),zt=null,(ce.length||ft.length)&&cn()}}let be=null,an=null;function Xt(e){const t=be;return be=e,an=e&&e.type.__scopeId||null,t}function Fo(e,t=be,i){if(!t||e._n)return e;const s=(...n)=>{s._d&&ys(-1);const o=Xt(t);let r;try{r=e(...n)}finally{Xt(o),s._d&&ys(1)}return r};return s._n=!0,s._c=!0,s._d=!0,s}function yi(e,t){if(be===null)return e;const i=hi(be),s=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[o,r,l,a=k]=t[n];o&&(F(o)&&(o={mounted:o,updated:o}),o.deep&&Ue(r),s.push({dir:o,instance:i,value:r,oldValue:void 0,arg:l,modifiers:a}))}return e}function $e(e,t,i,s){const n=e.dirs,o=t&&t.dirs;for(let r=0;r<n.length;r++){const l=n[r];o&&(l.oldValue=o[r].value);let a=l.dir[s];a&&(Je(),He(a,i,8,[e.el,l,e,t]),qe())}}const Ho=Symbol("_vte"),Vo=e=>e.__isTeleport;function ts(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ts(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function un(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function $t(e,t,i,s,n=!1){if(R(e)){e.forEach((V,H)=>$t(V,t&&(R(t)?t[H]:t),i,s,n));return}if(Tt(s)&&!n){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&$t(e,t,i,s.component.subTree);return}const o=s.shapeFlag&4?hi(s.component):s.el,r=n?null:o,{i:l,r:a}=e,m=t&&t.r,h=l.refs===k?l.refs={}:l.refs,f=l.setupState,S=B(f),L=f===k?()=>!1:V=>W(S,V);if(m!=null&&m!==a&&(te(m)?(h[m]=null,L(m)&&(f[m]=null)):oe(m)&&(m.value=null)),F(a))Ft(a,l,12,[r,h]);else{const V=te(a),H=oe(a);if(V||H){const ee=()=>{if(e.f){const Q=V?L(a)?f[a]:h[a]:a.value;n?R(Q)&&Qi(Q,o):R(Q)?Q.includes(o)||Q.push(o):V?(h[a]=[o],L(a)&&(f[a]=h[a])):(a.value=[o],e.k&&(h[e.k]=a.value))}else V?(h[a]=r,L(a)&&(f[a]=r)):H&&(a.value=r,e.k&&(h[e.k]=r))};r?(ee.id=-1,me(ee,i)):ee()}}}ri().requestIdleCallback;ri().cancelIdleCallback;const Tt=e=>!!e.type.__asyncLoader,fn=e=>e.type.__isKeepAlive;function Bo(e,t){hn(e,"a",t)}function Wo(e,t){hn(e,"da",t)}function hn(e,t,i=ae){const s=e.__wdc||(e.__wdc=()=>{let n=i;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(ai(t,s,i),i){let n=i.parent;for(;n&&n.parent;)fn(n.parent.vnode)&&Qo(s,t,i,n),n=n.parent}}function Qo(e,t,i,s){const n=ai(t,e,s,!0);gn(()=>{Qi(s[t],n)},i)}function ai(e,t,i=ae,s=!1){if(i){const n=i[e]||(i[e]=[]),o=t.__weh||(t.__weh=(...r)=>{Je();const l=Ht(i),a=He(t,i,e,r);return l(),qe(),a});return s?n.unshift(o):n.push(o),o}}const je=e=>(t,i=ae)=>{(!Dt||e==="sp")&&ai(e,(...s)=>t(...s),i)},Uo=je("bm"),dn=je("m"),jo=je("bu"),ko=je("u"),pn=je("bum"),gn=je("um"),Yo=je("sp"),No=je("rtg"),Ko=je("rtc");function Go(e,t=ae){ai("ec",e,t)}const Jo=Symbol.for("v-ndc"),Pi=e=>e?Vn(e)?hi(e):Pi(e.parent):null,St=ue(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Pi(e.parent),$root:e=>Pi(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Cn(e),$forceUpdate:e=>e.f||(e.f=()=>{es(e.update)}),$nextTick:e=>e.n||(e.n=on.bind(e.proxy)),$watch:e=>mr.bind(e)}),Ei=(e,t)=>e!==k&&!e.__isScriptSetup&&W(e,t),qo={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:i,setupState:s,data:n,props:o,accessCache:r,type:l,appContext:a}=e;let m;if(t[0]!=="$"){const L=r[t];if(L!==void 0)switch(L){case 1:return s[t];case 2:return n[t];case 4:return i[t];case 3:return o[t]}else{if(Ei(s,t))return r[t]=1,s[t];if(n!==k&&W(n,t))return r[t]=2,n[t];if((m=e.propsOptions[0])&&W(m,t))return r[t]=3,o[t];if(i!==k&&W(i,t))return r[t]=4,i[t];Di&&(r[t]=0)}}const h=St[t];let f,S;if(h)return t==="$attrs"&&ne(e.attrs,"get",""),h(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(i!==k&&W(i,t))return r[t]=4,i[t];if(S=a.config.globalProperties,W(S,t))return S[t]},set({_:e},t,i){const{data:s,setupState:n,ctx:o}=e;return Ei(n,t)?(n[t]=i,!0):s!==k&&W(s,t)?(s[t]=i,!0):W(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=i,!0)},has({_:{data:e,setupState:t,accessCache:i,ctx:s,appContext:n,propsOptions:o}},r){let l;return!!i[r]||e!==k&&W(e,r)||Ei(t,r)||(l=o[0])&&W(l,r)||W(s,r)||W(St,r)||W(n.config.globalProperties,r)},defineProperty(e,t,i){return i.get!=null?e._.accessCache[t]=0:W(i,"value")&&this.set(e,t,i.value,null),Reflect.defineProperty(e,t,i)}};function fs(e){return R(e)?e.reduce((t,i)=>(t[i]=null,t),{}):e}let Di=!0;function Zo(e){const t=Cn(e),i=e.proxy,s=e.ctx;Di=!1,t.beforeCreate&&hs(t.beforeCreate,e,"bc");const{data:n,computed:o,methods:r,watch:l,provide:a,inject:m,created:h,beforeMount:f,mounted:S,beforeUpdate:L,updated:V,activated:H,deactivated:ee,beforeDestroy:Q,beforeUnmount:G,destroyed:U,unmounted:P,render:q,renderTracked:_e,renderTriggered:fe,errorCaptured:ye,serverPrefetch:xe,expose:X,inheritAttrs:he,components:de,directives:pe,filters:we}=t;if(m&&zo(m,s,null),r)for(const K in r){const j=r[K];F(j)&&(s[K]=j.bind(i))}if(n){const K=n.call(i,i);Z(K)&&(e.data=zi(K))}if(Di=!0,o)for(const K in o){const j=o[K],Ie=F(j)?j.bind(i,i):F(j.get)?j.get.bind(i,i):Re,lt=!F(j)&&F(j.set)?j.set.bind(i):Re,Ae=Wr({get:Ie,set:lt});Object.defineProperty(s,K,{enumerable:!0,configurable:!0,get:()=>Ae.value,set:Ee=>Ae.value=Ee})}if(l)for(const K in l)mn(l[K],s,i,K);if(a){const K=F(a)?a.call(i):a;Reflect.ownKeys(K).forEach(j=>{sr(j,K[j])})}h&&hs(h,e,"c");function z(K,j){R(j)?j.forEach(Ie=>K(Ie.bind(i))):j&&K(j.bind(i))}if(z(Uo,f),z(dn,S),z(jo,L),z(ko,V),z(Bo,H),z(Wo,ee),z(Go,ye),z(Ko,_e),z(No,fe),z(pn,G),z(gn,P),z(Yo,xe),R(X))if(X.length){const K=e.exposed||(e.exposed={});X.forEach(j=>{Object.defineProperty(K,j,{get:()=>i[j],set:Ie=>i[j]=Ie})})}else e.exposed||(e.exposed={});q&&e.render===Re&&(e.render=q),he!=null&&(e.inheritAttrs=he),de&&(e.components=de),pe&&(e.directives=pe),xe&&un(e)}function zo(e,t,i=Re){R(e)&&(e=Ri(e));for(const s in e){const n=e[s];let o;Z(n)?"default"in n?o=Yt(n.from||s,n.default,!0):o=Yt(n.from||s):o=Yt(n),oe(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:r=>o.value=r}):t[s]=o}}function hs(e,t,i){He(R(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,i)}function mn(e,t,i,s){let n=s.includes(".")?On(i,s):()=>i[s];if(te(e)){const o=t[e];F(o)&&xi(n,o)}else if(F(e))xi(n,e.bind(i));else if(Z(e))if(R(e))e.forEach(o=>mn(o,t,i,s));else{const o=F(e.handler)?e.handler.bind(i):t[e.handler];F(o)&&xi(n,o,e)}}function Cn(e){const t=e.type,{mixins:i,extends:s}=t,{mixins:n,optionsCache:o,config:{optionMergeStrategies:r}}=e.appContext,l=o.get(t);let a;return l?a=l:!n.length&&!i&&!s?a=t:(a={},n.length&&n.forEach(m=>ei(a,m,r,!0)),ei(a,t,r)),Z(t)&&o.set(t,a),a}function ei(e,t,i,s=!1){const{mixins:n,extends:o}=t;o&&ei(e,o,i,!0),n&&n.forEach(r=>ei(e,r,i,!0));for(const r in t)if(!(s&&r==="expose")){const l=Xo[r]||i&&i[r];e[r]=l?l(e[r],t[r]):t[r]}return e}const Xo={data:ds,props:ps,emits:ps,methods:Et,computed:Et,beforeCreate:re,created:re,beforeMount:re,mounted:re,beforeUpdate:re,updated:re,beforeDestroy:re,beforeUnmount:re,destroyed:re,unmounted:re,activated:re,deactivated:re,errorCaptured:re,serverPrefetch:re,components:Et,directives:Et,watch:er,provide:ds,inject:$o};function ds(e,t){return t?e?function(){return ue(F(e)?e.call(this,this):e,F(t)?t.call(this,this):t)}:t:e}function $o(e,t){return Et(Ri(e),Ri(t))}function Ri(e){if(R(e)){const t={};for(let i=0;i<e.length;i++)t[e[i]]=e[i];return t}return e}function re(e,t){return e?[...new Set([].concat(e,t))]:t}function Et(e,t){return e?ue(Object.create(null),e,t):t}function ps(e,t){return e?R(e)&&R(t)?[...new Set([...e,...t])]:ue(Object.create(null),fs(e),fs(t??{})):t}function er(e,t){if(!e)return t;if(!t)return e;const i=ue(Object.create(null),e);for(const s in t)i[s]=re(e[s],t[s]);return i}function vn(){return{app:null,config:{isNativeTag:Qn,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let tr=0;function ir(e,t){return function(s,n=null){F(s)||(s=ue({},s)),n!=null&&!Z(n)&&(n=null);const o=vn(),r=new WeakSet,l=[];let a=!1;const m=o.app={_uid:tr++,_component:s,_props:n,_container:null,_context:o,_instance:null,version:Qr,get config(){return o.config},set config(h){},use(h,...f){return r.has(h)||(h&&F(h.install)?(r.add(h),h.install(m,...f)):F(h)&&(r.add(h),h(m,...f))),m},mixin(h){return o.mixins.includes(h)||o.mixins.push(h),m},component(h,f){return f?(o.components[h]=f,m):o.components[h]},directive(h,f){return f?(o.directives[h]=f,m):o.directives[h]},mount(h,f,S){if(!a){const L=m._ceVNode||Fe(s,n);return L.appContext=o,S===!0?S="svg":S===!1&&(S=void 0),e(L,h,S),a=!0,m._container=h,h.__vue_app__=m,hi(L.component)}},onUnmount(h){l.push(h)},unmount(){a&&(He(l,m._instance,16),e(null,m._container),delete m._container.__vue_app__)},provide(h,f){return o.provides[h]=f,m},runWithContext(h){const f=ht;ht=m;try{return h()}finally{ht=f}}};return m}}let ht=null;function sr(e,t){if(ae){let i=ae.provides;const s=ae.parent&&ae.parent.provides;s===i&&(i=ae.provides=Object.create(s)),i[e]=t}}function Yt(e,t,i=!1){const s=ae||be;if(s||ht){const n=ht?ht._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return i&&F(t)?t.call(s&&s.proxy):t}}const yn={},En=()=>Object.create(yn),bn=e=>Object.getPrototypeOf(e)===yn;function nr(e,t,i,s=!1){const n={},o=En();e.propsDefaults=Object.create(null),xn(e,t,n,o);for(const r in e.propsOptions[0])r in n||(n[r]=void 0);i?e.props=s?n:xo(n):e.type.props?e.props=n:e.props=o,e.attrs=o}function or(e,t,i,s){const{props:n,attrs:o,vnode:{patchFlag:r}}=e,l=B(n),[a]=e.propsOptions;let m=!1;if((s||r>0)&&!(r&16)){if(r&8){const h=e.vnode.dynamicProps;for(let f=0;f<h.length;f++){let S=h[f];if(ui(e.emitsOptions,S))continue;const L=t[S];if(a)if(W(o,S))L!==o[S]&&(o[S]=L,m=!0);else{const V=Ge(S);n[V]=Fi(a,l,V,L,e,!1)}else L!==o[S]&&(o[S]=L,m=!0)}}}else{xn(e,t,n,o)&&(m=!0);let h;for(const f in l)(!t||!W(t,f)&&((h=rt(f))===f||!W(t,h)))&&(a?i&&(i[f]!==void 0||i[h]!==void 0)&&(n[f]=Fi(a,l,f,void 0,e,!0)):delete n[f]);if(o!==l)for(const f in o)(!t||!W(t,f))&&(delete o[f],m=!0)}m&&Qe(e.attrs,"set","")}function xn(e,t,i,s){const[n,o]=e.propsOptions;let r=!1,l;if(t)for(let a in t){if(xt(a))continue;const m=t[a];let h;n&&W(n,h=Ge(a))?!o||!o.includes(h)?i[h]=m:(l||(l={}))[h]=m:ui(e.emitsOptions,a)||(!(a in s)||m!==s[a])&&(s[a]=m,r=!0)}if(o){const a=B(i),m=l||k;for(let h=0;h<o.length;h++){const f=o[h];i[f]=Fi(n,a,f,m[f],e,!W(m,f))}}return r}function Fi(e,t,i,s,n,o){const r=e[i];if(r!=null){const l=W(r,"default");if(l&&s===void 0){const a=r.default;if(r.type!==Function&&!r.skipFactory&&F(a)){const{propsDefaults:m}=n;if(i in m)s=m[i];else{const h=Ht(n);s=m[i]=a.call(null,t),h()}}else s=a;n.ce&&n.ce._setProp(i,s)}r[0]&&(o&&!l?s=!1:r[1]&&(s===""||s===rt(i))&&(s=!0))}return s}const rr=new WeakMap;function wn(e,t,i=!1){const s=i?rr:t.propsCache,n=s.get(e);if(n)return n;const o=e.props,r={},l=[];let a=!1;if(!F(e)){const h=f=>{a=!0;const[S,L]=wn(f,t,!0);ue(r,S),L&&l.push(...L)};!i&&t.mixins.length&&t.mixins.forEach(h),e.extends&&h(e.extends),e.mixins&&e.mixins.forEach(h)}if(!o&&!a)return Z(e)&&s.set(e,ut),ut;if(R(o))for(let h=0;h<o.length;h++){const f=Ge(o[h]);gs(f)&&(r[f]=k)}else if(o)for(const h in o){const f=Ge(h);if(gs(f)){const S=o[h],L=r[f]=R(S)||F(S)?{type:S}:ue({},S),V=L.type;let H=!1,ee=!0;if(R(V))for(let Q=0;Q<V.length;++Q){const G=V[Q],U=F(G)&&G.name;if(U==="Boolean"){H=!0;break}else U==="String"&&(ee=!1)}else H=F(V)&&V.name==="Boolean";L[0]=H,L[1]=ee,(H||W(L,"default"))&&l.push(f)}}const m=[r,l];return Z(e)&&s.set(e,m),m}function gs(e){return e[0]!=="$"&&!xt(e)}const An=e=>e[0]==="_"||e==="$stable",is=e=>R(e)?e.map(De):[De(e)],lr=(e,t,i)=>{if(t._n)return t;const s=Fo((...n)=>is(t(...n)),i);return s._c=!1,s},Mn=(e,t,i)=>{const s=e._ctx;for(const n in e){if(An(n))continue;const o=e[n];if(F(o))t[n]=lr(n,o,s);else if(o!=null){const r=is(o);t[n]=()=>r}}},Tn=(e,t)=>{const i=is(t);e.slots.default=()=>i},Sn=(e,t,i)=>{for(const s in t)(i||s!=="_")&&(e[s]=t[s])},cr=(e,t,i)=>{const s=e.slots=En();if(e.vnode.shapeFlag&32){const n=t._;n?(Sn(s,t,i),i&&Ws(s,"_",n,!0)):Mn(t,s)}else t&&Tn(e,t)},ar=(e,t,i)=>{const{vnode:s,slots:n}=e;let o=!0,r=k;if(s.shapeFlag&32){const l=t._;l?i&&l===1?o=!1:Sn(n,t,i):(o=!t.$stable,Mn(t,n)),r=t}else t&&(Tn(e,t),r={default:1});if(o)for(const l in n)!An(l)&&r[l]==null&&delete n[l]},me=wr;function ur(e){return fr(e)}function fr(e,t){const i=ri();i.__VUE__=!0;const{insert:s,remove:n,patchProp:o,createElement:r,createText:l,createComment:a,setText:m,setElementText:h,parentNode:f,nextSibling:S,setScopeId:L=Re,insertStaticContent:V}=e,H=(c,u,p,E=null,v=null,y=null,T=void 0,A=null,x=!!u.dynamicChildren)=>{if(c===u)return;c&&!yt(c,u)&&(E=ze(c),Ee(c,v,y,!0),c=null),u.patchFlag===-2&&(x=!1,u.dynamicChildren=null);const{type:b,ref:g,shapeFlag:d}=u;switch(b){case fi:ee(c,u,p,E);break;case ot:Q(c,u,p,E);break;case Nt:c==null&&G(u,p,E,T);break;case We:de(c,u,p,E,v,y,T,A,x);break;default:d&1?q(c,u,p,E,v,y,T,A,x):d&6?pe(c,u,p,E,v,y,T,A,x):(d&64||d&128)&&b.process(c,u,p,E,v,y,T,A,x,Xe)}g!=null&&v&&$t(g,c&&c.ref,y,u||c,!u)},ee=(c,u,p,E)=>{if(c==null)s(u.el=l(u.children),p,E);else{const v=u.el=c.el;u.children!==c.children&&m(v,u.children)}},Q=(c,u,p,E)=>{c==null?s(u.el=a(u.children||""),p,E):u.el=c.el},G=(c,u,p,E)=>{[c.el,c.anchor]=V(c.children,u,p,E,c.el,c.anchor)},U=({el:c,anchor:u},p,E)=>{let v;for(;c&&c!==u;)v=S(c),s(c,p,E),c=v;s(u,p,E)},P=({el:c,anchor:u})=>{let p;for(;c&&c!==u;)p=S(c),n(c),c=p;n(u)},q=(c,u,p,E,v,y,T,A,x)=>{u.type==="svg"?T="svg":u.type==="math"&&(T="mathml"),c==null?_e(u,p,E,v,y,T,A,x):xe(c,u,v,y,T,A,x)},_e=(c,u,p,E,v,y,T,A)=>{let x,b;const{props:g,shapeFlag:d,transition:C,dirs:M}=c;if(x=c.el=r(c.type,y,g&&g.is,g),d&8?h(x,c.children):d&16&&ye(c.children,x,null,E,v,bi(c,y),T,A),M&&$e(c,null,E,"created"),fe(x,c,c.scopeId,T,E),g){for(const _ in g)_!=="value"&&!xt(_)&&o(x,_,null,g[_],y,E);"value"in g&&o(x,"value",null,g.value,y),(b=g.onVnodeBeforeMount)&&Oe(b,E,c)}M&&$e(c,null,E,"beforeMount");const I=hr(v,C);I&&C.beforeEnter(x),s(x,u,p),((b=g&&g.onVnodeMounted)||I||M)&&me(()=>{b&&Oe(b,E,c),I&&C.enter(x),M&&$e(c,null,E,"mounted")},v)},fe=(c,u,p,E,v)=>{if(p&&L(c,p),E)for(let y=0;y<E.length;y++)L(c,E[y]);if(v){let y=v.subTree;if(u===y||Dn(y.type)&&(y.ssContent===u||y.ssFallback===u)){const T=v.vnode;fe(c,T,T.scopeId,T.slotScopeIds,v.parent)}}},ye=(c,u,p,E,v,y,T,A,x=0)=>{for(let b=x;b<c.length;b++){const g=c[b]=A?Ye(c[b]):De(c[b]);H(null,g,u,p,E,v,y,T,A)}},xe=(c,u,p,E,v,y,T)=>{const A=u.el=c.el;let{patchFlag:x,dynamicChildren:b,dirs:g}=u;x|=c.patchFlag&16;const d=c.props||k,C=u.props||k;let M;if(p&&et(p,!1),(M=C.onVnodeBeforeUpdate)&&Oe(M,p,u,c),g&&$e(u,c,p,"beforeUpdate"),p&&et(p,!0),(d.innerHTML&&C.innerHTML==null||d.textContent&&C.textContent==null)&&h(A,""),b?X(c.dynamicChildren,b,A,p,E,bi(u,v),y):T||j(c,u,A,null,p,E,bi(u,v),y,!1),x>0){if(x&16)he(A,d,C,p,v);else if(x&2&&d.class!==C.class&&o(A,"class",null,C.class,v),x&4&&o(A,"style",d.style,C.style,v),x&8){const I=u.dynamicProps;for(let _=0;_<I.length;_++){const w=I[_],O=d[w],D=C[w];(D!==O||w==="value")&&o(A,w,O,D,v,p)}}x&1&&c.children!==u.children&&h(A,u.children)}else!T&&b==null&&he(A,d,C,p,v);((M=C.onVnodeUpdated)||g)&&me(()=>{M&&Oe(M,p,u,c),g&&$e(u,c,p,"updated")},E)},X=(c,u,p,E,v,y,T)=>{for(let A=0;A<u.length;A++){const x=c[A],b=u[A],g=x.el&&(x.type===We||!yt(x,b)||x.shapeFlag&70)?f(x.el):p;H(x,b,g,null,E,v,y,T,!0)}},he=(c,u,p,E,v)=>{if(u!==p){if(u!==k)for(const y in u)!xt(y)&&!(y in p)&&o(c,y,u[y],null,v,E);for(const y in p){if(xt(y))continue;const T=p[y],A=u[y];T!==A&&y!=="value"&&o(c,y,A,T,v,E)}"value"in p&&o(c,"value",u.value,p.value,v)}},de=(c,u,p,E,v,y,T,A,x)=>{const b=u.el=c?c.el:l(""),g=u.anchor=c?c.anchor:l("");let{patchFlag:d,dynamicChildren:C,slotScopeIds:M}=u;M&&(A=A?A.concat(M):M),c==null?(s(b,p,E),s(g,p,E),ye(u.children||[],p,g,v,y,T,A,x)):d>0&&d&64&&C&&c.dynamicChildren?(X(c.dynamicChildren,C,p,v,y,T,A),(u.key!=null||v&&u===v.subTree)&&_n(c,u,!0)):j(c,u,p,g,v,y,T,A,x)},pe=(c,u,p,E,v,y,T,A,x)=>{u.slotScopeIds=A,c==null?u.shapeFlag&512?v.ctx.activate(u,p,E,T,x):we(u,p,E,v,y,T,x):gt(c,u,x)},we=(c,u,p,E,v,y,T)=>{const A=c.component=Dr(c,E,v);if(fn(c)&&(A.ctx.renderer=Xe),Rr(A,!1,T),A.asyncDep){if(v&&v.registerDep(A,z,T),!c.el){const x=A.subTree=Fe(ot);Q(null,x,u,p)}}else z(A,c,u,p,v,y,T)},gt=(c,u,p)=>{const E=u.component=c.component;if(br(c,u,p))if(E.asyncDep&&!E.asyncResolved){K(E,u,p);return}else E.next=u,E.update();else u.el=c.el,E.vnode=u},z=(c,u,p,E,v,y,T)=>{const A=()=>{if(c.isMounted){let{next:d,bu:C,u:M,parent:I,vnode:_}=c;{const J=In(c);if(J){d&&(d.el=_.el,K(c,d,T)),J.asyncDep.then(()=>{c.isUnmounted||A()});return}}let w=d,O;et(c,!1),d?(d.el=_.el,K(c,d,T)):d=_,C&&kt(C),(O=d.props&&d.props.onVnodeBeforeUpdate)&&Oe(O,I,d,_),et(c,!0);const D=Cs(c),Y=c.subTree;c.subTree=D,H(Y,D,f(Y.el),ze(Y),c,v,y),d.el=D.el,w===null&&xr(c,D.el),M&&me(M,v),(O=d.props&&d.props.onVnodeUpdated)&&me(()=>Oe(O,I,d,_),v)}else{let d;const{el:C,props:M}=u,{bm:I,m:_,parent:w,root:O,type:D}=c,Y=Tt(u);et(c,!1),I&&kt(I),!Y&&(d=M&&M.onVnodeBeforeMount)&&Oe(d,w,u),et(c,!0);{O.ce&&O.ce._injectChildStyle(D);const J=c.subTree=Cs(c);H(null,J,p,E,c,v,y),u.el=J.el}if(_&&me(_,v),!Y&&(d=M&&M.onVnodeMounted)){const J=u;me(()=>Oe(d,w,J),v)}(u.shapeFlag&256||w&&Tt(w.vnode)&&w.vnode.shapeFlag&256)&&c.a&&me(c.a,v),c.isMounted=!0,u=p=E=null}};c.scope.on();const x=c.effect=new Us(A);c.scope.off();const b=c.update=x.run.bind(x),g=c.job=x.runIfDirty.bind(x);g.i=c,g.id=c.uid,x.scheduler=()=>es(g),et(c,!0),b()},K=(c,u,p)=>{u.component=c;const E=c.vnode.props;c.vnode=u,c.next=null,or(c,u.props,E,p),ar(c,u.children,p),Je(),us(c),qe()},j=(c,u,p,E,v,y,T,A,x=!1)=>{const b=c&&c.children,g=c?c.shapeFlag:0,d=u.children,{patchFlag:C,shapeFlag:M}=u;if(C>0){if(C&128){lt(b,d,p,E,v,y,T,A,x);return}else if(C&256){Ie(b,d,p,E,v,y,T,A,x);return}}M&8?(g&16&&Ze(b,v,y),d!==b&&h(p,d)):g&16?M&16?lt(b,d,p,E,v,y,T,A,x):Ze(b,v,y,!0):(g&8&&h(p,""),M&16&&ye(d,p,E,v,y,T,A,x))},Ie=(c,u,p,E,v,y,T,A,x)=>{c=c||ut,u=u||ut;const b=c.length,g=u.length,d=Math.min(b,g);let C;for(C=0;C<d;C++){const M=u[C]=x?Ye(u[C]):De(u[C]);H(c[C],M,p,null,v,y,T,A,x)}b>g?Ze(c,v,y,!0,!1,d):ye(u,p,E,v,y,T,A,x,d)},lt=(c,u,p,E,v,y,T,A,x)=>{let b=0;const g=u.length;let d=c.length-1,C=g-1;for(;b<=d&&b<=C;){const M=c[b],I=u[b]=x?Ye(u[b]):De(u[b]);if(yt(M,I))H(M,I,p,null,v,y,T,A,x);else break;b++}for(;b<=d&&b<=C;){const M=c[d],I=u[C]=x?Ye(u[C]):De(u[C]);if(yt(M,I))H(M,I,p,null,v,y,T,A,x);else break;d--,C--}if(b>d){if(b<=C){const M=C+1,I=M<g?u[M].el:E;for(;b<=C;)H(null,u[b]=x?Ye(u[b]):De(u[b]),p,I,v,y,T,A,x),b++}}else if(b>C)for(;b<=d;)Ee(c[b],v,y,!0),b++;else{const M=b,I=b,_=new Map;for(b=I;b<=C;b++){const ge=u[b]=x?Ye(u[b]):De(u[b]);ge.key!=null&&_.set(ge.key,b)}let w,O=0;const D=C-I+1;let Y=!1,J=0;const Me=new Array(D);for(b=0;b<D;b++)Me[b]=0;for(b=M;b<=d;b++){const ge=c[b];if(O>=D){Ee(ge,v,y,!0);continue}let Le;if(ge.key!=null)Le=_.get(ge.key);else for(w=I;w<=C;w++)if(Me[w-I]===0&&yt(ge,u[w])){Le=w;break}Le===void 0?Ee(ge,v,y,!0):(Me[Le-I]=b+1,Le>=J?J=Le:Y=!0,H(ge,u[Le],p,null,v,y,T,A,x),O++)}const Wt=Y?dr(Me):ut;for(w=Wt.length-1,b=D-1;b>=0;b--){const ge=I+b,Le=u[ge],os=ge+1<g?u[ge+1].el:E;Me[b]===0?H(null,Le,p,os,v,y,T,A,x):Y&&(w<0||b!==Wt[w]?Ae(Le,p,os,2):w--)}}},Ae=(c,u,p,E,v=null)=>{const{el:y,type:T,transition:A,children:x,shapeFlag:b}=c;if(b&6){Ae(c.component.subTree,u,p,E);return}if(b&128){c.suspense.move(u,p,E);return}if(b&64){T.move(c,u,p,Xe);return}if(T===We){s(y,u,p);for(let d=0;d<x.length;d++)Ae(x[d],u,p,E);s(c.anchor,u,p);return}if(T===Nt){U(c,u,p);return}if(E!==2&&b&1&&A)if(E===0)A.beforeEnter(y),s(y,u,p),me(()=>A.enter(y),v);else{const{leave:d,delayLeave:C,afterLeave:M}=A,I=()=>s(y,u,p),_=()=>{d(y,()=>{I(),M&&M()})};C?C(y,I,_):_()}else s(y,u,p)},Ee=(c,u,p,E=!1,v=!1)=>{const{type:y,props:T,ref:A,children:x,dynamicChildren:b,shapeFlag:g,patchFlag:d,dirs:C,cacheIndex:M}=c;if(d===-2&&(v=!1),A!=null&&$t(A,null,p,c,!0),M!=null&&(u.renderCache[M]=void 0),g&256){u.ctx.deactivate(c);return}const I=g&1&&C,_=!Tt(c);let w;if(_&&(w=T&&T.onVnodeBeforeUnmount)&&Oe(w,u,c),g&6)mt(c.component,p,E);else{if(g&128){c.suspense.unmount(p,E);return}I&&$e(c,null,u,"beforeUnmount"),g&64?c.type.remove(c,u,p,Xe,E):b&&!b.hasOnce&&(y!==We||d>0&&d&64)?Ze(b,u,p,!1,!0):(y===We&&d&384||!v&&g&16)&&Ze(x,u,p),E&&Vt(c)}(_&&(w=T&&T.onVnodeUnmounted)||I)&&me(()=>{w&&Oe(w,u,c),I&&$e(c,null,u,"unmounted")},p)},Vt=c=>{const{type:u,el:p,anchor:E,transition:v}=c;if(u===We){pi(p,E);return}if(u===Nt){P(c);return}const y=()=>{n(p),v&&!v.persisted&&v.afterLeave&&v.afterLeave()};if(c.shapeFlag&1&&v&&!v.persisted){const{leave:T,delayLeave:A}=v,x=()=>T(p,y);A?A(c.el,y,x):x()}else y()},pi=(c,u)=>{let p;for(;c!==u;)p=S(c),n(c),c=p;n(u)},mt=(c,u,p)=>{const{bum:E,scope:v,job:y,subTree:T,um:A,m:x,a:b}=c;ms(x),ms(b),E&&kt(E),v.stop(),y&&(y.flags|=8,Ee(T,c,u,p)),A&&me(A,u),me(()=>{c.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&c.asyncDep&&!c.asyncResolved&&c.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},Ze=(c,u,p,E=!1,v=!1,y=0)=>{for(let T=y;T<c.length;T++)Ee(c[T],u,p,E,v)},ze=c=>{if(c.shapeFlag&6)return ze(c.component.subTree);if(c.shapeFlag&128)return c.suspense.next();const u=S(c.anchor||c.el),p=u&&u[Ho];return p?S(p):u};let Ct=!1;const Bt=(c,u,p)=>{c==null?u._vnode&&Ee(u._vnode,null,null,!0):H(u._vnode||null,c,u,null,null,null,p),u._vnode=c,Ct||(Ct=!0,us(),ln(),Ct=!1)},Xe={p:H,um:Ee,m:Ae,r:Vt,mt:we,mc:ye,pc:j,pbc:X,n:ze,o:e};return{render:Bt,hydrate:void 0,createApp:ir(Bt)}}function bi({type:e,props:t},i){return i==="svg"&&e==="foreignObject"||i==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:i}function et({effect:e,job:t},i){i?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function hr(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function _n(e,t,i=!1){const s=e.children,n=t.children;if(R(s)&&R(n))for(let o=0;o<s.length;o++){const r=s[o];let l=n[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=n[o]=Ye(n[o]),l.el=r.el),!i&&l.patchFlag!==-2&&_n(r,l)),l.type===fi&&(l.el=r.el)}}function dr(e){const t=e.slice(),i=[0];let s,n,o,r,l;const a=e.length;for(s=0;s<a;s++){const m=e[s];if(m!==0){if(n=i[i.length-1],e[n]<m){t[s]=n,i.push(s);continue}for(o=0,r=i.length-1;o<r;)l=o+r>>1,e[i[l]]<m?o=l+1:r=l;m<e[i[o]]&&(o>0&&(t[s]=i[o-1]),i[o]=s)}}for(o=i.length,r=i[o-1];o-- >0;)i[o]=r,r=t[r];return i}function In(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:In(t)}function ms(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const pr=Symbol.for("v-scx"),gr=()=>Yt(pr);function xi(e,t,i){return Ln(e,t,i)}function Ln(e,t,i=k){const{immediate:s,deep:n,flush:o,once:r}=i,l=ue({},i),a=t&&s||!t&&o!=="post";let m;if(Dt){if(o==="sync"){const L=gr();m=L.__watcherHandles||(L.__watcherHandles=[])}else if(!a){const L=()=>{};return L.stop=Re,L.resume=Re,L.pause=Re,L}}const h=ae;l.call=(L,V,H)=>He(L,h,V,H);let f=!1;o==="post"?l.scheduler=L=>{me(L,h&&h.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(L,V)=>{V?L():es(L)}),l.augmentJob=L=>{t&&(L.flags|=4),f&&(L.flags|=2,h&&(L.id=h.uid,L.i=h))};const S=Oo(e,t,l);return Dt&&(m?m.push(S):a&&S()),S}function mr(e,t,i){const s=this.proxy,n=te(e)?e.includes(".")?On(s,e):()=>s[e]:e.bind(s,s);let o;F(t)?o=t:(o=t.handler,i=t);const r=Ht(this),l=Ln(n,o.bind(s),i);return r(),l}function On(e,t){const i=t.split(".");return()=>{let s=e;for(let n=0;n<i.length&&s;n++)s=s[i[n]];return s}}const Cr=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ge(t)}Modifiers`]||e[`${rt(t)}Modifiers`];function vr(e,t,...i){if(e.isUnmounted)return;const s=e.vnode.props||k;let n=i;const o=t.startsWith("update:"),r=o&&Cr(s,t.slice(7));r&&(r.trim&&(n=i.map(h=>te(h)?h.trim():h)),r.number&&(n=i.map(qt)));let l,a=s[l=gi(t)]||s[l=gi(Ge(t))];!a&&o&&(a=s[l=gi(rt(t))]),a&&He(a,e,6,n);const m=s[l+"Once"];if(m){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,He(m,e,6,n)}}function Pn(e,t,i=!1){const s=t.emitsCache,n=s.get(e);if(n!==void 0)return n;const o=e.emits;let r={},l=!1;if(!F(e)){const a=m=>{const h=Pn(m,t,!0);h&&(l=!0,ue(r,h))};!i&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(Z(e)&&s.set(e,null),null):(R(o)?o.forEach(a=>r[a]=null):ue(r,o),Z(e)&&s.set(e,r),r)}function ui(e,t){return!e||!ni(t)?!1:(t=t.slice(2).replace(/Once$/,""),W(e,t[0].toLowerCase()+t.slice(1))||W(e,rt(t))||W(e,t))}function Cs(e){const{type:t,vnode:i,proxy:s,withProxy:n,propsOptions:[o],slots:r,attrs:l,emit:a,render:m,renderCache:h,props:f,data:S,setupState:L,ctx:V,inheritAttrs:H}=e,ee=Xt(e);let Q,G;try{if(i.shapeFlag&4){const P=n||s,q=P;Q=De(m.call(q,P,h,f,L,S,V)),G=l}else{const P=t;Q=De(P.length>1?P(f,{attrs:l,slots:r,emit:a}):P(f,null)),G=t.props?l:yr(l)}}catch(P){_t.length=0,ci(P,e,1),Q=Fe(ot)}let U=Q;if(G&&H!==!1){const P=Object.keys(G),{shapeFlag:q}=U;P.length&&q&7&&(o&&P.some(Wi)&&(G=Er(G,o)),U=pt(U,G,!1,!0))}return i.dirs&&(U=pt(U,null,!1,!0),U.dirs=U.dirs?U.dirs.concat(i.dirs):i.dirs),i.transition&&ts(U,i.transition),Q=U,Xt(ee),Q}const yr=e=>{let t;for(const i in e)(i==="class"||i==="style"||ni(i))&&((t||(t={}))[i]=e[i]);return t},Er=(e,t)=>{const i={};for(const s in e)(!Wi(s)||!(s.slice(9)in t))&&(i[s]=e[s]);return i};function br(e,t,i){const{props:s,children:n,component:o}=e,{props:r,children:l,patchFlag:a}=t,m=o.emitsOptions;if(t.dirs||t.transition)return!0;if(i&&a>=0){if(a&1024)return!0;if(a&16)return s?vs(s,r,m):!!r;if(a&8){const h=t.dynamicProps;for(let f=0;f<h.length;f++){const S=h[f];if(r[S]!==s[S]&&!ui(m,S))return!0}}}else return(n||l)&&(!l||!l.$stable)?!0:s===r?!1:s?r?vs(s,r,m):!0:!!r;return!1}function vs(e,t,i){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let n=0;n<s.length;n++){const o=s[n];if(t[o]!==e[o]&&!ui(i,o))return!0}return!1}function xr({vnode:e,parent:t},i){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=i,t=t.parent;else break}}const Dn=e=>e.__isSuspense;function wr(e,t){t&&t.pendingBranch?R(e)?t.effects.push(...e):t.effects.push(e):Ro(e)}const We=Symbol.for("v-fgt"),fi=Symbol.for("v-txt"),ot=Symbol.for("v-cmt"),Nt=Symbol.for("v-stc"),_t=[];let ve=null;function Kt(e=!1){_t.push(ve=e?null:[])}function Ar(){_t.pop(),ve=_t[_t.length-1]||null}let Pt=1;function ys(e,t=!1){Pt+=e,e<0&&ve&&t&&(ve.hasOnce=!0)}function Rn(e){return e.dynamicChildren=Pt>0?ve||ut:null,Ar(),Pt>0&&ve&&ve.push(e),e}function wi(e,t,i,s,n,o){return Rn(ie(e,t,i,s,n,o,!0))}function Mr(e,t,i,s,n){return Rn(Fe(e,t,i,s,n,!0))}function Fn(e){return e?e.__v_isVNode===!0:!1}function yt(e,t){return e.type===t.type&&e.key===t.key}const Hn=({key:e})=>e??null,Gt=({ref:e,ref_key:t,ref_for:i})=>(typeof e=="number"&&(e=""+e),e!=null?te(e)||oe(e)||F(e)?{i:be,r:e,k:t,f:!!i}:e:null);function ie(e,t=null,i=null,s=0,n=null,o=e===We?0:1,r=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Hn(t),ref:t&&Gt(t),scopeId:an,slotScopeIds:null,children:i,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:be};return l?(ss(a,i),o&128&&e.normalize(a)):i&&(a.shapeFlag|=te(i)?8:16),Pt>0&&!r&&ve&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&ve.push(a),a}const Fe=Tr;function Tr(e,t=null,i=null,s=0,n=null,o=!1){if((!e||e===Jo)&&(e=ot),Fn(e)){const l=pt(e,t,!0);return i&&ss(l,i),Pt>0&&!o&&ve&&(l.shapeFlag&6?ve[ve.indexOf(e)]=l:ve.push(l)),l.patchFlag=-2,l}if(Br(e)&&(e=e.__vccOpts),t){t=Sr(t);let{class:l,style:a}=t;l&&!te(l)&&(t.class=Yi(l)),Z(a)&&($i(a)&&!R(a)&&(a=ue({},a)),t.style=ki(a))}const r=te(e)?1:Dn(e)?128:Vo(e)?64:Z(e)?4:F(e)?2:0;return ie(e,t,i,s,n,r,o,!0)}function Sr(e){return e?$i(e)||bn(e)?ue({},e):e:null}function pt(e,t,i=!1,s=!1){const{props:n,ref:o,patchFlag:r,children:l,transition:a}=e,m=t?Lr(n||{},t):n,h={__v_isVNode:!0,__v_skip:!0,type:e.type,props:m,key:m&&Hn(m),ref:t&&t.ref?i&&o?R(o)?o.concat(Gt(t)):[o,Gt(t)]:Gt(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==We?r===-1?16:r|16:r,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&pt(e.ssContent),ssFallback:e.ssFallback&&pt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&s&&ts(h,a.clone(h)),h}function _r(e=" ",t=0){return Fe(fi,null,e,t)}function Ir(e,t){const i=Fe(Nt,null,e);return i.staticCount=t,i}function Ai(e="",t=!1){return t?(Kt(),Mr(ot,null,e)):Fe(ot,null,e)}function De(e){return e==null||typeof e=="boolean"?Fe(ot):R(e)?Fe(We,null,e.slice()):Fn(e)?Ye(e):Fe(fi,null,String(e))}function Ye(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:pt(e)}function ss(e,t){let i=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(R(t))i=16;else if(typeof t=="object")if(s&65){const n=t.default;n&&(n._c&&(n._d=!1),ss(e,n()),n._c&&(n._d=!0));return}else{i=32;const n=t._;!n&&!bn(t)?t._ctx=be:n===3&&be&&(be.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else F(t)?(t={default:t,_ctx:be},i=32):(t=String(t),s&64?(i=16,t=[_r(t)]):i=8);e.children=t,e.shapeFlag|=i}function Lr(...e){const t={};for(let i=0;i<e.length;i++){const s=e[i];for(const n in s)if(n==="class")t.class!==s.class&&(t.class=Yi([t.class,s.class]));else if(n==="style")t.style=ki([t.style,s.style]);else if(ni(n)){const o=t[n],r=s[n];r&&o!==r&&!(R(o)&&o.includes(r))&&(t[n]=o?[].concat(o,r):r)}else n!==""&&(t[n]=s[n])}return t}function Oe(e,t,i,s=null){He(e,t,7,[i,s])}const Or=vn();let Pr=0;function Dr(e,t,i){const s=e.type,n=(t?t.appContext:e.appContext)||Or,o={uid:Pr++,vnode:e,type:s,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new to(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:wn(s,n),emitsOptions:Pn(s,n),emit:null,emitted:null,propsDefaults:k,inheritAttrs:s.inheritAttrs,ctx:k,data:k,props:k,attrs:k,slots:k,refs:k,setupState:k,setupContext:null,suspense:i,suspenseId:i?i.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=vr.bind(null,o),e.ce&&e.ce(o),o}let ae=null,ti,Hi;{const e=ri(),t=(i,s)=>{let n;return(n=e[i])||(n=e[i]=[]),n.push(s),o=>{n.length>1?n.forEach(r=>r(o)):n[0](o)}};ti=t("__VUE_INSTANCE_SETTERS__",i=>ae=i),Hi=t("__VUE_SSR_SETTERS__",i=>Dt=i)}const Ht=e=>{const t=ae;return ti(e),e.scope.on(),()=>{e.scope.off(),ti(t)}},Es=()=>{ae&&ae.scope.off(),ti(null)};function Vn(e){return e.vnode.shapeFlag&4}let Dt=!1;function Rr(e,t=!1,i=!1){t&&Hi(t);const{props:s,children:n}=e.vnode,o=Vn(e);nr(e,s,o,t),cr(e,n,i);const r=o?Fr(e,t):void 0;return t&&Hi(!1),r}function Fr(e,t){const i=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,qo);const{setup:s}=i;if(s){Je();const n=e.setupContext=s.length>1?Vr(e):null,o=Ht(e),r=Ft(s,e,0,[e.props,n]),l=Vs(r);if(qe(),o(),(l||e.sp)&&!Tt(e)&&un(e),l){if(r.then(Es,Es),t)return r.then(a=>{bs(e,a)}).catch(a=>{ci(a,e,0)});e.asyncDep=r}else bs(e,r)}else Bn(e)}function bs(e,t,i){F(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Z(t)&&(e.setupState=sn(t)),Bn(e)}function Bn(e,t,i){const s=e.type;e.render||(e.render=s.render||Re);{const n=Ht(e);Je();try{Zo(e)}finally{qe(),n()}}}const Hr={get(e,t){return ne(e,"get",""),e[t]}};function Vr(e){const t=i=>{e.exposed=i||{}};return{attrs:new Proxy(e.attrs,Hr),slots:e.slots,emit:e.emit,expose:t}}function hi(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(sn(wo(e.exposed)),{get(t,i){if(i in t)return t[i];if(i in St)return St[i](e)},has(t,i){return i in t||i in St}})):e.proxy}function Br(e){return F(e)&&"__vccOpts"in e}const Wr=(e,t)=>Io(e,t,Dt),Qr="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Vi;const xs=typeof window<"u"&&window.trustedTypes;if(xs)try{Vi=xs.createPolicy("vue",{createHTML:e=>e})}catch{}const Wn=Vi?e=>Vi.createHTML(e):e=>e,Ur="http://www.w3.org/2000/svg",jr="http://www.w3.org/1998/Math/MathML",Be=typeof document<"u"?document:null,ws=Be&&Be.createElement("template"),kr={insert:(e,t,i)=>{t.insertBefore(e,i||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,i,s)=>{const n=t==="svg"?Be.createElementNS(Ur,e):t==="mathml"?Be.createElementNS(jr,e):i?Be.createElement(e,{is:i}):Be.createElement(e);return e==="select"&&s&&s.multiple!=null&&n.setAttribute("multiple",s.multiple),n},createText:e=>Be.createTextNode(e),createComment:e=>Be.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Be.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,i,s,n,o){const r=i?i.previousSibling:t.lastChild;if(n&&(n===o||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),i),!(n===o||!(n=n.nextSibling)););else{ws.innerHTML=Wn(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=ws.content;if(s==="svg"||s==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,i)}return[r?r.nextSibling:t.firstChild,i?i.previousSibling:t.lastChild]}},Yr=Symbol("_vtc");function Nr(e,t,i){const s=e[Yr];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):i?e.setAttribute("class",t):e.className=t}const As=Symbol("_vod"),Kr=Symbol("_vsh"),Gr=Symbol(""),Jr=/(^|;)\s*display\s*:/;function qr(e,t,i){const s=e.style,n=te(i);let o=!1;if(i&&!n){if(t)if(te(t))for(const r of t.split(";")){const l=r.slice(0,r.indexOf(":")).trim();i[l]==null&&Jt(s,l,"")}else for(const r in t)i[r]==null&&Jt(s,r,"");for(const r in i)r==="display"&&(o=!0),Jt(s,r,i[r])}else if(n){if(t!==i){const r=s[Gr];r&&(i+=";"+r),s.cssText=i,o=Jr.test(i)}}else t&&e.removeAttribute("style");As in e&&(e[As]=o?s.display:"",e[Kr]&&(s.display="none"))}const Ms=/\s*!important$/;function Jt(e,t,i){if(R(i))i.forEach(s=>Jt(e,t,s));else if(i==null&&(i=""),t.startsWith("--"))e.setProperty(t,i);else{const s=Zr(e,t);Ms.test(i)?e.setProperty(rt(s),i.replace(Ms,""),"important"):e[s]=i}}const Ts=["Webkit","Moz","ms"],Mi={};function Zr(e,t){const i=Mi[t];if(i)return i;let s=Ge(t);if(s!=="filter"&&s in e)return Mi[t]=s;s=Bs(s);for(let n=0;n<Ts.length;n++){const o=Ts[n]+s;if(o in e)return Mi[t]=o}return t}const Ss="http://www.w3.org/1999/xlink";function _s(e,t,i,s,n,o=Xn(t)){s&&t.startsWith("xlink:")?i==null?e.removeAttributeNS(Ss,t.slice(6,t.length)):e.setAttributeNS(Ss,t,i):i==null||o&&!Qs(i)?e.removeAttribute(t):e.setAttribute(t,o?"":Ke(i)?String(i):i)}function Is(e,t,i,s,n){if(t==="innerHTML"||t==="textContent"){i!=null&&(e[t]=t==="innerHTML"?Wn(i):i);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=i==null?e.type==="checkbox"?"on":"":String(i);(l!==a||!("_value"in e))&&(e.value=a),i==null&&e.removeAttribute(t),e._value=i;return}let r=!1;if(i===""||i==null){const l=typeof e[t];l==="boolean"?i=Qs(i):i==null&&l==="string"?(i="",r=!0):l==="number"&&(i=0,r=!0)}try{e[t]=i}catch{}r&&e.removeAttribute(n||t)}function it(e,t,i,s){e.addEventListener(t,i,s)}function zr(e,t,i,s){e.removeEventListener(t,i,s)}const Ls=Symbol("_vei");function Xr(e,t,i,s,n=null){const o=e[Ls]||(e[Ls]={}),r=o[t];if(s&&r)r.value=s;else{const[l,a]=$r(t);if(s){const m=o[t]=il(s,n);it(e,l,m,a)}else r&&(zr(e,l,r,a),o[t]=void 0)}}const Os=/(?:Once|Passive|Capture)$/;function $r(e){let t;if(Os.test(e)){t={};let s;for(;s=e.match(Os);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):rt(e.slice(2)),t]}let Ti=0;const el=Promise.resolve(),tl=()=>Ti||(el.then(()=>Ti=0),Ti=Date.now());function il(e,t){const i=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=i.attached)return;He(sl(s,i.value),t,5,[s])};return i.value=e,i.attached=tl(),i}function sl(e,t){if(R(t)){const i=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{i.call(e),e._stopped=!0},t.map(s=>n=>!n._stopped&&s&&s(n))}else return t}const Ps=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,nl=(e,t,i,s,n,o)=>{const r=n==="svg";t==="class"?Nr(e,s,r):t==="style"?qr(e,i,s):ni(t)?Wi(t)||Xr(e,t,i,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ol(e,t,s,r))?(Is(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&_s(e,t,s,r,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!te(s))?Is(e,Ge(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),_s(e,t,s,r))};function ol(e,t,i,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ps(t)&&F(i));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return Ps(t)&&te(i)?!1:t in e}const ii=e=>{const t=e.props["onUpdate:modelValue"]||!1;return R(t)?i=>kt(t,i):t};function rl(e){e.target.composing=!0}function Ds(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const dt=Symbol("_assign"),Rs={created(e,{modifiers:{lazy:t,trim:i,number:s}},n){e[dt]=ii(n);const o=s||n.props&&n.props.type==="number";it(e,t?"change":"input",r=>{if(r.target.composing)return;let l=e.value;i&&(l=l.trim()),o&&(l=qt(l)),e[dt](l)}),i&&it(e,"change",()=>{e.value=e.value.trim()}),t||(it(e,"compositionstart",rl),it(e,"compositionend",Ds),it(e,"change",Ds))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:i,modifiers:{lazy:s,trim:n,number:o}},r){if(e[dt]=ii(r),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?qt(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(s&&t===i||n&&e.value.trim()===a)||(e.value=a))}},ll={deep:!0,created(e,{value:t,modifiers:{number:i}},s){const n=Ui(t);it(e,"change",()=>{const o=Array.prototype.filter.call(e.options,r=>r.selected).map(r=>i?qt(si(r)):si(r));e[dt](e.multiple?n?new Set(o):o:o[0]),e._assigning=!0,on(()=>{e._assigning=!1})}),e[dt]=ii(s)},mounted(e,{value:t}){Fs(e,t)},beforeUpdate(e,t,i){e[dt]=ii(i)},updated(e,{value:t}){e._assigning||Fs(e,t)}};function Fs(e,t){const i=e.multiple,s=R(t);if(!(i&&!s&&!Ui(t))){for(let n=0,o=e.options.length;n<o;n++){const r=e.options[n],l=si(r);if(i)if(s){const a=typeof l;a==="string"||a==="number"?r.selected=t.some(m=>String(m)===String(l)):r.selected=eo(t,l)>-1}else r.selected=t.has(l);else if(li(si(r),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!i&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function si(e){return"_value"in e?e._value:e.value}const cl=ue({patchProp:nl},kr);let Hs;function al(){return Hs||(Hs=ur(cl))}const ul=(...e)=>{const t=al().createApp(...e),{mount:i}=t;return t.mount=s=>{const n=hl(s);if(!n)return;const o=t._component;!F(o)&&!o.render&&!o.template&&(o.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const r=i(n,!1,fl(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),r},t};function fl(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function hl(e){return te(e)?document.querySelector(e):e}const $="data:image/png;base64,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";class di{constructor(t){this._definitionChanged=new Cesium.Event,this._color=void 0,this.color=t.color}get isConstant(){return!1}get definitionChanged(){return this._definitionChanged}getType(t){return Cesium.Material.WallDiffuseMaterialType}getValue(t,i){return Cesium.defined(i)||(i={}),i.color=Cesium.Property.getValueOrDefault(this._color,t,Cesium.Color.RED,i.color),i}equals(t){return this===t||t instanceof di&&Cesium.Property.equals(this._color,t._color)}}Object.defineProperties(di.prototype,{color:Cesium.createPropertyDescriptor("color")});Cesium.Property.WallDiffuseMaterialProperty=di;Cesium.Material.WallDiffuseMaterialProperty="WallDiffuseMaterialProperty";Cesium.Material.WallDiffuseMaterialType="WallDiffuseMaterialType";Cesium.Material.WallDiffuseMaterialSource=`
    uniform vec4 color;
    czm_material czm_getMaterial(czm_materialInput materialInput){
    czm_material material = czm_getDefaultMaterial(materialInput);
    vec2 st = materialInput.st;

    material.diffuse = color.rgb * 2.0;
    material.alpha = color.a * (1.0 - fract(st.t)) * 0.8 ;
    return material;
    }
                                            
    `;Cesium.Material._materialCache.addMaterial(Cesium.Material.WallDiffuseMaterialType,{fabric:{type:Cesium.Material.WallDiffuseMaterialType,uniforms:{color:new Cesium.Color(1,0,0,1),scanLineHeight:0},source:Cesium.Material.WallDiffuseMaterialSource},translucent:function(e){return!0}});class dl{constructor(t){this.viewer=t,this.initEvents(),this.positions=[],this.tempPositions=[],this.vertexEntities=[],this.labelEntity=void 0,this.moveVertexEntity=void 0,this.measureDistance=0}initEvents(){this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.MeasureStartEvent=new Cesium.Event,this.MeasureEndEvent=new Cesium.Event}activate(){this.deactivate(),this.registerEvents(),this.viewer.enableCursorStyle=!1,this.viewer._element.style.cursor="default",this.isMeasure=!0,this.measureDistance=0}deactivate(){this.isMeasure&&(this.unRegisterEvents(),this.viewer._element.style.cursor="pointer",this.viewer.enableCursorStyle=!0,this.isMeasure=!1,this.tempPositions=[],this.positions=[])}clear(){this.viewer.entities.remove(this.lineEntity),this.lineEntity=void 0,this.vertexEntities.forEach(t=>{this.viewer.entities.remove(t)}),this.vertexEntities=[],this.moveVertexEntity&&(this.viewer.entities.remove(this.moveVertexEntity),this.moveVertexEntity=void 0)}createLineEntity(){this.lineEntity=this.viewer.entities.add({polyline:{positions:new Cesium.CallbackProperty(t=>this.tempPositions,!1),width:2,material:Cesium.Color.YELLOW,depthFailMaterial:Cesium.Color.YELLOW}})}createVertex(){let t=this.viewer.entities.add({position:this.positions[this.positions.length-1],id:"MeasureDistanceVertex"+this.positions.length,type:"MeasureDistanceVertex",label:{text:this.getSpaceDistance(this.positions)+"米",scale:.5,font:"normal 24px MicroSoft YaHei",distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,5e3),scaleByDistance:new Cesium.NearFarScalar(1e3,1,3e3,.4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM,style:Cesium.LabelStyle.FILL_AND_OUTLINE,pixelOffset:new Cesium.Cartesian2(0,-30),outlineWidth:9,outlineColor:Cesium.Color.WHITE},point:{color:Cesium.Color.FUCHSIA,pixelSize:8,disableDepthTestDistance:500}});this.vertexEntities.push(t)}createStartEntity(){let t=this.viewer.entities.add({position:this.positions[0],type:"MeasureDistanceVertex",billboard:{image:"data:image/png;base64,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",scaleByDistance:new Cesium.NearFarScalar(300,1,1200,.4),distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,1e4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM},point:{color:Cesium.Color.FUCHSIA,pixelSize:6}});this.vertexEntities.push(t)}createEndEntity(){let t=this.viewer.entities.getById("MeasureDistanceVertex"+this.positions.length);t&&this.viewer.entities.remove(t),this.moveVertexEntity&&this.viewer.entities.remove(this.moveVertexEntity);let i=this.viewer.entities.add({position:this.positions[this.positions.length-1],type:"MeasureDistanceVertex",label:{text:"总距离："+this.getSpaceDistance(this.positions)+"米",scale:.5,font:"normal 26px MicroSoft YaHei",distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,5e3),scaleByDistance:new Cesium.NearFarScalar(1e3,1,3e3,.4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM,style:Cesium.LabelStyle.FILL_AND_OUTLINE,pixelOffset:new Cesium.Cartesian2(0,-50),outlineWidth:9,outlineColor:Cesium.Color.WHITE,eyeOffset:new Cesium.Cartesian3(0,0,-10)},billboard:{image:"data:image/png;base64,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",scaleByDistance:new Cesium.NearFarScalar(300,1,1200,.4),distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,1e4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM},point:{color:Cesium.Color.FUCHSIA,pixelSize:6}});this.vertexEntities.push(i)}registerEvents(){this.leftClickEvent(),this.rightClickEvent(),this.mouseMoveEvent()}leftClickEvent(){this.handler.setInputAction(t=>{this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.position);if(!i){const s=this.viewer.scene.globe.ellipsoid;i=this.viewer.scene.camera.pickEllipsoid(t.position,s)}if(i){if(this.positions.push(i),this.positions.length==1){this.createLineEntity(),this.createStartEntity();return}this.createVertex()}},Cesium.ScreenSpaceEventType.LEFT_CLICK)}mouseMoveEvent(){this.handler.setInputAction(t=>{if(!this.isMeasure)return;this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.endPosition);i||(i=this.viewer.scene.camera.pickEllipsoid(t.endPosition,this.viewer.scene.globe.ellipsoid)),i&&this.handleMoveEvent(i)},Cesium.ScreenSpaceEventType.MOUSE_MOVE)}handleMoveEvent(t){this.positions.length<1||(this.tempPositions=this.positions.concat([t]),!this.moveVertexEntity&&this.positions.length>=1&&(this.moveVertexEntity=this.viewer.entities.add({position:new Cesium.CallbackProperty(()=>this.tempPositions.length>0?this.tempPositions[this.tempPositions.length-1]:null,!1),point:{color:Cesium.Color.YELLOW,pixelSize:8,disableDepthTestDistance:500},label:{text:new Cesium.CallbackProperty(()=>this.tempPositions.length>1?this.getSpaceDistance(this.tempPositions)+"米":"",!1),scale:.5,font:"normal 24px MicroSoft YaHei",distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,5e3),scaleByDistance:new Cesium.NearFarScalar(1e3,1,3e3,.4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM,style:Cesium.LabelStyle.FILL_AND_OUTLINE,pixelOffset:new Cesium.Cartesian2(0,-30),outlineWidth:9,outlineColor:Cesium.Color.WHITE}})))}rightClickEvent(){this.handler.setInputAction(t=>{!this.isMeasure||this.positions.length<1?(this.deactivate(),this.clear()):(this.createEndEntity(),this.lineEntity.polyline={positions:this.positions,width:2,material:Cesium.Color.YELLOW,depthFailMaterial:Cesium.Color.YELLOW},this.measureEnd())},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}measureEnd(){this.deactivate(),this.measureDistance=this.getSpaceDistance(this.positions),this.MeasureEndEvent.raiseEvent(this.measureDistance)}unRegisterEvents(){this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)}getSpaceDistance(t){let i=0;for(let s=0;s<t.length-1;s++){const n=Cesium.Cartographic.fromCartesian(t[s]),o=Cesium.Cartographic.fromCartesian(t[s+1]),r=new Cesium.EllipsoidGeodesic;r.setEndPoints(n,o);let l=r.surfaceDistance;l=Math.sqrt(Math.pow(l,2)+Math.pow(o.height-n.height,2)),i=i+l}return i.toFixed(2)}}class pl{constructor(t){this.viewer=t,this.initEvents(),this.positions=[],this.vertexEntities=[],this.labelEntity=void 0,this.measureHeight=0}initEvents(){this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.MeasureStartEvent=new Cesium.Event,this.MeasureEndEvent=new Cesium.Event}activate(){this.deactivate(),this.registerEvents(),this.viewer.enableCursorStyle=!1,this.viewer._element.style.cursor="default",this.isMeasure=!0,this.circleRadius=.1,this.measureHeight=0,this.positions=[]}deactivate(){this.isMeasure&&(this.unRegisterEvents(),this.viewer._element.style.cursor="pointer",this.viewer.enableCursorStyle=!0,this.isMeasure=!1)}clear(){this.viewer.entities.remove(this.lineEntity),this.lineEntity=void 0,this.viewer.entities.remove(this.labelEntity),this.labelEntity=void 0,this.removeCircleEntity(),this.vertexEntities.forEach(t=>{this.viewer.entities.remove(t)}),this.vertexEntities=[]}createLineEntity(){this.lineEntity=this.viewer.entities.add({polyline:{positions:new Cesium.CallbackProperty(t=>this.positions,!1),width:2,material:Cesium.Color.YELLOW,depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.RED})}})}createLabel(){this.labelEntity=this.viewer.entities.add({position:new Cesium.CallbackProperty(t=>this.positions[this.positions.length-1],!1),label:{text:"",scale:.5,font:"normal 40px MicroSoft YaHei",distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,5e3),scaleByDistance:new Cesium.NearFarScalar(500,1,1500,.4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM,style:Cesium.LabelStyle.FILL_AND_OUTLINE,pixelOffset:new Cesium.Cartesian2(0,-30),outlineWidth:9,outlineColor:Cesium.Color.WHITE}})}createVertex(t){let i=this.viewer.entities.add({position:new Cesium.CallbackProperty(s=>this.positions[t],!1),type:"MeasureHeightVertex",point:{color:Cesium.Color.FUCHSIA,pixelSize:6}});this.vertexEntities.push(i)}createCircleEntitiy(){this.circleEntity=this.viewer.entities.add({position:new Cesium.CallbackProperty(t=>this.positions[this.positions.length-1],!1),ellipse:{height:new Cesium.CallbackProperty(t=>this.getPositionHeight(this.positions[this.positions.length-1]),!1),semiMinorAxis:new Cesium.CallbackProperty(t=>this.circleRadius,!1),semiMajorAxis:new Cesium.CallbackProperty(t=>this.circleRadius,!1),material:Cesium.Color.YELLOW.withAlpha(.5)}})}removeCircleEntity(){this.viewer.entities.remove(this.circleEntity),this.circleEntity=void 0}registerEvents(){this.leftClickEvent(),this.rightClickEvent(),this.mouseMoveEvent()}leftClickEvent(){this.handler.setInputAction(t=>{this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.position);if(!i){const s=this.viewer.scene.globe.ellipsoid;i=this.viewer.scene.camera.pickEllipsoid(t.position,s)}i&&(this.positions.length==0?(this.positions.push(i),this.createVertex(0),this.createLineEntity(),this.createCircleEntitiy(),this.createLabel()):this.measureEnd())},Cesium.ScreenSpaceEventType.LEFT_CLICK)}mouseMoveEvent(){this.handler.setInputAction(t=>{if(!this.isMeasure)return;this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.endPosition);i||(i=this.viewer.scene.camera.pickEllipsoid(t.endPosition,this.viewer.scene.globe.ellipsoid)),i&&this.handleMoveEvent(i)},Cesium.ScreenSpaceEventType.MOUSE_MOVE)}handleMoveEvent(t){if(this.positions.length<1)return;let i=this.cartesian3ToPoint3D(this.positions[0]),s=this.cartesian3ToPoint3D(t);const n=s.z-i.z;i.z=s.z;const o=Cesium.Cartesian3.fromDegrees(i.x,i.y,s.z);this.positions.length<2?(this.positions.push(o),this.createVertex(1)):(this.positions[1]=o,this.measureHeight=Math.abs(n).toFixed(3),this.labelEntity.label.text="高度："+this.measureHeight+" 米"),this.circleRadius=this.getDistanceH(this.positions[0],t)}rightClickEvent(){this.handler.setInputAction(t=>{this.isMeasure&&(this.deactivate(),this.clear())},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}measureEnd(){this.deactivate(),this.MeasureEndEvent.raiseEvent(this.measureHeight)}unRegisterEvents(){this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)}getPositionHeight(t){return Cesium.Cartographic.fromCartesian(t).height}cartesian3ToPoint3D(t){const i=Cesium.Cartographic.fromCartesian(t),s=Cesium.Math.toDegrees(i.longitude),n=Cesium.Math.toDegrees(i.latitude),o=i.height;return{x:s,y:n,z:o}}getDistanceH(t,i){const s=this.cartesian3ToPoint3D(t),n=this.cartesian3ToPoint3D(i),o=new Cesium.EllipsoidGeodesic,r=Cesium.Cartographic.fromDegrees(s.x,s.y),l=Cesium.Cartographic.fromDegrees(n.x,n.y);return o.setEndPoints(r,l),o.surfaceDistance}}class gl{constructor(t){this.viewer=t,this.initEvents(),this.positions=[],this.tempPositions=[],this.vertexEntities=[],this.labelEntity=void 0,this.measureArea=0}initEvents(){this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.MeasureStartEvent=new Cesium.Event,this.MeasureEndEvent=new Cesium.Event}activate(){this.deactivate(),this.registerEvents(),this.viewer.enableCursorStyle=!1,this.viewer._element.style.cursor="default",this.isMeasure=!0,this.measureArea=0}deactivate(){this.isMeasure&&(this.unRegisterEvents(),this.viewer._element.style.cursor="pointer",this.viewer.enableCursorStyle=!0,this.isMeasure=!1,this.tempPositions=[],this.positions=[],this.height=void 0)}clear(){this.viewer.entities.remove(this.polygonEntity),this.polygonEntity=void 0,this.vertexEntities.forEach(t=>{this.viewer.entities.remove(t)}),this.vertexEntities=[],this.viewer.entities.remove(this.mesureResultEntity),this.mesureResultEntity=void 0,this.height=void 0}createPolygonEntity(){this.polygonEntity=this.viewer.entities.add({polygon:{hierarchy:new Cesium.CallbackProperty(t=>new Cesium.PolygonHierarchy(this.tempPositions),!1),material:Cesium.Color.RED.withAlpha(.4),perPositionHeight:!0},polyline:{positions:new Cesium.CallbackProperty(t=>this.tempPositions.concat(this.tempPositions[0]),!1),width:1,material:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.YELLOW}),depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.YELLOW})}})}createVertex(){let t=this.viewer.entities.add({position:this.positions[this.positions.length-1],type:"MeasureAreaVertex",point:{color:Cesium.Color.FUCHSIA,pixelSize:8,disableDepthTestDistance:500}});this.vertexEntities.push(t)}createResultLabel(){this.mesureResultEntity=this.viewer.entities.add({position:new Cesium.CallbackProperty(t=>this.getCenterPosition(),!1),type:"MeasureAreaResult",label:{text:new Cesium.CallbackProperty(t=>"面积："+this.computeArea(this.tempPositions)+"平方米",!1),scale:.5,font:"normal 28px MicroSoft YaHei",distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,5e3),scaleByDistance:new Cesium.NearFarScalar(1e3,1,3e3,.4),verticalOrigin:Cesium.VerticalOrigin.BOTTOM,style:Cesium.LabelStyle.FILL_AND_OUTLINE,pixelOffset:new Cesium.Cartesian2(0,-30),outlineWidth:9,outlineColor:Cesium.Color.YELLOW}})}getCenterPosition(){if(this.tempPositions.length<3)return this.tempPositions[0];let t=0,i=0,s=0;for(let n=0;n<this.tempPositions.length;n++){const o=this.cartesian3ToPoint3D(this.tempPositions[n]);t+=o.x,i+=o.y,s+=o.z}return t/=this.tempPositions.length,i/=this.tempPositions.length,s/=this.tempPositions.length,Cesium.Cartesian3.fromDegrees(t,i,this.height||s)}registerEvents(){this.leftClickEvent(),this.rightClickEvent(),this.mouseMoveEvent()}leftClickEvent(){this.handler.setInputAction(t=>{this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.position);if(!i){const s=this.viewer.scene.globe.ellipsoid;i=this.viewer.scene.camera.pickEllipsoid(t.position,s)}i&&(this.positions.push(i),this.height=this.unifiedHeight(this.positions,this.height),this.positions.length==1&&this.createPolygonEntity(),this.createVertex())},Cesium.ScreenSpaceEventType.LEFT_CLICK)}mouseMoveEvent(){this.handler.setInputAction(t=>{if(!this.isMeasure)return;this.viewer._element.style.cursor="default";let i=this.viewer.scene.pickPosition(t.endPosition);i||(i=this.viewer.scene.camera.pickEllipsoid(t.endPosition,this.viewer.scene.globe.ellipsoid)),i&&this.handleMoveEvent(i)},Cesium.ScreenSpaceEventType.MOUSE_MOVE)}handleMoveEvent(t){this.positions.length<1||(this.height=this.unifiedHeight(this.positions,this.height),this.tempPositions=this.positions.concat([t]),this.tempPositions.length>=3&&!this.mesureResultEntity&&this.createResultLabel())}unifiedHeight(t,i){i||(i=this.getPositionHeight(t[0]));let s;for(let n=0;n<t.length;n++){const o=t[n];s=this.cartesian3ToPoint3D(o),t[n]=Cesium.Cartesian3.fromDegrees(s.x,s.y,i)}return i}getPositionHeight(t){return Cesium.Cartographic.fromCartesian(t).height}cartesian3ToPoint3D(t){const i=Cesium.Cartographic.fromCartesian(t),s=Cesium.Math.toDegrees(i.longitude),n=Cesium.Math.toDegrees(i.latitude);return{x:s,y:n,z:i.height}}computeArea(t){if(t.length<3)return 0;let i=[];for(let r=0;r<t.length;r++){const l=this.cartesian3ToPoint3D(t[r]);i.push([l.x,l.y])}let s=0,n=i.length-1;for(let r=0;r<i.length;r++){const l=i[r],a=i[n],m=l[0]*Math.PI/180,h=l[1]*Math.PI/180,f=a[0]*Math.PI/180;a[1]*Math.PI/180,s+=(f-m)*Math.sin(h),n=r}const o=6378137;return s=Math.abs(s*o*o/2),s.toFixed(2)}rightClickEvent(){this.handler.setInputAction(t=>{!this.isMeasure||this.positions.length<3?(this.deactivate(),this.clear()):(this.tempPositions=[...this.positions],this.polygonEntity.polyline={positions:this.positions.concat(this.positions[0]),width:2,material:Cesium.Color.YELLOW,depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.YELLOW})},this.polygonEntity.polygon.hierarchy=new Cesium.PolygonHierarchy(this.tempPositions),this.mesureResultEntity.position=this.getCenterPosition(),this.mesureResultEntity.label.text="总面积："+this.computeArea(this.positions)+"平方米",this.measureArea=this.computeArea(this.positions),this.measureEnd())},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}measureEnd(){this.deactivate(),this.MeasureEndEvent.raiseEvent(this.measureArea)}unRegisterEvents(){this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)}}const ml=(e,t)=>{const i=e.__vccOpts||e;for(const[s,n]of t)i[s]=n;return i},Cl={id:"app"},vl=["innerHTML"],yl={key:2,id:"pointModal",class:"modal"},El={class:"modal-content"},bl={class:"form-group"},xl={class:"form-group"},wl={class:"form-group"},Al={__name:"App",setup(e){const t=["对象015","对象022"],i=["对象007","对象012","Md_DianTi_006","对象031"],s=["对象006","对象011","对象021","对象030"],n=["对象005","对象010","对象020","对象029"],o=["对象004","对象013","对象019","对象028"],r=["对象003","对象014","对象018","对象027"],l=["对象002","对象009","对象017","对象026"],a=["对象001","对象008","对象016","对象025"],m=["Md_WuDing_006","Md_WuDing_005"];Cesium.Ion.defaultAccessToken="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJiOThlNzQ2Zi1jNWZhLTQwYmUtOWRiYi01Y2MzM2YyYzgxODMiLCJpZCI6MjU0NDE5LCJpYXQiOjE3MzEzMjU3OTV9.P4W0WtGb9-78VX5akp-p731bjn2XeUWwKueOHOlVduY";const h=se(null);let f=null,S=null;const L=se(120.194911),V=se(33.352104),H=se(55),ee=se(0),Q=se(120.18057615702192),G=se(33.34459424874954),U=se(55),P=se(1217.345908737083),q=se(!1),_e=se(""),fe=se(!1),ye=se(!1),xe=se(null),X=se({name:"",description:"",color:"YELLOW"});let he=null,de=null,pe=null,we=null;const gt=g=>{console.log(g),g.key==="w"&&f.camera.moveForward(10),g.key==="s"&&f.camera.moveBackward(10),g.key==="a"&&f.camera.moveLeft(10),g.key==="d"&&f.camera.moveRight(10),g.key==="e"&&f.camera.moveUp(10),g.key==="q"&&f.camera.moveDown(10);const d=f.camera.position,C=Cesium.Cartographic.fromCartesian(d),M=Cesium.Math.toDegrees(C.longitude),I=Cesium.Math.toDegrees(C.latitude),_=C.height;console.log(`longitude:${M},  latitude: ${I}, 高度: ${_}`)},z=se([{name:"标点1",longitude:120.1891222671583,latitude:33.35383753423389,height:62.426616633977744,labelText:"A1#西伏河菁英公寓",font:"500 30px Helvetica",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是A1#西伏河菁英公寓的详细信息。</p>"},{name:"标点2",longitude:120.18954221604284,latitude:33.35413596675203,height:62.426616633977744,labelText:"A2#西伏河菁英公寓",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层2的详细信息。</p>"},{name:"标点3",longitude:120.18948117113337,latitude:33.35330813501644,height:46.120346620566785,labelText:"B1#现代农业科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层3的详细信息。</p>"},{name:"标点4",longitude:120.19034916408134,latitude:33.35362420093073,height:35.23555939324899,labelText:"B2#未来产业培育中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层4的详细信息。</p>"},{name:"标点5",longitude:120.19004041902282,latitude:33.352841788303266,height:35.23555939324899,labelText:"B3#未来产业培育中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层5的详细信息。</p>"},{name:"标点6",longitude:120.1906372887605,latitude:33.35309779486584,height:35.23555939324899,labelText:"B4#光伏科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层6的详细信息。</p>"},{name:"标点7",longitude:120.18829716205744,latitude:33.35227913709945,height:41.25962076008002,labelText:"B5#氢能科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层7的详细信息。</p>"},{name:"标点8",longitude:120.18904148021537,latitude:33.35142431593994,height:41.25962076008002,labelText:"B6#储能科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层8的详细信息。</p>"},{name:"标点9",longitude:120.18983718178865,latitude:33.350653487205946,height:41.25962076008002,labelText:"B7#海洋经济科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层9的详细信息。</p>"},{name:"标点10",longitude:120.19046266145415,latitude:33.34974211313412,height:41.25962076008002,labelText:"B8#风电科创中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点11",longitude:120.19023587561483,latitude:33.35233355356273,height:41.25962076008002,labelText:"B9#江苏沿海零碳服务中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点12",longitude:120.19088448005887,latitude:33.35253998220558,height:41.25962076008002,labelText:"B10#中科院电工所到功率电力电子实验室",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点13",longitude:120.19080896797266,latitude:33.35166701022569,height:41.25962076008002,labelText:"B11#江苏可再生能源技术创新中心",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点14",longitude:120.19147190031437,latitude:33.3521318769472,height:41.25962076008002,labelText:"楼层14",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点15",longitude:120.19125063747177,latitude:33.35118226992003,height:41.25962076008002,labelText:"楼层15",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点16",longitude:120.1917953866177,latitude:33.350730614356515,height:41.25962076008002,labelText:"楼层16",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点17",longitude:120.19212424737852,latitude:33.35128999758356,height:94.15576542541079,labelText:"楼层17",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"},{name:"标点18",longitude:120.1915056877939,latitude:33.349329946676534,height:22.23163099390385,labelText:"楼层18",font:"400 24px Arial",labelColor:Cesium.Color.YELLOW,iconImage:$,description:"<p>这是楼层10的详细信息。</p>"}]);dn(async()=>{try{window.addEventListener("message",Ie,!1);const g=await Cesium.createWorldTerrainAsync();f=new Cesium.Viewer(h.value,{timeline:!1,animation:!1,geocoder:!1,homeButton:!1,sceneModePicker:!1,baseLayerPicker:!1,navigationHelpButton:!1,infoBox:!1,selectionIndicator:!1,fullscreenButton:!1,shadows:!1,shouldAnimate:!0,terrainProvider:g}),await f.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(Q.value,G.value,P.value),orientation:{heading:Cesium.Math.toRadians(U.value),pitch:Cesium.Math.toRadians(-45),roll:0},duration:1}),S=await Ee(L.value,V.value,ee.value,H.value);const d=Cesium.Cartesian3.fromDegreesArray([120.189771,33.354834,120.186688,33.352799,120.191011,33.347168,120.194683,33.349288,120.189771,33.354834]),C=f.entities.add({name:"立体墙效果",wall:{positions:d,maximumHeights:new Array(d.length).fill(50),minimunHeights:new Array(d.length).fill(0),material:new Cesium.Property.WallDiffuseMaterialProperty({color:new Cesium.Color.fromBytes(57.197,187),scanLineHeight:0})}});z.value.forEach(I=>{ze(I)}),j(),new Cesium.ScreenSpaceEventHandler(f.scene.canvas).setInputAction(function(I){const _=f.scene.pick(I.position);if(ye.value){const w=f.scene.pickPosition(I.position);if(Cesium.defined(w)){const O=Cesium.Cartographic.fromCartesian(w),D=Cesium.Math.toDegrees(O.longitude),Y=Cesium.Math.toDegrees(O.latitude),J=O.height;console.log(`点击位置: 经度=${D}, 纬度=${Y}, 高度=${J}`),xe.value={longitude:D,latitude:Y,height:J},fe.value=!0;return}}if(Cesium.defined(_)&&Cesium.defined(_.id)){const w=_.id;if(w.name){const O=w.name,D=w.label?w.label.text.getValue(f.clock.currentTime):O;p({type:"cesiumEntityClicked",data:{name:O,labelText:D}})}if(w.position&&typeof w.position.getValue=="function"){const O=w.position.getValue(f.clock.currentTime);if(O){let D;w.boundingVolume?D=Cesium.BoundingSphere.fromBoundingVolume(w.boundingVolume,f.scene.globe.ellipsoid):D=new Cesium.BoundingSphere(O,100);const Y=D.radius*2.5,J=f.camera.heading,Me=Cesium.Math.toRadians(-45),Wt=new Cesium.HeadingPitchRange(J,Me,Y);f.flyTo(w,{duration:2,offset:Wt})}else console.warn(`实体 ${w.name} 没有有效的位置。`)}else console.warn(`实体 ${w.name} 没有有效的位置。`)}else q.value=!1},Cesium.ScreenSpaceEventType.LEFT_CLICK),document.addEventListener("keydown",gt),f.cesiumWidget&&f.cesiumWidget.creditContainer&&(f.cesiumWidget.creditContainer.style.display="none"),K()}catch(g){console.error("Cesium Viewer initialization failed:",g)}});function K(){if(!f){console.error("Viewer未初始化，无法创建测量工具");return}try{console.log("正在初始化测量工具..."),he=new dl(f),de=new pl(f),pe=new gl(f),he&&he.MeasureEndEvent&&he.MeasureEndEvent.addEventListener(g=>{console.log("距离测量结果:",g,"米")}),de&&de.MeasureEndEvent&&de.MeasureEndEvent.addEventListener(g=>{console.log("高度测量结果:",g,"米")}),pe&&pe.MeasureEndEvent&&pe.MeasureEndEvent.addEventListener(g=>{console.log("面积测量结果:",g,"平方米")}),console.log("测量工具初始化完成")}catch(g){console.error("测量工具初始化失败:",g,g.stack),he=null,de=null,pe=null}}function j(){z.value.forEach(g=>{console.log(`名称: ${g.name}`),console.log(`描述: ${g.description}`),console.log("-----------")})}pn(()=>{f&&(f.destroy(),f=null),window.removeEventListener("message",Ie),document.removeEventListener("keydown",gt)});const Ie=g=>{if(!g.data||typeof g.data!="object")return;const{type:d,data:C}=g.data;console.log("[Cesium] 收到消息:",{type:d,data:C}),d==="cesiumCommand"&&lt(C)};function lt(g){const{command:d,params:C}=g||{};try{switch(d){case"jumpToLocation1":C&&C.camLongitude&&C.camLatitude&&Ct(C.camLongitude,C.camLatitude,C.camHeight||1e3);break;case"jumpToLocation0":Bt();break;case"jumpToLocation2":Xe();break;case"jumpToLocation3":ns();break;case"toggleFloor":(C==null?void 0:C.floor)!==void 0&&(console.log(`[Cesium] 切换到楼层: ${C.floor}`),mt(Ae("reset")),C.floor!=="reset"&&Vt(Ae(C.floor)));break;case"resetFloor":console.log("[Cesium] 重置所有楼层"),mt(Ae("reset"));break;case"jumpToEntity":C!=null&&C.entityName&&c(C.entityName);break;case"addPoint":console.log("[Cesium] 接收到添加标签命令"),(C==null?void 0:C.action)==="activate"&&A();break;case"distanceMeasure":console.log("[Cesium] 接收到距离测量命令"),(C==null?void 0:C.action)==="activate"&&E();break;case"heightMeasure":console.log("[Cesium] 接收到高度测量命令"),(C==null?void 0:C.action)==="activate"&&v();break;case"areaMeasure":console.log("[Cesium] 接收到面积测量命令"),(C==null?void 0:C.action)==="activate"&&y();break;case"clearMeasure":console.log("[Cesium] 接收到清除测量命令"),T();break;default:console.warn(`[Cesium] 未识别的命令: ${d}`)}}catch(M){console.error(`[Cesium] 命令执行失败: ${d}`,M)}}function Ae(g){return console.log(`[Cesium] 获取楼层节点: ${g}`),{0:[...t],1:[...i,...s,...n,...o,...r,...l,...a,...m],2:[...s,...n,...o,...r,...l,...a,...m],3:[...n,...o,...r,...l,...a,...m],4:[...o,...r,...l,...a,...m],5:[...r,...l,...a,...m],6:[...l,...a,...m],7:[...a,...m],all:[...t,...i,...s,...n,...o,...r,...l,...a,...m],reset:[...t,...i,...s,...n,...o,...r,...l,...a,...m]}[g]||[]}async function Ee(g,d,C,M){const I=Cesium.Cartesian3.fromDegrees(g,d,C),_=Cesium.Math.toRadians(M),w=Cesium.Math.toRadians(0),O=Cesium.Math.toRadians(0),D=new Cesium.HeadingPitchRoll(_,w,O),Y=Cesium.Transforms.headingPitchRollToFixedFrame(I,D),J=await Cesium.Model.fromGltfAsync({url:"model/xifu3.glb",modelMatrix:Y,minimumPixelSize:64,maximumScale:2e4,scale:.5});return f.scene.primitives.add(J),J}function Vt(g){mt([...i,...s,...n,...o,...r,...l,...a,...m]),g.forEach(d=>{pi(d)})}function pi(g){const d=S.getNode(g);let C=20,M=0;const I=C/(1*60),_=Cesium.Matrix4.clone(d.matrix);function w(){M<C?M+=I:(M=C,f.scene.preRender.removeEventListener(w));const O=Cesium.Cartesian3.fromElements(0,0,M),D=Cesium.Matrix4.fromTranslation(O);d.matrix=Cesium.Matrix4.multiply(_,D,new Cesium.Matrix4)}f.scene.preRender.addEventListener(w)}function mt(g){g.forEach(d=>{Ze(d)})}function Ze(g){const d=S.getNode(g);d.matrix=Cesium.Matrix4.clone(d.originalMatrix)}function ze(g){const{longitude:d,latitude:C,height:M,labelText:I,font:_,labelColor:w,iconImage:O,description:D}=g;return f.entities.add({name:g.name,position:Cesium.Cartesian3.fromDegrees(d,C,M),label:{text:I,font:_,scale:.5,style:Cesium.LabelStyle.FILL,fillColor:w,pixelOffset:new Cesium.Cartesian2(0,-45),distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,1500)},billboard:{width:20,height:20,image:O,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,verticalOrigin:Cesium.VerticalOrigin.BOTTOM,distanceDisplayCondition:new Cesium.DistanceDisplayCondition(0,2e3)},description:D||""})}function Ct(g,d,C){f.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(g,d,C),orientation:{heading:Cesium.Math.toRadians(U.value),pitch:Cesium.Math.toRadians(-45),roll:0},duration:2})}function Bt(){f.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(120.18057615702192,33.34459424874954,1217.3459087375948),orientation:{heading:Cesium.Math.toRadians(U.value),pitch:Cesium.Math.toRadians(-45),roll:0},duration:2})}function Xe(){f.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(120.18521125510068,33.347694998604126,631.0488822977416),orientation:{heading:Cesium.Math.toRadians(U.value),pitch:Cesium.Math.toRadians(-45),roll:0},duration:2})}function ns(){f.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(120.18749039820405,33.351791030928524,118.79728977059871),orientation:{heading:Cesium.Math.toRadians(U.value),pitch:Cesium.Math.toRadians(-45),roll:0},duration:2})}function c(g){console.log(`[Cesium] 尝试跳转到实体: ${g}`);let d=u(g);if(!d){if(console.log("[Cesium] 未找到精确匹配实体，尝试查找相似名称..."),g.includes("-")&&g.includes("#")){const C=g.split("#"),I=C[0].replace(/-/g,"")+"#"+(C[1]||"");console.log(`[Cesium] 尝试查找替代名称: ${I}`),d=u(I)}if(!d&&/\d+/.test(g)){const C=g.match(/\d+/g);if(C&&C.length>0){console.log(`[Cesium] 尝试查找包含数字 ${C[0]} 的实体`);const M=f.entities.values;for(let I=0;I<M.length;I++){const _=M[I];if(_.label&&_.label.text){const w=_.label.text.getValue(f.clock.currentTime);if(w.includes(C[0])){console.log(`[Cesium] 找到包含相同数字的实体: ${w}`),d=_;break}}}}}}if(d){console.log("[Cesium] 找到实体，准备跳转...");const C=d.position.getValue(f.clock.currentTime);if(C){const I=new Cesium.BoundingSphere(C,100).radius*2.5,_=f.camera.heading,w=Cesium.Math.toRadians(-45),O=new Cesium.HeadingPitchRange(_,w,I);return f.flyTo(d,{duration:2,offset:O}),console.log(`[Cesium] 成功跳转到实体: ${d.label.text.getValue(f.clock.currentTime)}`),!0}else console.error("[Cesium] 实体没有有效位置")}else{console.warn("[Cesium] 未找到实体，尝试使用默认视角...");const C=f.entities.values;let M=null,I=0;for(let _=0;_<C.length;_++){const w=C[_];if(w.label&&w.label.text){const O=w.label.text.getValue(f.clock.currentTime);let D=0;const Y=g.toLowerCase(),J=O.toLowerCase();for(let Me=0;Me<Y.length;Me++)J.includes(Y[Me])&&D++;D>I&&(I=D,M=w)}}if(M&&I>g.length/2){console.log(`[Cesium] 找到最佳匹配实体: ${M.label.text.getValue(f.clock.currentTime)}`);const _=M.position.getValue(f.clock.currentTime);if(_){const O=new Cesium.BoundingSphere(_,100).radius*2.5,D=f.camera.heading,Y=Cesium.Math.toRadians(-45),J=new Cesium.HeadingPitchRange(D,Y,O);return f.flyTo(M,{duration:2,offset:J}),console.log("[Cesium] 成功跳转到最佳匹配实体"),!0}}else console.error("[Cesium] 未找到任何匹配实体，无法跳转")}return console.warn(`[Cesium] 未找到实体或无法跳转: ${g}`),!1}function u(g){console.log(`[Cesium] 开始查找实体: "${g}"`);const d=g.toLowerCase().replace(/\s+/g,""),C=[],M=f.entities.values;console.log(`[Cesium] 实体总数: ${M.length}`);for(let _=0;_<M.length;_++){const w=M[_];if(w.label&&w.label.text){const O=w.label.text.getValue(f.clock.currentTime);if(C.push(O),O.toLowerCase().replace(/\s+/g,""),O===g)return console.log(`[Cesium] 找到完全匹配: "${O}"`),w}}for(let _=0;_<M.length;_++){const w=M[_];if(w.label&&w.label.text){const O=w.label.text.getValue(f.clock.currentTime),D=O.toLowerCase().replace(/\s+/g,"");if(D.includes(d))return console.log(`[Cesium] 找到包含匹配: "${O}" 包含 "${g}"`),w;if(d.includes(D))return console.log(`[Cesium] 找到反向包含匹配: "${g}" 包含 "${O}"`),w}}const I=d.replace(/-/g,"");for(let _=0;_<M.length;_++){const w=M[_];if(w.label&&w.label.text){const O=w.label.text.getValue(f.clock.currentTime),D=O.toLowerCase().replace(/\s+/g,"").replace(/-/g,"");if(D.includes(I)||I.includes(D))return console.log(`[Cesium] 找到移除连字符后的匹配: "${O}" 与 "${g}"`),w}}if(d.includes("#")){const w=d.split("#")[0].replace(/-/g,"");for(let O=0;O<M.length;O++){const D=M[O];if(D.label&&D.label.text){const Y=D.label.text.getValue(f.clock.currentTime);if(Y.toLowerCase().replace(/\s+/g,"").includes(w+"#"))return console.log(`[Cesium] 找到前缀匹配: "${Y}" 与 "${g}"`),D}}}return console.log("[Cesium] 未找到匹配实体。可用的实体标签:",C),null}function p(g){try{window.parent!==window?(window.parent.postMessage(g,"*"),console.log("[Cesium] 已发送消息到父窗口:",g)):console.log("[Cesium] 不在iframe中，无法发送消息:",g)}catch(d){console.error("[Cesium] 发送消息失败:",d)}}function E(){f&&he?(T(),he.activate(),we=he):console.error("距离测量工具未初始化")}function v(){f&&de?(T(),de.activate(),we=de):console.error("高度测量工具未初始化")}function y(){f&&pe?(T(),pe.activate(),we=pe):console.error("面积测量工具未初始化")}function T(){we&&(we.deactivate(),we.clear(),we=null)}function A(){T(),ye.value=!0,f&&f.container&&(f.container.style.cursor="crosshair")}function x(){fe.value=!1,ye.value=!1,xe.value=null,X.value={name:"",description:"",color:"YELLOW"},f&&f.container&&(f.container.style.cursor="default")}function b(){if(!xe.value){alert("位置无效，请重新选择");return}if(!X.value.name){alert("请输入标签名称");return}const g={name:`标点_${z.value.length+1}`,longitude:xe.value.longitude,latitude:xe.value.latitude,height:xe.value.height,labelText:X.value.name,font:"400 24px Arial",labelColor:Cesium.Color[X.value.color],iconImage:$,description:X.value.description||""};z.value.push(g),ze(g),x(),console.log("已添加自定义标签:",g)}return(g,d)=>(Kt(),wi("div",Cl,[Ai("",!0),ie("div",{id:"cesiumContainer",ref_key:"cesiumContainer",ref:h},null,512),q.value?(Kt(),wi("div",{key:1,id:"infoBox",innerHTML:_e.value},null,8,vl)):Ai("",!0),fe.value?(Kt(),wi("div",yl,[ie("div",El,[d[25]||(d[25]=ie("h3",null,"添加自定义标签",-1)),ie("div",bl,[d[21]||(d[21]=ie("label",{for:"pointName"},"标签名称:",-1)),yi(ie("input",{type:"text",id:"pointName","onUpdate:modelValue":d[12]||(d[12]=C=>X.value.name=C)},null,512),[[Rs,X.value.name]])]),ie("div",xl,[d[22]||(d[22]=ie("label",{for:"pointDesc"},"标签描述:",-1)),yi(ie("textarea",{id:"pointDesc","onUpdate:modelValue":d[13]||(d[13]=C=>X.value.description=C)},null,512),[[Rs,X.value.description]])]),ie("div",wl,[d[24]||(d[24]=ie("label",{for:"pointColor"},"标签颜色:",-1)),yi(ie("select",{id:"pointColor","onUpdate:modelValue":d[14]||(d[14]=C=>X.value.color=C)},d[23]||(d[23]=[Ir('<option value="YELLOW" data-v-dcb76ed1>黄色</option><option value="RED" data-v-dcb76ed1>红色</option><option value="GREEN" data-v-dcb76ed1>绿色</option><option value="BLUE" data-v-dcb76ed1>蓝色</option><option value="WHITE" data-v-dcb76ed1>白色</option>',5)]),512),[[ll,X.value.color]])]),ie("div",{class:"modal-buttons"},[ie("button",{onClick:b},"保存"),ie("button",{onClick:x},"取消")])])])):Ai("",!0)]))}},Ml=ml(Al,[["__scopeId","data-v-dcb76ed1"]]);ul(Ml).mount("#app");
