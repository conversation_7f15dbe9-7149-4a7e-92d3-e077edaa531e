/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.127
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as Rn}from"./chunk-PWAJ3RAI.js";import{a as M}from"./chunk-ANLJ4KBN.js";import{a as re}from"./chunk-ID6SFQTL.js";import{a as sn}from"./chunk-VLPNAR64.js";import{b as on}from"./chunk-GE5NEIZC.js";import{a as Pt,c as or,d as sr,e as Le}from"./chunk-35CVRQTC.js";var An=or((st,Et)=>{var pt=function(){var o=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0;return typeof __filename<"u"&&(o=o||__filename),function(f){f=f||{};var r=typeof f<"u"?f:{},C,l;r.ready=new Promise(function(e,t){C=e,l=t});var E={},F;for(F in r)r.hasOwnProperty(F)&&(E[F]=r[F]);var g=[],m="./this.program",I=function(e,t){throw t},N=!1,d=!1,P=!1,Q=!1;N=typeof window=="object",d=typeof importScripts=="function",P=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",Q=!N&&!P&&!d;var u="";function W(e){return r.locateFile?r.locateFile(e,u):u+e}var J,ae,j,Be,Ce,Fe;P?(d?u=Pt("path").dirname(u)+"/":u=__dirname+"/",J=function(t,n){return Ce||(Ce=Pt("fs")),Fe||(Fe=Pt("path")),t=Fe.normalize(t),Ce.readFileSync(t,n?null:"utf8")},j=function(t){var n=J(t,!0);return n.buffer||(n=new Uint8Array(n)),D(n.buffer),n},process.argv.length>1&&(m=process.argv[1].replace(/\\/g,"/")),g=process.argv.slice(2),process.on("uncaughtException",function(e){if(!(e instanceof _r))throw e}),process.on("unhandledRejection",Oe),I=function(e){process.exit(e)},r.inspect=function(){return"[Emscripten Module object]"}):Q?(typeof read<"u"&&(J=function(t){return read(t)}),j=function(t){var n;return typeof readbuffer=="function"?new Uint8Array(readbuffer(t)):(n=read(t,"binary"),D(typeof n=="object"),n)},typeof scriptArgs<"u"?g=scriptArgs:typeof arguments<"u"&&(g=arguments),typeof quit=="function"&&(I=function(e){quit(e)}),typeof print<"u"&&(typeof console>"u"&&(console={}),console.log=print,console.warn=console.error=typeof printErr<"u"?printErr:print)):(N||d)&&(d?u=self.location.href:typeof document<"u"&&document.currentScript&&(u=document.currentScript.src),o&&(u=o),u.indexOf("blob:")!==0?u=u.substr(0,u.lastIndexOf("/")+1):u="",J=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},d&&(j=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),ae=function(e,t,n){var _=new XMLHttpRequest;_.open("GET",e,!0),_.responseType="arraybuffer",_.onload=function(){if(_.status==200||_.status==0&&_.response){t(_.response);return}n()},_.onerror=n,_.send(null)},Be=function(e){document.title=e});var it=r.print||console.log.bind(console),oe=r.printErr||console.warn.bind(console);for(F in E)E.hasOwnProperty(F)&&(r[F]=E[F]);E=null,r.arguments&&(g=r.arguments),r.thisProgram&&(m=r.thisProgram),r.quit&&(I=r.quit);var Rt=0,vt=function(e){Rt=e},se;r.wasmBinary&&(se=r.wasmBinary);var ln=r.noExitRuntime||!0;typeof WebAssembly!="object"&&Oe("no native wasm support detected");var de,Ie=!1,ce;function D(e,t){e||Oe("Assertion failed: "+t)}var be=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function we(e,t,n){for(var _=t+n,s=t;e[s]&&!(s>=_);)++s;if(s-t>16&&e.subarray&&be)return be.decode(e.subarray(t,s));for(var a="";t<s;){var c=e[t++];if(!(c&128)){a+=String.fromCharCode(c);continue}var i=e[t++]&63;if((c&224)==192){a+=String.fromCharCode((c&31)<<6|i);continue}var R=e[t++]&63;if((c&240)==224?c=(c&15)<<12|i<<6|R:c=(c&7)<<18|i<<12|R<<6|e[t++]&63,c<65536)a+=String.fromCharCode(c);else{var T=c-65536;a+=String.fromCharCode(55296|T>>10,56320|T&1023)}}return a}function Se(e,t){return e?we(p,e,t):""}function He(e,t,n,_){if(!(_>0))return 0;for(var s=n,a=n+_-1,c=0;c<e.length;++c){var i=e.charCodeAt(c);if(i>=55296&&i<=57343){var R=e.charCodeAt(++c);i=65536+((i&1023)<<10)|R&1023}if(i<=127){if(n>=a)break;t[n++]=i}else if(i<=2047){if(n+1>=a)break;t[n++]=192|i>>6,t[n++]=128|i&63}else if(i<=65535){if(n+2>=a)break;t[n++]=224|i>>12,t[n++]=128|i>>6&63,t[n++]=128|i&63}else{if(n+3>=a)break;t[n++]=240|i>>18,t[n++]=128|i>>12&63,t[n++]=128|i>>6&63,t[n++]=128|i&63}}return t[n]=0,n-s}function Xe(e,t,n){return He(e,p,t,n)}function at(e){for(var t=0,n=0;n<e.length;++n){var _=e.charCodeAt(n);_>=55296&&_<=57343&&(_=65536+((_&1023)<<10)|e.charCodeAt(++n)&1023),_<=127?++t:_<=2047?t+=2:_<=65535?t+=3:t+=4}return t}var Ge=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0;function xe(e,t){for(var n=e,_=n>>1,s=_+t/2;!(_>=s)&&Ae[_];)++_;if(n=_<<1,n-e>32&&Ge)return Ge.decode(p.subarray(e,n));for(var a="",c=0;!(c>=t/2);++c){var i=k[e+c*2>>1];if(i==0)break;a+=String.fromCharCode(i)}return a}function ke(e,t,n){if(n===void 0&&(n=2147483647),n<2)return 0;n-=2;for(var _=t,s=n<e.length*2?n/2:e.length,a=0;a<s;++a){var c=e.charCodeAt(a);k[t>>1]=c,t+=2}return k[t>>1]=0,t-_}function $e(e){return e.length*2}function ct(e,t){for(var n=0,_="";!(n>=t/4);){var s=L[e+n*4>>2];if(s==0)break;if(++n,s>=65536){var a=s-65536;_+=String.fromCharCode(55296|a>>10,56320|a&1023)}else _+=String.fromCharCode(s)}return _}function At(e,t,n){if(n===void 0&&(n=2147483647),n<4)return 0;for(var _=t,s=_+n-4,a=0;a<e.length;++a){var c=e.charCodeAt(a);if(c>=55296&&c<=57343){var i=e.charCodeAt(++a);c=65536+((c&1023)<<10)|i&1023}if(L[t>>2]=c,t+=4,t+4>s)break}return L[t>>2]=0,t-_}function ft(e){for(var t=0,n=0;n<e.length;++n){var _=e.charCodeAt(n);_>=55296&&_<=57343&&++n,t+=4}return t}function Tt(e,t){return e%t>0&&(e+=t-e%t),e}var Ne,U,p,k,Ae,L,z,Ut,ht;function yt(e){Ne=e,r.HEAP8=U=new Int8Array(e),r.HEAP16=k=new Int16Array(e),r.HEAP32=L=new Int32Array(e),r.HEAPU8=p=new Uint8Array(e),r.HEAPU16=Ae=new Uint16Array(e),r.HEAPU32=z=new Uint32Array(e),r.HEAPF32=Ut=new Float32Array(e),r.HEAPF64=ht=new Float64Array(e)}var ur=r.INITIAL_MEMORY||16777216,Ve,gt=[],Lt=[],Mn=[],mt=[],Kn=!1;function Bn(){if(r.preRun)for(typeof r.preRun=="function"&&(r.preRun=[r.preRun]);r.preRun.length;)Sn(r.preRun.shift());We(gt)}function Cn(){Kn=!0,We(Lt)}function Fn(){We(Mn)}function dn(){if(r.postRun)for(typeof r.postRun=="function"&&(r.postRun=[r.postRun]);r.postRun.length;)Nn(r.postRun.shift());We(mt)}function Sn(e){gt.unshift(e)}function Gn(e){Lt.unshift(e)}function Nn(e){mt.unshift(e)}var fe=0,ut=null,Pe=null;function Vn(e){fe++,r.monitorRunDependencies&&r.monitorRunDependencies(fe)}function Pn(e){if(fe--,r.monitorRunDependencies&&r.monitorRunDependencies(fe),fe==0&&(ut!==null&&(clearInterval(ut),ut=null),Pe)){var t=Pe;Pe=null,t()}}r.preloadedImages={},r.preloadedAudios={};function Oe(e){r.onAbort&&r.onAbort(e),e+="",oe(e),Ie=!0,ce=1,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.";var t=new WebAssembly.RuntimeError(e);throw l(t),t}function Dt(e,t){return String.prototype.startsWith?e.startsWith(t):e.indexOf(t)===0}var pn="data:application/octet-stream;base64,";function It(e){return Dt(e,pn)}var En="file://";function bt(e){return Dt(e,En)}var X="basis_transcoder.wasm";It(X)||(X=W(X));function wt(e){try{if(e==X&&se)return new Uint8Array(se);if(j)return j(e);throw"both async and sync fetching of the wasm failed"}catch(t){Oe(t)}}function vn(){if(!se&&(N||d)){if(typeof fetch=="function"&&!bt(X))return fetch(X,{credentials:"same-origin"}).then(function(e){if(!e.ok)throw"failed to load wasm binary file at '"+X+"'";return e.arrayBuffer()}).catch(function(){return wt(X)});if(ae)return new Promise(function(e,t){ae(X,function(n){e(new Uint8Array(n))},t)})}return Promise.resolve().then(function(){return wt(X)})}function Un(){var e={a:J_};function t(c,i){var R=c.exports;r.asm=R,de=r.asm.K,yt(de.buffer),Ve=r.asm.O,Gn(r.asm.L),Pn("wasm-instantiate")}Vn("wasm-instantiate");function n(c){t(c.instance)}function _(c){return vn().then(function(i){var R=WebAssembly.instantiate(i,e);return R}).then(c,function(i){oe("failed to asynchronously prepare wasm: "+i),Oe(i)})}function s(){return!se&&typeof WebAssembly.instantiateStreaming=="function"&&!It(X)&&!bt(X)&&typeof fetch=="function"?fetch(X,{credentials:"same-origin"}).then(function(c){var i=WebAssembly.instantiateStreaming(c,e);return i.then(n,function(R){return oe("wasm streaming compile failed: "+R),oe("falling back to ArrayBuffer instantiation"),_(n)})}):_(n)}if(r.instantiateWasm)try{var a=r.instantiateWasm(e,t);return a}catch(c){return oe("Module.instantiateWasm callback failed with error: "+c),!1}return s().catch(l),{}}function We(e){for(;e.length>0;){var t=e.shift();if(typeof t=="function"){t(r);continue}var n=t.func;typeof n=="number"?t.arg===void 0?Ve.get(n)():Ve.get(n)(t.arg):n(t.arg===void 0?null:t.arg)}}var je={};function Ye(e){for(;e.length;){var t=e.pop(),n=e.pop();n(t)}}function pe(e){return this.fromWireType(z[e>>2])}var le={},Te={},ze={},hn=48,yn=57;function qe(e){if(e===void 0)return"_unknown";e=e.replace(/[^a-zA-Z0-9_]/g,"$");var t=e.charCodeAt(0);return t>=hn&&t<=yn?"_"+e:e}function Ze(e,t){return e=qe(e),new Function("body","return function "+e+`() {
    "use strict";    return body.apply(this, arguments);
};
`)(t)}function Ot(e,t){var n=Ze(t,function(_){this.name=t,this.message=_;var s=new Error(_).stack;s!==void 0&&(this.stack=this.toString()+`
`+s.replace(/^Error(:[^\n]*)?\n/,""))});return n.prototype=Object.create(e.prototype),n.prototype.constructor=n,n.prototype.toString=function(){return this.message===void 0?this.name:this.name+": "+this.message},n}var Ht=void 0;function Qe(e){throw new Ht(e)}function ie(e,t,n){e.forEach(function(i){ze[i]=t});function _(i){var R=n(i);R.length!==e.length&&Qe("Mismatched type converter count");for(var T=0;T<e.length;++T)q(e[T],R[T])}var s=new Array(t.length),a=[],c=0;t.forEach(function(i,R){Te.hasOwnProperty(i)?s[R]=Te[i]:(a.push(i),le.hasOwnProperty(i)||(le[i]=[]),le[i].push(function(){s[R]=Te[i],++c,c===a.length&&_(s)}))}),a.length===0&&_(s)}function gn(e){var t=je[e];delete je[e];var n=t.rawConstructor,_=t.rawDestructor,s=t.fields,a=s.map(function(c){return c.getterReturnType}).concat(s.map(function(c){return c.setterArgumentType}));ie([e],a,function(c){var i={};return s.forEach(function(R,T){var O=R.fieldName,K=c[T],G=R.getter,V=R.getterContext,y=c[T+s.length],b=R.setter,$=R.setterContext;i[O]={read:function(Z){return K.fromWireType(G(V,Z))},write:function(Z,ge){var _e=[];b($,Z,y.toWireType(_e,ge)),Ye(_e)}}}),[{name:t.name,fromWireType:function(R){var T={};for(var O in i)T[O]=i[O].read(R);return _(R),T},toWireType:function(R,T){for(var O in i)if(!(O in T))throw new TypeError('Missing field:  "'+O+'"');var K=n();for(O in i)i[O].write(K,T[O]);return R!==null&&R.push(_,K),K},argPackAdvance:8,readValueFromPointer:pe,destructorFunction:_}]})}function Je(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}function Ln(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);Xt=e}var Xt=void 0;function h(e){for(var t="",n=e;p[n];)t+=Xt[p[n++]];return t}var Me=void 0;function S(e){throw new Me(e)}function q(e,t,n){if(n=n||{},!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var _=t.name;if(e||S('type "'+_+'" must have a positive integer typeid pointer'),Te.hasOwnProperty(e)){if(n.ignoreDuplicateRegistrations)return;S("Cannot register type '"+_+"' twice")}if(Te[e]=t,delete ze[e],le.hasOwnProperty(e)){var s=le[e];delete le[e],s.forEach(function(a){a()})}}function mn(e,t,n,_,s){var a=Je(n);t=h(t),q(e,{name:t,fromWireType:function(c){return!!c},toWireType:function(c,i){return i?_:s},argPackAdvance:8,readValueFromPointer:function(c){var i;if(n===1)i=U;else if(n===2)i=k;else if(n===4)i=L;else throw new TypeError("Unknown boolean type size: "+t);return this.fromWireType(i[c>>a])},destructorFunction:null})}function Dn(e){if(!(this instanceof Re)||!(e instanceof Re))return!1;for(var t=this.$$.ptrType.registeredClass,n=this.$$.ptr,_=e.$$.ptrType.registeredClass,s=e.$$.ptr;t.baseClass;)n=t.upcast(n),t=t.baseClass;for(;_.baseClass;)s=_.upcast(s),_=_.baseClass;return t===_&&n===s}function In(e){return{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType}}function lt(e){function t(n){return n.$$.ptrType.registeredClass.name}S(t(e)+" instance already deleted")}var Mt=!1;function xt(e){}function bn(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}function kt(e){e.count.value-=1;var t=e.count.value===0;t&&bn(e)}function Ee(e){return typeof FinalizationGroup>"u"?(Ee=function(t){return t},e):(Mt=new FinalizationGroup(function(t){for(var n=t.next();!n.done;n=t.next()){var _=n.value;_.ptr?kt(_):console.warn("object already deleted: "+_.ptr)}}),Ee=function(t){return Mt.register(t,t.$$,t.$$),t},xt=function(t){Mt.unregister(t.$$)},Ee(e))}function wn(){if(this.$$.ptr||lt(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e=Ee(Object.create(Object.getPrototypeOf(this),{$$:{value:In(this.$$)}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e}function Hn(){this.$$.ptr||lt(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&S("Object already scheduled for deletion"),xt(this),kt(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Xn(){return!this.$$.ptr}var ve=void 0,Ue=[];function Kt(){for(;Ue.length;){var e=Ue.pop();e.$$.deleteScheduled=!1,e.delete()}}function xn(){return this.$$.ptr||lt(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&S("Object already scheduled for deletion"),Ue.push(this),Ue.length===1&&ve&&ve(Kt),this.$$.deleteScheduled=!0,this}function kn(){Re.prototype.isAliasOf=Dn,Re.prototype.clone=wn,Re.prototype.delete=Hn,Re.prototype.isDeleted=Xn,Re.prototype.deleteLater=xn}function Re(){}var $t={};function Wt(e,t,n){if(e[t].overloadTable===void 0){var _=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||S("Function '"+n+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[_.argCount]=_}}function Bt(e,t,n){r.hasOwnProperty(e)?((n===void 0||r[e].overloadTable!==void 0&&r[e].overloadTable[n]!==void 0)&&S("Cannot register public name '"+e+"' twice"),Wt(r,e,e),r.hasOwnProperty(n)&&S("Cannot register multiple overloads of a function with the same number of arguments ("+n+")!"),r[e].overloadTable[n]=t):(r[e]=t,n!==void 0&&(r[e].numArguments=n))}function $n(e,t,n,_,s,a,c,i){this.name=e,this.constructor=t,this.instancePrototype=n,this.rawDestructor=_,this.baseClass=s,this.getActualType=a,this.upcast=c,this.downcast=i,this.pureVirtualFunctions=[]}function Ct(e,t,n){for(;t!==n;)t.upcast||S("Expected null or instance of "+n.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function Wn(e,t){if(t===null)return this.isReference&&S("null is not a valid "+this.name),0;t.$$||S('Cannot pass "'+Ke(t)+'" as a '+this.name),t.$$.ptr||S("Cannot pass deleted object as a pointer of type "+this.name);var n=t.$$.ptrType.registeredClass,_=Ct(t.$$.ptr,n,this.registeredClass);return _}function jn(e,t){var n;if(t===null)return this.isReference&&S("null is not a valid "+this.name),this.isSmartPointer?(n=this.rawConstructor(),e!==null&&e.push(this.rawDestructor,n),n):0;t.$$||S('Cannot pass "'+Ke(t)+'" as a '+this.name),t.$$.ptr||S("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&S("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var _=t.$$.ptrType.registeredClass;if(n=Ct(t.$$.ptr,_,this.registeredClass),this.isSmartPointer)switch(t.$$.smartPtr===void 0&&S("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?n=t.$$.smartPtr:S("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:n=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)n=t.$$.smartPtr;else{var s=t.clone();n=this.rawShare(n,te(function(){s.delete()})),e!==null&&e.push(this.rawDestructor,n)}break;default:S("Unsupporting sharing policy")}return n}function Yn(e,t){if(t===null)return this.isReference&&S("null is not a valid "+this.name),0;t.$$||S('Cannot pass "'+Ke(t)+'" as a '+this.name),t.$$.ptr||S("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&S("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var n=t.$$.ptrType.registeredClass,_=Ct(t.$$.ptr,n,this.registeredClass);return _}function zn(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function qn(e){this.rawDestructor&&this.rawDestructor(e)}function Zn(e){e!==null&&e.delete()}function jt(e,t,n){if(t===n)return e;if(n.baseClass===void 0)return null;var _=jt(e,t,n.baseClass);return _===null?null:n.downcast(_)}function Qn(){return Object.keys(he).length}function Jn(){var e=[];for(var t in he)he.hasOwnProperty(t)&&e.push(he[t]);return e}function e_(e){ve=e,Ue.length&&ve&&ve(Kt)}function t_(){r.getInheritedInstanceCount=Qn,r.getLiveInheritedInstances=Jn,r.flushPendingDeletes=Kt,r.setDelayFunction=e_}var he={};function n_(e,t){for(t===void 0&&S("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}function __(e,t){return t=n_(e,t),he[t]}function et(e,t){(!t.ptrType||!t.ptr)&&Qe("makeClassHandle requires ptr and ptrType");var n=!!t.smartPtrType,_=!!t.smartPtr;return n!==_&&Qe("Both smartPtrType and smartPtr must be specified"),t.count={value:1},Ee(Object.create(e,{$$:{value:t}}))}function r_(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var n=__(this.registeredClass,t);if(n!==void 0){if(n.$$.count.value===0)return n.$$.ptr=t,n.$$.smartPtr=e,n.clone();var _=n.clone();return this.destructor(e),_}function s(){return this.isSmartPointer?et(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):et(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var a=this.registeredClass.getActualType(t),c=$t[a];if(!c)return s.call(this);var i;this.isConst?i=c.constPointerType:i=c.pointerType;var R=jt(t,this.registeredClass,i.registeredClass);return R===null?s.call(this):this.isSmartPointer?et(i.registeredClass.instancePrototype,{ptrType:i,ptr:R,smartPtrType:this,smartPtr:e}):et(i.registeredClass.instancePrototype,{ptrType:i,ptr:R})}function o_(){ee.prototype.getPointee=zn,ee.prototype.destructor=qn,ee.prototype.argPackAdvance=8,ee.prototype.readValueFromPointer=pe,ee.prototype.deleteObject=Zn,ee.prototype.fromWireType=r_}function ee(e,t,n,_,s,a,c,i,R,T,O){this.name=e,this.registeredClass=t,this.isReference=n,this.isConst=_,this.isSmartPointer=s,this.pointeeType=a,this.sharingPolicy=c,this.rawGetPointee=i,this.rawConstructor=R,this.rawShare=T,this.rawDestructor=O,!s&&t.baseClass===void 0?_?(this.toWireType=Wn,this.destructorFunction=null):(this.toWireType=Yn,this.destructorFunction=null):this.toWireType=jn}function Yt(e,t,n){r.hasOwnProperty(e)||Qe("Replacing nonexistant public symbol"),r[e].overloadTable!==void 0&&n!==void 0?r[e].overloadTable[n]=t:(r[e]=t,r[e].argCount=n)}function s_(e,t,n){var _=r["dynCall_"+e];return n&&n.length?_.apply(null,[t].concat(n)):_.call(null,t)}function i_(e,t,n){return e.indexOf("j")!=-1?s_(e,t,n):Ve.get(t).apply(null,n)}function R_(e,t){var n=[];return function(){n.length=arguments.length;for(var _=0;_<arguments.length;_++)n[_]=arguments[_];return i_(e,t,n)}}function Y(e,t){e=h(e);function n(){return e.indexOf("j")!=-1?R_(e,t):Ve.get(t)}var _=n();return typeof _!="function"&&S("unknown function pointer with signature "+e+": "+t),_}var zt=void 0;function qt(e){var t=tn(e),n=h(t);return ne(t),n}function tt(e,t){var n=[],_={};function s(a){if(!_[a]&&!Te[a]){if(ze[a]){ze[a].forEach(s);return}n.push(a),_[a]=!0}}throw t.forEach(s),new zt(e+": "+n.map(qt).join([", "]))}function a_(e,t,n,_,s,a,c,i,R,T,O,K,G){O=h(O),a=Y(s,a),i&&(i=Y(c,i)),T&&(T=Y(R,T)),G=Y(K,G);var V=qe(O);Bt(V,function(){tt("Cannot construct "+O+" due to unbound types",[_])}),ie([e,t,n],_?[_]:[],function(y){y=y[0];var b,$;_?(b=y.registeredClass,$=b.instancePrototype):$=Re.prototype;var Z=Ze(V,function(){if(Object.getPrototypeOf(this)!==ge)throw new Me("Use 'new' to construct "+O);if(_e.constructor_body===void 0)throw new Me(O+" has no accessible constructor");var rn=_e.constructor_body[arguments.length];if(rn===void 0)throw new Me("Tried to invoke ctor of "+O+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(_e.constructor_body).toString()+") parameters instead!");return rn.apply(this,arguments)}),ge=Object.create($,{constructor:{value:Z}});Z.prototype=ge;var _e=new $n(O,Z,ge,G,b,a,i,T),rr=new ee(O,_e,!0,!1,!1),nn=new ee(O+"*",_e,!1,!1,!1),_n=new ee(O+" const*",_e,!1,!0,!1);return $t[e]={pointerType:nn,constPointerType:_n},Yt(V,Z),[rr,nn,_n]})}function Ft(e,t){for(var n=[],_=0;_<e;_++)n.push(L[(t>>2)+_]);return n}function c_(e,t,n,_,s,a){D(t>0);var c=Ft(t,n);s=Y(_,s);var i=[a],R=[];ie([],[e],function(T){T=T[0];var O="constructor "+T.name;if(T.registeredClass.constructor_body===void 0&&(T.registeredClass.constructor_body=[]),T.registeredClass.constructor_body[t-1]!==void 0)throw new Me("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+T.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return T.registeredClass.constructor_body[t-1]=function(){tt("Cannot construct "+T.name+" due to unbound types",c)},ie([],c,function(K){return T.registeredClass.constructor_body[t-1]=function(){arguments.length!==t-1&&S(O+" called with "+arguments.length+" arguments, expected "+(t-1)),R.length=0,i.length=t;for(var V=1;V<t;++V)i[V]=K[V].toWireType(R,arguments[V-1]);var y=s.apply(null,i);return Ye(R),K[0].fromWireType(y)},[]}),[]})}function Zt(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var n=Ze(e.name||"unknownFunctionName",function(){});n.prototype=e.prototype;var _=new n,s=e.apply(_,t);return s instanceof Object?s:_}function Qt(e,t,n,_,s){var a=t.length;a<2&&S("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var c=t[1]!==null&&n!==null,i=!1,R=1;R<t.length;++R)if(t[R]!==null&&t[R].destructorFunction===void 0){i=!0;break}for(var T=t[0].name!=="void",O="",K="",R=0;R<a-2;++R)O+=(R!==0?", ":"")+"arg"+R,K+=(R!==0?", ":"")+"arg"+R+"Wired";var G="return function "+qe(e)+"("+O+`) {
if (arguments.length !== `+(a-2)+`) {
throwBindingError('function `+e+" called with ' + arguments.length + ' arguments, expected "+(a-2)+` args!');
}
`;i&&(G+=`var destructors = [];
`);var V=i?"destructors":"null",y=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],b=[S,_,s,Ye,t[0],t[1]];c&&(G+="var thisWired = classParam.toWireType("+V+`, this);
`);for(var R=0;R<a-2;++R)G+="var arg"+R+"Wired = argType"+R+".toWireType("+V+", arg"+R+"); // "+t[R+2].name+`
`,y.push("argType"+R),b.push(t[R+2]);if(c&&(K="thisWired"+(K.length>0?", ":"")+K),G+=(T?"var rv = ":"")+"invoker(fn"+(K.length>0?", ":"")+K+`);
`,i)G+=`runDestructors(destructors);
`;else for(var R=c?1:2;R<t.length;++R){var $=R===1?"thisWired":"arg"+(R-2)+"Wired";t[R].destructorFunction!==null&&(G+=$+"_dtor("+$+"); // "+t[R].name+`
`,y.push($+"_dtor"),b.push(t[R].destructorFunction))}T&&(G+=`var ret = retType.fromWireType(rv);
return ret;
`),G+=`}
`,y.push(G);var Z=Zt(Function,y).apply(null,b);return Z}function A_(e,t,n,_,s,a,c,i){var R=Ft(n,_);t=h(t),a=Y(s,a),ie([],[e],function(T){T=T[0];var O=T.name+"."+t;i&&T.registeredClass.pureVirtualFunctions.push(t);function K(){tt("Cannot call "+O+" due to unbound types",R)}var G=T.registeredClass.instancePrototype,V=G[t];return V===void 0||V.overloadTable===void 0&&V.className!==T.name&&V.argCount===n-2?(K.argCount=n-2,K.className=T.name,G[t]=K):(Wt(G,t,O),G[t].overloadTable[n-2]=K),ie([],R,function(y){var b=Qt(O,y,T,a,c);return G[t].overloadTable===void 0?(b.argCount=n-2,G[t]=b):G[t].overloadTable[n-2]=b,[]}),[]})}function f_(e,t,n){e=h(e),ie([],[t],function(_){return _=_[0],r[e]=_.fromWireType(n),[]})}var dt=[],x=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function St(e){e>4&&--x[e].refcount===0&&(x[e]=void 0,dt.push(e))}function T_(){for(var e=0,t=5;t<x.length;++t)x[t]!==void 0&&++e;return e}function u_(){for(var e=5;e<x.length;++e)if(x[e]!==void 0)return x[e];return null}function O_(){r.count_emval_handles=T_,r.get_first_emval=u_}function te(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:{var t=dt.length?dt.pop():x.length;return x[t]={refcount:1,value:e},t}}}function l_(e,t){t=h(t),q(e,{name:t,fromWireType:function(n){var _=x[n].value;return St(n),_},toWireType:function(n,_){return te(_)},argPackAdvance:8,readValueFromPointer:pe,destructorFunction:null})}function M_(e,t,n){switch(t){case 0:return function(_){var s=n?U:p;return this.fromWireType(s[_])};case 1:return function(_){var s=n?k:Ae;return this.fromWireType(s[_>>1])};case 2:return function(_){var s=n?L:z;return this.fromWireType(s[_>>2])};default:throw new TypeError("Unknown integer type: "+e)}}function K_(e,t,n,_){var s=Je(n);t=h(t);function a(){}a.values={},q(e,{name:t,constructor:a,fromWireType:function(c){return this.constructor.values[c]},toWireType:function(c,i){return i.value},argPackAdvance:8,readValueFromPointer:M_(t,s,_),destructorFunction:null}),Bt(t,a)}function nt(e,t){var n=Te[e];return n===void 0&&S(t+" has unknown type "+qt(e)),n}function B_(e,t,n){var _=nt(e,"enum");t=h(t);var s=_.constructor,a=Object.create(_.constructor.prototype,{value:{value:n},constructor:{value:Ze(_.name+"_"+t,function(){})}});s.values[n]=a,s[t]=a}function Ke(e){if(e===null)return"null";var t=typeof e;return t==="object"||t==="array"||t==="function"?e.toString():""+e}function C_(e,t){switch(t){case 2:return function(n){return this.fromWireType(Ut[n>>2])};case 3:return function(n){return this.fromWireType(ht[n>>3])};default:throw new TypeError("Unknown float type: "+e)}}function F_(e,t,n){var _=Je(n);t=h(t),q(e,{name:t,fromWireType:function(s){return s},toWireType:function(s,a){if(typeof a!="number"&&typeof a!="boolean")throw new TypeError('Cannot convert "'+Ke(a)+'" to '+this.name);return a},argPackAdvance:8,readValueFromPointer:C_(t,_),destructorFunction:null})}function d_(e,t,n,_,s,a){var c=Ft(t,n);e=h(e),s=Y(_,s),Bt(e,function(){tt("Cannot call "+e+" due to unbound types",c)},t-1),ie([],c,function(i){var R=[i[0],null].concat(i.slice(1));return Yt(e,Qt(e,R,null,s,a),t-1),[]})}function S_(e,t,n){switch(t){case 0:return n?function(s){return U[s]}:function(s){return p[s]};case 1:return n?function(s){return k[s>>1]}:function(s){return Ae[s>>1]};case 2:return n?function(s){return L[s>>2]}:function(s){return z[s>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function G_(e,t,n,_,s){t=h(t),s===-1&&(s=4294967295);var a=Je(n),c=function(T){return T};if(_===0){var i=32-8*n;c=function(T){return T<<i>>>i}}var R=t.indexOf("unsigned")!=-1;q(e,{name:t,fromWireType:c,toWireType:function(T,O){if(typeof O!="number"&&typeof O!="boolean")throw new TypeError('Cannot convert "'+Ke(O)+'" to '+this.name);if(O<_||O>s)throw new TypeError('Passing a number "'+Ke(O)+'" from JS side to C/C++ side to an argument of type "'+t+'", which is outside the valid range ['+_+", "+s+"]!");return R?O>>>0:O|0},argPackAdvance:8,readValueFromPointer:S_(t,a,_!==0),destructorFunction:null})}function N_(e,t,n){var _=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],s=_[t];function a(c){c=c>>2;var i=z,R=i[c],T=i[c+1];return new s(Ne,T,R)}n=h(n),q(e,{name:n,fromWireType:a,argPackAdvance:8,readValueFromPointer:a},{ignoreDuplicateRegistrations:!0})}function V_(e,t){t=h(t);var n=t==="std::string";q(e,{name:t,fromWireType:function(_){var s=z[_>>2],a;if(n)for(var c=_+4,i=0;i<=s;++i){var R=_+4+i;if(i==s||p[R]==0){var T=R-c,O=Se(c,T);a===void 0?a=O:(a+="\0",a+=O),c=R+1}}else{for(var K=new Array(s),i=0;i<s;++i)K[i]=String.fromCharCode(p[_+4+i]);a=K.join("")}return ne(_),a},toWireType:function(_,s){s instanceof ArrayBuffer&&(s=new Uint8Array(s));var a,c=typeof s=="string";c||s instanceof Uint8Array||s instanceof Uint8ClampedArray||s instanceof Int8Array||S("Cannot pass non-string to std::string"),n&&c?a=function(){return at(s)}:a=function(){return s.length};var i=a(),R=Nt(4+i+1);if(z[R>>2]=i,n&&c)Xe(s,R+4,i+1);else if(c)for(var T=0;T<i;++T){var O=s.charCodeAt(T);O>255&&(ne(R),S("String has UTF-16 code units that do not fit in 8 bits")),p[R+4+T]=O}else for(var T=0;T<i;++T)p[R+4+T]=s[T];return _!==null&&_.push(ne,R),R},argPackAdvance:8,readValueFromPointer:pe,destructorFunction:function(_){ne(_)}})}function P_(e,t,n){n=h(n);var _,s,a,c,i;t===2?(_=xe,s=ke,c=$e,a=function(){return Ae},i=1):t===4&&(_=ct,s=At,c=ft,a=function(){return z},i=2),q(e,{name:n,fromWireType:function(R){for(var T=z[R>>2],O=a(),K,G=R+4,V=0;V<=T;++V){var y=R+4+V*t;if(V==T||O[y>>i]==0){var b=y-G,$=_(G,b);K===void 0?K=$:(K+="\0",K+=$),G=y+t}}return ne(R),K},toWireType:function(R,T){typeof T!="string"&&S("Cannot pass non-string to C++ string type "+n);var O=c(T),K=Nt(4+O+t);return z[K>>2]=O>>i,s(T,K+4,O+t),R!==null&&R.push(ne,K),K},argPackAdvance:8,readValueFromPointer:pe,destructorFunction:function(R){ne(R)}})}function p_(e,t,n,_,s,a){je[e]={name:h(t),rawConstructor:Y(n,_),rawDestructor:Y(s,a),fields:[]}}function E_(e,t,n,_,s,a,c,i,R,T){je[e].fields.push({fieldName:h(t),getterReturnType:n,getter:Y(_,s),getterContext:a,setterArgumentType:c,setter:Y(i,R),setterContext:T})}function v_(e,t){t=h(t),q(e,{isVoid:!0,name:t,argPackAdvance:0,fromWireType:function(){},toWireType:function(n,_){}})}function ye(e){return e||S("Cannot use deleted val. handle = "+e),x[e].value}function U_(e,t,n){e=ye(e),t=nt(t,"emval::as");var _=[],s=te(_);return L[n>>2]=s,t.toWireType(_,e)}var h_={};function _t(e){var t=h_[e];return t===void 0?h(e):t}var Gt=[];function y_(e,t,n,_){e=Gt[e],t=ye(t),n=_t(n),e(t,n,null,_)}function Jt(){return typeof globalThis=="object"?globalThis:function(){return Function}()("return this")()}function g_(e){return e===0?te(Jt()):(e=_t(e),te(Jt()[e]))}function L_(e){var t=Gt.length;return Gt.push(e),t}function m_(e,t){for(var n=new Array(e),_=0;_<e;++_)n[_]=nt(L[(t>>2)+_],"parameter "+_);return n}function D_(e,t){for(var n=m_(e,t),_=n[0],s=_.name+"_$"+n.slice(1).map(function(V){return V.name}).join("_")+"$",a=["retType"],c=[_],i="",R=0;R<e-1;++R)i+=(R!==0?", ":"")+"arg"+R,a.push("argType"+R),c.push(n[1+R]);for(var T=qe("methodCaller_"+s),O="return function "+T+`(handle, name, destructors, args) {
`,K=0,R=0;R<e-1;++R)O+="    var arg"+R+" = argType"+R+".readValueFromPointer(args"+(K?"+"+K:"")+`);
`,K+=n[R+1].argPackAdvance;O+="    var rv = handle[name]("+i+`);
`;for(var R=0;R<e-1;++R)n[R+1].deleteObject&&(O+="    argType"+R+".deleteObject(arg"+R+`);
`);_.isVoid||(O+=`    return retType.toWireType(destructors, rv);
`),O+=`};
`,a.push(O);var G=Zt(Function,a).apply(null,c);return L_(G)}function I_(e){return e=_t(e),te(r[e])}function b_(e,t){return e=ye(e),t=ye(t),te(e[t])}function w_(e){e>4&&(x[e].refcount+=1)}function H_(e){for(var t="",n=0;n<e;++n)t+=(n!==0?", ":"")+"arg"+n;for(var _="return function emval_allocator_"+e+`(constructor, argTypes, args) {
`,n=0;n<e;++n)_+="var argType"+n+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+n+'], "parameter '+n+`");
var arg`+n+" = argType"+n+`.readValueFromPointer(args);
args += argType`+n+`['argPackAdvance'];
`;return _+="var obj = new constructor("+t+`);
return __emval_register(obj);
}
`,new Function("requireRegisteredType","Module","__emval_register",_)(nt,r,te)}var en={};function X_(e,t,n,_){e=ye(e);var s=en[t];return s||(s=H_(t),en[t]=s),s(e,n,_)}function x_(e){return te(_t(e))}function k_(e){var t=x[e].value;Ye(t),St(e)}function $_(){Oe()}function W_(e,t,n){p.copyWithin(e,t,t+n)}function j_(e){try{return de.grow(e-Ne.byteLength+65535>>>16),yt(de.buffer),1}catch{}}function Y_(e){var t=p.length;e=e>>>0;var n=2147483648;if(e>n)return!1;for(var _=1;_<=4;_*=2){var s=t*(1+.2/_);s=Math.min(s,e+100663296);var a=Math.min(n,Tt(Math.max(e,s),65536)),c=j_(a);if(c)return!0}return!1}var rt={mappings:{},buffers:[null,[],[]],printChar:function(e,t){var n=rt.buffers[e];t===0||t===10?((e===1?it:oe)(we(n,0)),n.length=0):n.push(t)},varargs:void 0,get:function(){rt.varargs+=4;var e=L[rt.varargs-4>>2];return e},getStr:function(e){var t=Se(e);return t},get64:function(e,t){return e}};function z_(e){return 0}function q_(e,t,n,_,s){}function Z_(e,t,n,_){for(var s=0,a=0;a<n;a++){for(var c=L[t+a*8>>2],i=L[t+(a*8+4)>>2],R=0;R<i;R++)rt.printChar(e,p[c+R]);s+=i}return L[_>>2]=s,0}function Q_(e){vt(e|0)}Ht=r.InternalError=Ot(Error,"InternalError"),Ln(),Me=r.BindingError=Ot(Error,"BindingError"),kn(),o_(),t_(),zt=r.UnboundTypeError=Ot(Error,"UnboundTypeError"),O_();var J_={t:gn,I:mn,x:a_,w:c_,d:A_,k:f_,H:l_,n:K_,a:B_,A:F_,i:d_,j:G_,h:N_,B:V_,v:P_,u:p_,c:E_,J:v_,m:U_,s:y_,b:St,y:g_,p:D_,r:I_,e:b_,g:w_,q:X_,f:x_,l:k_,o:$_,E:W_,F:Y_,G:z_,C:q_,z:Z_,D:Q_},Or=Un(),er=r.___wasm_call_ctors=function(){return(er=r.___wasm_call_ctors=r.asm.L).apply(null,arguments)},Nt=r._malloc=function(){return(Nt=r._malloc=r.asm.M).apply(null,arguments)},ne=r._free=function(){return(ne=r._free=r.asm.N).apply(null,arguments)},tn=r.___getTypeName=function(){return(tn=r.___getTypeName=r.asm.P).apply(null,arguments)},tr=r.___embind_register_native_and_builtin_types=function(){return(tr=r.___embind_register_native_and_builtin_types=r.asm.Q).apply(null,arguments)},nr=r.dynCall_jiji=function(){return(nr=r.dynCall_jiji=r.asm.R).apply(null,arguments)},ot;function _r(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}Pe=function e(){ot||Vt(),ot||(Pe=e)};function Vt(e){if(e=e||g,fe>0||(Bn(),fe>0))return;function t(){ot||(ot=!0,r.calledRun=!0,!Ie&&(Cn(),Fn(),C(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),dn()))}r.setStatus?(r.setStatus("Running..."),setTimeout(function(){setTimeout(function(){r.setStatus("")},1),t()},1)):t()}if(r.run=Vt,r.preInit)for(typeof r.preInit=="function"&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return Vt(),f.ready}}();typeof st=="object"&&typeof Et=="object"?Et.exports=pt:typeof define=="function"&&define.amd?define([],function(){return pt}):typeof st=="object"&&(st.BASIS=pt)});var B={UNSIGNED_BYTE:M.UNSIGNED_BYTE,UNSIGNED_SHORT:M.UNSIGNED_SHORT,UNSIGNED_INT:M.UNSIGNED_INT,FLOAT:M.FLOAT,HALF_FLOAT:M.HALF_FLOAT_OES,UNSIGNED_INT_24_8:M.UNSIGNED_INT_24_8,UNSIGNED_SHORT_4_4_4_4:M.UNSIGNED_SHORT_4_4_4_4,UNSIGNED_SHORT_5_5_5_1:M.UNSIGNED_SHORT_5_5_5_1,UNSIGNED_SHORT_5_6_5:M.UNSIGNED_SHORT_5_6_5};B.toWebGLConstant=function(o,f){switch(o){case B.UNSIGNED_BYTE:return M.UNSIGNED_BYTE;case B.UNSIGNED_SHORT:return M.UNSIGNED_SHORT;case B.UNSIGNED_INT:return M.UNSIGNED_INT;case B.FLOAT:return M.FLOAT;case B.HALF_FLOAT:return f.webgl2?M.HALF_FLOAT:M.HALF_FLOAT_OES;case B.UNSIGNED_INT_24_8:return M.UNSIGNED_INT_24_8;case B.UNSIGNED_SHORT_4_4_4_4:return M.UNSIGNED_SHORT_4_4_4_4;case B.UNSIGNED_SHORT_5_5_5_1:return M.UNSIGNED_SHORT_5_5_5_1;case B.UNSIGNED_SHORT_5_6_5:return B.UNSIGNED_SHORT_5_6_5}};B.isPacked=function(o){return o===B.UNSIGNED_INT_24_8||o===B.UNSIGNED_SHORT_4_4_4_4||o===B.UNSIGNED_SHORT_5_5_5_1||o===B.UNSIGNED_SHORT_5_6_5};B.sizeInBytes=function(o){switch(o){case B.UNSIGNED_BYTE:return 1;case B.UNSIGNED_SHORT:case B.UNSIGNED_SHORT_4_4_4_4:case B.UNSIGNED_SHORT_5_5_5_1:case B.UNSIGNED_SHORT_5_6_5:case B.HALF_FLOAT:return 2;case B.UNSIGNED_INT:case B.FLOAT:case B.UNSIGNED_INT_24_8:return 4}};B.validate=function(o){return o===B.UNSIGNED_BYTE||o===B.UNSIGNED_SHORT||o===B.UNSIGNED_INT||o===B.FLOAT||o===B.HALF_FLOAT||o===B.UNSIGNED_INT_24_8||o===B.UNSIGNED_SHORT_4_4_4_4||o===B.UNSIGNED_SHORT_5_5_5_1||o===B.UNSIGNED_SHORT_5_6_5};B.getTypedArrayConstructor=function(o){let f=B.sizeInBytes(o);return f===Uint8Array.BYTES_PER_ELEMENT?Uint8Array:f===Uint16Array.BYTES_PER_ELEMENT?Uint16Array:f===Float32Array.BYTES_PER_ELEMENT&&o===B.FLOAT?Float32Array:Uint32Array};var H=Object.freeze(B);var A={DEPTH_COMPONENT:M.DEPTH_COMPONENT,DEPTH_STENCIL:M.DEPTH_STENCIL,ALPHA:M.ALPHA,RED:M.RED,RG:M.RG,RGB:M.RGB,RGBA:M.RGBA,LUMINANCE:M.LUMINANCE,LUMINANCE_ALPHA:M.LUMINANCE_ALPHA,RGB_DXT1:M.COMPRESSED_RGB_S3TC_DXT1_EXT,RGBA_DXT1:M.COMPRESSED_RGBA_S3TC_DXT1_EXT,RGBA_DXT3:M.COMPRESSED_RGBA_S3TC_DXT3_EXT,RGBA_DXT5:M.COMPRESSED_RGBA_S3TC_DXT5_EXT,RGB_PVRTC_4BPPV1:M.COMPRESSED_RGB_PVRTC_4BPPV1_IMG,RGB_PVRTC_2BPPV1:M.COMPRESSED_RGB_PVRTC_2BPPV1_IMG,RGBA_PVRTC_4BPPV1:M.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG,RGBA_PVRTC_2BPPV1:M.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG,RGBA_ASTC:M.COMPRESSED_RGBA_ASTC_4x4_WEBGL,RGB_ETC1:M.COMPRESSED_RGB_ETC1_WEBGL,RGB8_ETC2:M.COMPRESSED_RGB8_ETC2,RGBA8_ETC2_EAC:M.COMPRESSED_RGBA8_ETC2_EAC,RGBA_BC7:M.COMPRESSED_RGBA_BPTC_UNORM};A.componentsLength=function(o){switch(o){case A.RGB:return 3;case A.RGBA:return 4;case A.LUMINANCE_ALPHA:case A.RG:return 2;case A.ALPHA:case A.RED:case A.LUMINANCE:return 1;default:return 1}};A.validate=function(o){return o===A.DEPTH_COMPONENT||o===A.DEPTH_STENCIL||o===A.ALPHA||o===A.RED||o===A.RG||o===A.RGB||o===A.RGBA||o===A.LUMINANCE||o===A.LUMINANCE_ALPHA||o===A.RGB_DXT1||o===A.RGBA_DXT1||o===A.RGBA_DXT3||o===A.RGBA_DXT5||o===A.RGB_PVRTC_4BPPV1||o===A.RGB_PVRTC_2BPPV1||o===A.RGBA_PVRTC_4BPPV1||o===A.RGBA_PVRTC_2BPPV1||o===A.RGBA_ASTC||o===A.RGB_ETC1||o===A.RGB8_ETC2||o===A.RGBA8_ETC2_EAC||o===A.RGBA_BC7};A.isColorFormat=function(o){return o===A.RED||o===A.ALPHA||o===A.RGB||o===A.RGBA||o===A.LUMINANCE||o===A.LUMINANCE_ALPHA};A.isDepthFormat=function(o){return o===A.DEPTH_COMPONENT||o===A.DEPTH_STENCIL};A.isCompressedFormat=function(o){return o===A.RGB_DXT1||o===A.RGBA_DXT1||o===A.RGBA_DXT3||o===A.RGBA_DXT5||o===A.RGB_PVRTC_4BPPV1||o===A.RGB_PVRTC_2BPPV1||o===A.RGBA_PVRTC_4BPPV1||o===A.RGBA_PVRTC_2BPPV1||o===A.RGBA_ASTC||o===A.RGB_ETC1||o===A.RGB8_ETC2||o===A.RGBA8_ETC2_EAC||o===A.RGBA_BC7};A.isDXTFormat=function(o){return o===A.RGB_DXT1||o===A.RGBA_DXT1||o===A.RGBA_DXT3||o===A.RGBA_DXT5};A.isPVRTCFormat=function(o){return o===A.RGB_PVRTC_4BPPV1||o===A.RGB_PVRTC_2BPPV1||o===A.RGBA_PVRTC_4BPPV1||o===A.RGBA_PVRTC_2BPPV1};A.isASTCFormat=function(o){return o===A.RGBA_ASTC};A.isETC1Format=function(o){return o===A.RGB_ETC1};A.isETC2Format=function(o){return o===A.RGB8_ETC2||o===A.RGBA8_ETC2_EAC};A.isBC7Format=function(o){return o===A.RGBA_BC7};A.compressedTextureSizeInBytes=function(o,f,r){switch(o){case A.RGB_DXT1:case A.RGBA_DXT1:case A.RGB_ETC1:case A.RGB8_ETC2:return Math.floor((f+3)/4)*Math.floor((r+3)/4)*8;case A.RGBA_DXT3:case A.RGBA_DXT5:case A.RGBA_ASTC:case A.RGBA8_ETC2_EAC:return Math.floor((f+3)/4)*Math.floor((r+3)/4)*16;case A.RGB_PVRTC_4BPPV1:case A.RGBA_PVRTC_4BPPV1:return Math.floor((Math.max(f,8)*Math.max(r,8)*4+7)/8);case A.RGB_PVRTC_2BPPV1:case A.RGBA_PVRTC_2BPPV1:return Math.floor((Math.max(f,16)*Math.max(r,8)*2+7)/8);case A.RGBA_BC7:return Math.ceil(f/4)*Math.ceil(r/4)*16;default:return 0}};A.textureSizeInBytes=function(o,f,r,C){let l=A.componentsLength(o);return H.isPacked(f)&&(l=1),l*H.sizeInBytes(f)*r*C};A.alignmentInBytes=function(o,f,r){let C=A.textureSizeInBytes(o,f,r,1)%4;return C===0?4:C===2?2:1};A.createTypedArray=function(o,f,r,C){let l=H.getTypedArrayConstructor(f),E=A.componentsLength(o)*r*C;return new l(E)};A.flipY=function(o,f,r,C,l){if(l===1)return o;let E=A.createTypedArray(f,r,C,l),F=A.componentsLength(f),g=C*F;for(let m=0;m<l;++m){let I=m*C*F,N=(l-m-1)*C*F;for(let d=0;d<g;++d)E[N+d]=o[I+d]}return E};A.toInternalFormat=function(o,f,r){if(!r.webgl2)return o;if(o===A.DEPTH_STENCIL)return M.DEPTH24_STENCIL8;if(o===A.DEPTH_COMPONENT){if(f===H.UNSIGNED_SHORT)return M.DEPTH_COMPONENT16;if(f===H.UNSIGNED_INT)return M.DEPTH_COMPONENT24}if(f===H.FLOAT)switch(o){case A.RGBA:return M.RGBA32F;case A.RGB:return M.RGB32F;case A.RG:return M.RG32F;case A.RED:return M.R32F}if(f===H.HALF_FLOAT)switch(o){case A.RGBA:return M.RGBA16F;case A.RGB:return M.RGB16F;case A.RG:return M.RG16F;case A.RED:return M.R16F}return o};var v=Object.freeze(A);var ir={VK_FORMAT_UNDEFINED:0,VK_FORMAT_R4G4_UNORM_PACK8:1,VK_FORMAT_R4G4B4A4_UNORM_PACK16:2,VK_FORMAT_B4G4R4A4_UNORM_PACK16:3,VK_FORMAT_R5G6B5_UNORM_PACK16:4,VK_FORMAT_B5G6R5_UNORM_PACK16:5,VK_FORMAT_R5G5B5A1_UNORM_PACK16:6,VK_FORMAT_B5G5R5A1_UNORM_PACK16:7,VK_FORMAT_A1R5G5B5_UNORM_PACK16:8,VK_FORMAT_R8_UNORM:9,VK_FORMAT_R8_SNORM:10,VK_FORMAT_R8_USCALED:11,VK_FORMAT_R8_SSCALED:12,VK_FORMAT_R8_UINT:13,VK_FORMAT_R8_SINT:14,VK_FORMAT_R8_SRGB:15,VK_FORMAT_R8G8_UNORM:16,VK_FORMAT_R8G8_SNORM:17,VK_FORMAT_R8G8_USCALED:18,VK_FORMAT_R8G8_SSCALED:19,VK_FORMAT_R8G8_UINT:20,VK_FORMAT_R8G8_SINT:21,VK_FORMAT_R8G8_SRGB:22,VK_FORMAT_R8G8B8_UNORM:23,VK_FORMAT_R8G8B8_SNORM:24,VK_FORMAT_R8G8B8_USCALED:25,VK_FORMAT_R8G8B8_SSCALED:26,VK_FORMAT_R8G8B8_UINT:27,VK_FORMAT_R8G8B8_SINT:28,VK_FORMAT_R8G8B8_SRGB:29,VK_FORMAT_B8G8R8_UNORM:30,VK_FORMAT_B8G8R8_SNORM:31,VK_FORMAT_B8G8R8_USCALED:32,VK_FORMAT_B8G8R8_SSCALED:33,VK_FORMAT_B8G8R8_UINT:34,VK_FORMAT_B8G8R8_SINT:35,VK_FORMAT_B8G8R8_SRGB:36,VK_FORMAT_R8G8B8A8_UNORM:37,VK_FORMAT_R8G8B8A8_SNORM:38,VK_FORMAT_R8G8B8A8_USCALED:39,VK_FORMAT_R8G8B8A8_SSCALED:40,VK_FORMAT_R8G8B8A8_UINT:41,VK_FORMAT_R8G8B8A8_SINT:42,VK_FORMAT_R8G8B8A8_SRGB:43,VK_FORMAT_B8G8R8A8_UNORM:44,VK_FORMAT_B8G8R8A8_SNORM:45,VK_FORMAT_B8G8R8A8_USCALED:46,VK_FORMAT_B8G8R8A8_SSCALED:47,VK_FORMAT_B8G8R8A8_UINT:48,VK_FORMAT_B8G8R8A8_SINT:49,VK_FORMAT_B8G8R8A8_SRGB:50,VK_FORMAT_A8B8G8R8_UNORM_PACK32:51,VK_FORMAT_A8B8G8R8_SNORM_PACK32:52,VK_FORMAT_A8B8G8R8_USCALED_PACK32:53,VK_FORMAT_A8B8G8R8_SSCALED_PACK32:54,VK_FORMAT_A8B8G8R8_UINT_PACK32:55,VK_FORMAT_A8B8G8R8_SINT_PACK32:56,VK_FORMAT_A8B8G8R8_SRGB_PACK32:57,VK_FORMAT_A2R10G10B10_UNORM_PACK32:58,VK_FORMAT_A2R10G10B10_SNORM_PACK32:59,VK_FORMAT_A2R10G10B10_USCALED_PACK32:60,VK_FORMAT_A2R10G10B10_SSCALED_PACK32:61,VK_FORMAT_A2R10G10B10_UINT_PACK32:62,VK_FORMAT_A2R10G10B10_SINT_PACK32:63,VK_FORMAT_A2B10G10R10_UNORM_PACK32:64,VK_FORMAT_A2B10G10R10_SNORM_PACK32:65,VK_FORMAT_A2B10G10R10_USCALED_PACK32:66,VK_FORMAT_A2B10G10R10_SSCALED_PACK32:67,VK_FORMAT_A2B10G10R10_UINT_PACK32:68,VK_FORMAT_A2B10G10R10_SINT_PACK32:69,VK_FORMAT_R16_UNORM:70,VK_FORMAT_R16_SNORM:71,VK_FORMAT_R16_USCALED:72,VK_FORMAT_R16_SSCALED:73,VK_FORMAT_R16_UINT:74,VK_FORMAT_R16_SINT:75,VK_FORMAT_R16_SFLOAT:76,VK_FORMAT_R16G16_UNORM:77,VK_FORMAT_R16G16_SNORM:78,VK_FORMAT_R16G16_USCALED:79,VK_FORMAT_R16G16_SSCALED:80,VK_FORMAT_R16G16_UINT:81,VK_FORMAT_R16G16_SINT:82,VK_FORMAT_R16G16_SFLOAT:83,VK_FORMAT_R16G16B16_UNORM:84,VK_FORMAT_R16G16B16_SNORM:85,VK_FORMAT_R16G16B16_USCALED:86,VK_FORMAT_R16G16B16_SSCALED:87,VK_FORMAT_R16G16B16_UINT:88,VK_FORMAT_R16G16B16_SINT:89,VK_FORMAT_R16G16B16_SFLOAT:90,VK_FORMAT_R16G16B16A16_UNORM:91,VK_FORMAT_R16G16B16A16_SNORM:92,VK_FORMAT_R16G16B16A16_USCALED:93,VK_FORMAT_R16G16B16A16_SSCALED:94,VK_FORMAT_R16G16B16A16_UINT:95,VK_FORMAT_R16G16B16A16_SINT:96,VK_FORMAT_R16G16B16A16_SFLOAT:97,VK_FORMAT_R32_UINT:98,VK_FORMAT_R32_SINT:99,VK_FORMAT_R32_SFLOAT:100,VK_FORMAT_R32G32_UINT:101,VK_FORMAT_R32G32_SINT:102,VK_FORMAT_R32G32_SFLOAT:103,VK_FORMAT_R32G32B32_UINT:104,VK_FORMAT_R32G32B32_SINT:105,VK_FORMAT_R32G32B32_SFLOAT:106,VK_FORMAT_R32G32B32A32_UINT:107,VK_FORMAT_R32G32B32A32_SINT:108,VK_FORMAT_R32G32B32A32_SFLOAT:109,VK_FORMAT_R64_UINT:110,VK_FORMAT_R64_SINT:111,VK_FORMAT_R64_SFLOAT:112,VK_FORMAT_R64G64_UINT:113,VK_FORMAT_R64G64_SINT:114,VK_FORMAT_R64G64_SFLOAT:115,VK_FORMAT_R64G64B64_UINT:116,VK_FORMAT_R64G64B64_SINT:117,VK_FORMAT_R64G64B64_SFLOAT:118,VK_FORMAT_R64G64B64A64_UINT:119,VK_FORMAT_R64G64B64A64_SINT:120,VK_FORMAT_R64G64B64A64_SFLOAT:121,VK_FORMAT_B10G11R11_UFLOAT_PACK32:122,VK_FORMAT_E5B9G9R9_UFLOAT_PACK32:123,VK_FORMAT_D16_UNORM:124,VK_FORMAT_X8_D24_UNORM_PACK32:125,VK_FORMAT_D32_SFLOAT:126,VK_FORMAT_S8_UINT:127,VK_FORMAT_D16_UNORM_S8_UINT:128,VK_FORMAT_D24_UNORM_S8_UINT:129,VK_FORMAT_D32_SFLOAT_S8_UINT:130,VK_FORMAT_BC1_RGB_UNORM_BLOCK:131,VK_FORMAT_BC1_RGB_SRGB_BLOCK:132,VK_FORMAT_BC1_RGBA_UNORM_BLOCK:133,VK_FORMAT_BC1_RGBA_SRGB_BLOCK:134,VK_FORMAT_BC2_UNORM_BLOCK:135,VK_FORMAT_BC2_SRGB_BLOCK:136,VK_FORMAT_BC3_UNORM_BLOCK:137,VK_FORMAT_BC3_SRGB_BLOCK:138,VK_FORMAT_BC4_UNORM_BLOCK:139,VK_FORMAT_BC4_SNORM_BLOCK:140,VK_FORMAT_BC5_UNORM_BLOCK:141,VK_FORMAT_BC5_SNORM_BLOCK:142,VK_FORMAT_BC6H_UFLOAT_BLOCK:143,VK_FORMAT_BC6H_SFLOAT_BLOCK:144,VK_FORMAT_BC7_UNORM_BLOCK:145,VK_FORMAT_BC7_SRGB_BLOCK:146,VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK:147,VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK:148,VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK:149,VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK:150,VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK:151,VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK:152,VK_FORMAT_EAC_R11_UNORM_BLOCK:153,VK_FORMAT_EAC_R11_SNORM_BLOCK:154,VK_FORMAT_EAC_R11G11_UNORM_BLOCK:155,VK_FORMAT_EAC_R11G11_SNORM_BLOCK:156,VK_FORMAT_ASTC_4x4_UNORM_BLOCK:157,VK_FORMAT_ASTC_4x4_SRGB_BLOCK:158,VK_FORMAT_ASTC_5x4_UNORM_BLOCK:159,VK_FORMAT_ASTC_5x4_SRGB_BLOCK:160,VK_FORMAT_ASTC_5x5_UNORM_BLOCK:161,VK_FORMAT_ASTC_5x5_SRGB_BLOCK:162,VK_FORMAT_ASTC_6x5_UNORM_BLOCK:163,VK_FORMAT_ASTC_6x5_SRGB_BLOCK:164,VK_FORMAT_ASTC_6x6_UNORM_BLOCK:165,VK_FORMAT_ASTC_6x6_SRGB_BLOCK:166,VK_FORMAT_ASTC_8x5_UNORM_BLOCK:167,VK_FORMAT_ASTC_8x5_SRGB_BLOCK:168,VK_FORMAT_ASTC_8x6_UNORM_BLOCK:169,VK_FORMAT_ASTC_8x6_SRGB_BLOCK:170,VK_FORMAT_ASTC_8x8_UNORM_BLOCK:171,VK_FORMAT_ASTC_8x8_SRGB_BLOCK:172,VK_FORMAT_ASTC_10x5_UNORM_BLOCK:173,VK_FORMAT_ASTC_10x5_SRGB_BLOCK:174,VK_FORMAT_ASTC_10x6_UNORM_BLOCK:175,VK_FORMAT_ASTC_10x6_SRGB_BLOCK:176,VK_FORMAT_ASTC_10x8_UNORM_BLOCK:177,VK_FORMAT_ASTC_10x8_SRGB_BLOCK:178,VK_FORMAT_ASTC_10x10_UNORM_BLOCK:179,VK_FORMAT_ASTC_10x10_SRGB_BLOCK:180,VK_FORMAT_ASTC_12x10_UNORM_BLOCK:181,VK_FORMAT_ASTC_12x10_SRGB_BLOCK:182,VK_FORMAT_ASTC_12x12_UNORM_BLOCK:183,VK_FORMAT_ASTC_12x12_SRGB_BLOCK:184,VK_FORMAT_G8B8G8R8_422_UNORM:1000156e3,VK_FORMAT_B8G8R8G8_422_UNORM:1000156001,VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM:1000156002,VK_FORMAT_G8_B8R8_2PLANE_420_UNORM:1000156003,VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM:1000156004,VK_FORMAT_G8_B8R8_2PLANE_422_UNORM:1000156005,VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM:1000156006,VK_FORMAT_R10X6_UNORM_PACK16:1000156007,VK_FORMAT_R10X6G10X6_UNORM_2PACK16:1000156008,VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16:1000156009,VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16:1000156010,VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16:1000156011,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16:1000156012,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16:1000156013,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16:1000156014,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16:1000156015,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16:1000156016,VK_FORMAT_R12X4_UNORM_PACK16:1000156017,VK_FORMAT_R12X4G12X4_UNORM_2PACK16:1000156018,VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16:1000156019,VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16:1000156020,VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16:1000156021,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16:1000156022,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16:1000156023,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16:1000156024,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16:1000156025,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16:1000156026,VK_FORMAT_G16B16G16R16_422_UNORM:1000156027,VK_FORMAT_B16G16R16G16_422_UNORM:1000156028,VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM:1000156029,VK_FORMAT_G16_B16R16_2PLANE_420_UNORM:1000156030,VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM:1000156031,VK_FORMAT_G16_B16R16_2PLANE_422_UNORM:1000156032,VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM:1000156033,VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG:1000054e3,VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG:1000054001,VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG:1000054002,VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG:1000054003,VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG:1000054004,VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG:1000054005,VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG:1000054006,VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG:1000054007,VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT:1000066e3,VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT:1000066001,VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT:1000066002,VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT:1000066003,VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT:1000066004,VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT:1000066005,VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT:1000066006,VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT:1000066007,VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT:1000066008,VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT:1000066009,VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT:1000066010,VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT:1000066011,VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT:1000066012,VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT:1000066013,VK_FORMAT_G8B8G8R8_422_UNORM_KHR:1000156e3,VK_FORMAT_B8G8R8G8_422_UNORM_KHR:1000156001,VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM_KHR:1000156002,VK_FORMAT_G8_B8R8_2PLANE_420_UNORM_KHR:1000156003,VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM_KHR:1000156004,VK_FORMAT_G8_B8R8_2PLANE_422_UNORM_KHR:1000156005,VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM_KHR:1000156006,VK_FORMAT_R10X6_UNORM_PACK16_KHR:1000156007,VK_FORMAT_R10X6G10X6_UNORM_2PACK16_KHR:1000156008,VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16_KHR:1000156009,VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16_KHR:1000156010,VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16_KHR:1000156011,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16_KHR:1000156012,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16_KHR:1000156013,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16_KHR:1000156014,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16_KHR:1000156015,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16_KHR:1000156016,VK_FORMAT_R12X4_UNORM_PACK16_KHR:1000156017,VK_FORMAT_R12X4G12X4_UNORM_2PACK16_KHR:1000156018,VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16_KHR:1000156019,VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16_KHR:1000156020,VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16_KHR:1000156021,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16_KHR:1000156022,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16_KHR:1000156023,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16_KHR:1000156024,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16_KHR:1000156025,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16_KHR:1000156026,VK_FORMAT_G16B16G16R16_422_UNORM_KHR:1000156027,VK_FORMAT_B16G16R16G16_422_UNORM_KHR:1000156028,VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM_KHR:1000156029,VK_FORMAT_G16_B16R16_2PLANE_420_UNORM_KHR:1000156030,VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM_KHR:1000156031,VK_FORMAT_G16_B16R16_2PLANE_422_UNORM_KHR:1000156032,VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM_KHR:1000156033},me=Object.freeze(ir);function Rr(){return{vkFormat:0,typeSize:1,pixelWidth:0,pixelHeight:0,pixelDepth:0,layerCount:0,faceCount:1,supercompressionScheme:0,levels:[],dataFormatDescriptor:[{vendorId:0,descriptorType:0,versionNumber:2,colorModel:0,colorPrimaries:1,transferFunction:2,flags:0,texelBlockDimension:[0,0,0,0],bytesPlane:[0,0,0,0,0,0,0,0],samples:[]}],keyValue:{},globalData:null}}var ue=class{constructor(f,r,C,l){this._dataView=void 0,this._littleEndian=void 0,this._offset=void 0,this._dataView=new DataView(f.buffer,f.byteOffset+r,C),this._littleEndian=l,this._offset=0}_nextUint8(){let f=this._dataView.getUint8(this._offset);return this._offset+=1,f}_nextUint16(){let f=this._dataView.getUint16(this._offset,this._littleEndian);return this._offset+=2,f}_nextUint32(){let f=this._dataView.getUint32(this._offset,this._littleEndian);return this._offset+=4,f}_nextUint64(){let f=this._dataView.getUint32(this._offset,this._littleEndian),r=this._dataView.getUint32(this._offset+4,this._littleEndian),C=f+2**32*r;return this._offset+=8,C}_nextInt32(){let f=this._dataView.getInt32(this._offset,this._littleEndian);return this._offset+=4,f}_nextUint8Array(f){let r=new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+this._offset,f);return this._offset+=f,r}_skip(f){return this._offset+=f,this}_scan(f,r=0){let C=this._offset,l=0;for(;this._dataView.getUint8(this._offset)!==r&&l<f;)l++,this._offset++;return l<f&&this._offset++,new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+C,l)}};var dr=new Uint8Array([0]),w=[171,75,84,88,32,50,48,187,13,10,26,10];function an(o){return new TextDecoder().decode(o)}function cn(o){let f=new Uint8Array(o.buffer,o.byteOffset,w.length);if(f[0]!==w[0]||f[1]!==w[1]||f[2]!==w[2]||f[3]!==w[3]||f[4]!==w[4]||f[5]!==w[5]||f[6]!==w[6]||f[7]!==w[7]||f[8]!==w[8]||f[9]!==w[9]||f[10]!==w[10]||f[11]!==w[11])throw new Error("Missing KTX 2.0 identifier.");let r=Rr(),C=17*Uint32Array.BYTES_PER_ELEMENT,l=new ue(o,w.length,C,!0);r.vkFormat=l._nextUint32(),r.typeSize=l._nextUint32(),r.pixelWidth=l._nextUint32(),r.pixelHeight=l._nextUint32(),r.pixelDepth=l._nextUint32(),r.layerCount=l._nextUint32(),r.faceCount=l._nextUint32();let E=l._nextUint32();r.supercompressionScheme=l._nextUint32();let F=l._nextUint32(),g=l._nextUint32(),m=l._nextUint32(),I=l._nextUint32(),N=l._nextUint64(),d=l._nextUint64(),P=E*3*8,Q=new ue(o,w.length+C,P,!0);for(let U=0;U<E;U++)r.levels.push({levelData:new Uint8Array(o.buffer,o.byteOffset+Q._nextUint64(),Q._nextUint64()),uncompressedByteLength:Q._nextUint64()});let u=new ue(o,F,g,!0);u._skip(4);let W=u._nextUint16(),J=u._nextUint16(),ae=u._nextUint16(),j=u._nextUint16(),Be=u._nextUint8(),Ce=u._nextUint8(),Fe=u._nextUint8(),it=u._nextUint8(),oe=[u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8()],Rt=[u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8()],se={vendorId:W,descriptorType:J,versionNumber:ae,colorModel:Be,colorPrimaries:Ce,transferFunction:Fe,flags:it,texelBlockDimension:oe,bytesPlane:Rt,samples:[]},Ie=(j/4-6)/4;for(let U=0;U<Ie;U++){let p={bitOffset:u._nextUint16(),bitLength:u._nextUint8(),channelType:u._nextUint8(),samplePosition:[u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8()],sampleLower:Number.NEGATIVE_INFINITY,sampleUpper:Number.POSITIVE_INFINITY};p.channelType&64?(p.sampleLower=u._nextInt32(),p.sampleUpper=u._nextInt32()):(p.sampleLower=u._nextUint32(),p.sampleUpper=u._nextUint32()),se.samples[U]=p}r.dataFormatDescriptor.length=0,r.dataFormatDescriptor.push(se);let ce=new ue(o,m,I,!0);for(;ce._offset<I;){let U=ce._nextUint32(),p=ce._scan(U),k=an(p);if(r.keyValue[k]=ce._nextUint8Array(U-p.byteLength-1),k.match(/^ktx/i)){let L=an(r.keyValue[k]);r.keyValue[k]=L.substring(0,L.lastIndexOf("\0"))}let Ae=U%4?4-U%4:0;ce._skip(Ae)}if(d<=0)return r;let D=new ue(o,N,d,!0),be=D._nextUint16(),we=D._nextUint16(),Se=D._nextUint32(),He=D._nextUint32(),Xe=D._nextUint32(),at=D._nextUint32(),Ge=[];for(let U=0;U<E;U++)Ge.push({imageFlags:D._nextUint32(),rgbSliceByteOffset:D._nextUint32(),rgbSliceByteLength:D._nextUint32(),alphaSliceByteOffset:D._nextUint32(),alphaSliceByteLength:D._nextUint32()});let xe=N+D._offset,ke=xe+Se,$e=ke+He,ct=$e+Xe,At=new Uint8Array(o.buffer,o.byteOffset+xe,Se),ft=new Uint8Array(o.buffer,o.byteOffset+ke,He),Tt=new Uint8Array(o.buffer,o.byteOffset+$e,Xe),Ne=new Uint8Array(o.buffer,o.byteOffset+ct,at);return r.globalData={endpointCount:be,selectorCount:we,imageDescs:Ge,endpointsData:At,selectorsData:ft,tablesData:Tt,extendedData:Ne},r}var fn=sr(An(),1),Tn=["positiveX","negativeX","positiveY","negativeY","positiveZ","negativeZ"],un=163,On=166,De;function ar(o,f){on.typeOf.object("transcoderModule",De);let r=o.ktx2Buffer,C=o.supportedTargetFormats,l;try{l=cn(r)}catch{throw new re("Invalid KTX2 file.")}if(l.layerCount!==0)throw new re("KTX2 texture arrays are not supported.");if(l.pixelDepth!==0)throw new re("KTX2 3D textures are unsupported.");let E=l.dataFormatDescriptor[0],F=new Array(l.levelCount);return l.vkFormat===0&&(E.colorModel===un||E.colorModel===On)?Ar(r,l,C,De,f,F):(f.push(r.buffer),cr(l,F)),F}function cr(o,f){let r=o.vkFormat===me.VK_FORMAT_R8G8B8_SRGB?v.RGB:v.RGBA,C;o.vkFormat===me.VK_FORMAT_R8G8B8A8_UNORM?C=H.UNSIGNED_BYTE:o.vkFormat===me.VK_FORMAT_R16G16B16A16_SFLOAT?C=H.HALF_FLOAT:o.vkFormat===me.VK_FORMAT_R32G32B32A32_SFLOAT&&(C=H.FLOAT);for(let l=0;l<o.levels.length;++l){let E={};f[l]=E;let F=o.levels[l].levelData,g=o.pixelWidth>>l,m=o.pixelHeight>>l,I=g*m*v.componentsLength(r);for(let N=0;N<o.faceCount;++N){let d=F.byteOffset+I*o.typeSize*N,P;!Le(C)||H.sizeInBytes(C)===1?P=new Uint8Array(F.buffer,d,I):H.sizeInBytes(C)===2?P=new Uint16Array(F.buffer,d,I):P=new Float32Array(F.buffer,d,I),E[Tn[N]]={internalFormat:r,datatype:C,width:g,height:m,levelBuffer:P}}}}function Ar(o,f,r,C,l,E){let F=new C.KTX2File(o),g=F.getWidth(),m=F.getHeight(),I=F.getLevels(),N=F.getHasAlpha();if(!(g>0)||!(m>0)||!(I>0))throw F.close(),F.delete(),new re("Invalid KTX2 file");let d,P,Q=f.dataFormatDescriptor[0],u=C.transcoder_texture_format;if(Q.colorModel===un)if(r.etc)d=N?v.RGBA8_ETC2_EAC:v.RGB8_ETC2,P=N?u.cTFETC2_RGBA:u.cTFETC1_RGB;else if(r.etc1&&!N)d=v.RGB_ETC1,P=u.cTFETC1_RGB;else if(r.s3tc)d=N?v.RGBA_DXT5:v.RGB_DXT1,P=N?u.cTFBC3_RGBA:u.cTFBC1_RGB;else if(r.pvrtc)d=N?v.RGBA_PVRTC_4BPPV1:v.RGB_PVRTC_4BPPV1,P=N?u.cTFPVRTC1_4_RGBA:u.cTFPVRTC1_4_RGB;else if(r.astc)d=v.RGBA_ASTC,P=u.cTFASTC_4x4_RGBA;else if(r.bc7)d=v.RGBA_BC7,P=u.cTFBC7_RGBA;else throw new re("No transcoding format target available for ETC1S compressed ktx2.");else if(Q.colorModel===On)if(r.astc)d=v.RGBA_ASTC,P=u.cTFASTC_4x4_RGBA;else if(r.bc7)d=v.RGBA_BC7,P=u.cTFBC7_RGBA;else if(r.s3tc)d=N?v.RGBA_DXT5:v.RGB_DXT1,P=N?u.cTFBC3_RGBA:u.cTFBC1_RGB;else if(r.etc)d=N?v.RGBA8_ETC2_EAC:v.RGB8_ETC2,P=N?u.cTFETC2_RGBA:u.cTFETC1_RGB;else if(r.etc1&&!N)d=v.RGB_ETC1,P=u.cTFETC1_RGB;else if(r.pvrtc)d=N?v.RGBA_PVRTC_4BPPV1:v.RGB_PVRTC_4BPPV1,P=N?u.cTFPVRTC1_4_RGBA:u.cTFPVRTC1_4_RGB;else throw new re("No transcoding format target available for UASTC compressed ktx2.");if(!F.startTranscoding())throw F.close(),F.delete(),new re("startTranscoding() failed");for(let W=0;W<f.levels.length;++W){let J={};E[W]=J,g=f.pixelWidth>>W,m=f.pixelHeight>>W;let ae=F.getImageTranscodedSizeInBytes(W,0,0,P.value),j=new Uint8Array(ae),Be=F.transcodeImage(j,W,0,0,P.value,0,-1,-1);if(!Le(Be))throw new re("transcodeImage() failed.");l.push(j.buffer),J[Tn[0]]={internalFormat:d,width:g,height:m,levelBuffer:j}}return F.close(),F.delete(),E}async function fr(o,f){let r=o.webAssemblyConfig,C=sn(fn.default,self.BASIS);return Le(r.wasmBinaryFile)?De=await C(r):De=await C(),De.initializeBasis(),!0}function Tr(o,f){let r=o.webAssemblyConfig;return Le(r)?fr(o,f):ar(o,f)}var yr=Rn(Tr);export{yr as default};
