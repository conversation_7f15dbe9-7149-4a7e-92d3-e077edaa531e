/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.127
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{b as _,h as I}from"./chunk-YWRPWWKI.js";import{a as n,b as N,d as Z,e as A}from"./chunk-JMSSU44E.js";import{a as cn}from"./chunk-4UYSGV57.js";import{a as T}from"./chunk-VLPNAR64.js";import{a as on,b as h}from"./chunk-GE5NEIZC.js";import{e as x}from"./chunk-35CVRQTC.js";function V(e){this._ellipsoid=T(e,Z.default),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(V.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}});V.prototype.project=function(e,t){let o=this._semimajorAxis,c=e.longitude*o,r=e.latitude*o,d=e.height;return x(t)?(t.x=c,t.y=r,t.z=d,t):new n(c,r,d)};V.prototype.unproject=function(e,t){if(!x(e))throw new on("cartesian is required");let o=this._oneOverSemimajorAxis,c=e.x*o,r=e.y*o,d=e.z;return x(t)?(t.longitude=c,t.latitude=r,t.height=d,t):new N(c,r,d)};var k=V;var mn={OUTSIDE:-1,INTERSECTING:0,INSIDE:1},U=Object.freeze(mn);function un(e,t){this.start=T(e,0),this.stop=T(t,0)}var rn=un;function a(e,t){this.center=n.clone(T(e,n.ZERO)),this.radius=T(t,0)}var F=new n,Y=new n,J=new n,K=new n,Q=new n,$=new n,L=new n,b=new n,H=new n,nn=new n,tn=new n,en=new n,xn=4/3*cn.PI;a.fromPoints=function(e,t){if(x(t)||(t=new a),!x(e)||e.length===0)return t.center=n.clone(n.ZERO,t.center),t.radius=0,t;let o=n.clone(e[0],L),c=n.clone(o,F),r=n.clone(o,Y),d=n.clone(o,J),f=n.clone(o,K),s=n.clone(o,Q),m=n.clone(o,$),z=e.length,y;for(y=1;y<z;y++){n.clone(e[y],o);let R=o.x,M=o.y,w=o.z;R<c.x&&n.clone(o,c),R>f.x&&n.clone(o,f),M<r.y&&n.clone(o,r),M>s.y&&n.clone(o,s),w<d.z&&n.clone(o,d),w>m.z&&n.clone(o,m)}let u=n.magnitudeSquared(n.subtract(f,c,b)),i=n.magnitudeSquared(n.subtract(s,r,b)),v=n.magnitudeSquared(n.subtract(m,d,b)),C=c,q=f,O=u;i>O&&(O=i,C=r,q=s),v>O&&(O=v,C=d,q=m);let p=H;p.x=(C.x+q.x)*.5,p.y=(C.y+q.y)*.5,p.z=(C.z+q.z)*.5;let S=n.magnitudeSquared(n.subtract(q,p,b)),l=Math.sqrt(S),j=nn;j.x=c.x,j.y=r.y,j.z=d.z;let P=tn;P.x=f.x,P.y=s.y,P.z=m.z;let D=n.midpoint(j,P,en),B=0;for(y=0;y<z;y++){n.clone(e[y],o);let R=n.magnitude(n.subtract(o,D,b));R>B&&(B=R);let M=n.magnitudeSquared(n.subtract(o,p,b));if(M>S){let w=Math.sqrt(M);l=(l+w)*.5,S=l*l;let g=w-l;p.x=(l*p.x+g*o.x)/w,p.y=(l*p.y+g*o.y)/w,p.z=(l*p.z+g*o.z)/w}}return l<B?(n.clone(p,t.center),t.radius=l):(n.clone(D,t.center),t.radius=B),t};var an=new k,yn=new n,ln=new n,G=new N,X=new N;a.fromRectangle2D=function(e,t,o){return a.fromRectangleWithHeights2D(e,t,0,0,o)};a.fromRectangleWithHeights2D=function(e,t,o,c,r){if(x(r)||(r=new a),!x(e))return r.center=n.clone(n.ZERO,r.center),r.radius=0,r;an._ellipsoid=Z.default,t=T(t,an),I.southwest(e,G),G.height=o,I.northeast(e,X),X.height=c;let d=t.project(G,yn),f=t.project(X,ln),s=f.x-d.x,m=f.y-d.y,z=f.z-d.z;r.radius=Math.sqrt(s*s+m*m+z*z)*.5;let y=r.center;return y.x=d.x+s*.5,y.y=d.y+m*.5,y.z=d.z+z*.5,r};var pn=[];a.fromRectangle3D=function(e,t,o,c){if(t=T(t,Z.default),o=T(o,0),x(c)||(c=new a),!x(e))return c.center=n.clone(n.ZERO,c.center),c.radius=0,c;let r=I.subsample(e,t,o,pn);return a.fromPoints(r,c)};a.fromVertices=function(e,t,o,c){if(x(c)||(c=new a),!x(e)||e.length===0)return c.center=n.clone(n.ZERO,c.center),c.radius=0,c;t=T(t,n.ZERO),o=T(o,3),h.typeOf.number.greaterThanOrEquals("stride",o,3);let r=L;r.x=e[0]+t.x,r.y=e[1]+t.y,r.z=e[2]+t.z;let d=n.clone(r,F),f=n.clone(r,Y),s=n.clone(r,J),m=n.clone(r,K),z=n.clone(r,Q),y=n.clone(r,$),u=e.length,i;for(i=0;i<u;i+=o){let w=e[i]+t.x,g=e[i+1]+t.y,E=e[i+2]+t.z;r.x=w,r.y=g,r.z=E,w<d.x&&n.clone(r,d),w>m.x&&n.clone(r,m),g<f.y&&n.clone(r,f),g>z.y&&n.clone(r,z),E<s.z&&n.clone(r,s),E>y.z&&n.clone(r,y)}let v=n.magnitudeSquared(n.subtract(m,d,b)),C=n.magnitudeSquared(n.subtract(z,f,b)),q=n.magnitudeSquared(n.subtract(y,s,b)),O=d,p=m,S=v;C>S&&(S=C,O=f,p=z),q>S&&(S=q,O=s,p=y);let l=H;l.x=(O.x+p.x)*.5,l.y=(O.y+p.y)*.5,l.z=(O.z+p.z)*.5;let j=n.magnitudeSquared(n.subtract(p,l,b)),P=Math.sqrt(j),D=nn;D.x=d.x,D.y=f.y,D.z=s.z;let B=tn;B.x=m.x,B.y=z.y,B.z=y.z;let R=n.midpoint(D,B,en),M=0;for(i=0;i<u;i+=o){r.x=e[i]+t.x,r.y=e[i+1]+t.y,r.z=e[i+2]+t.z;let w=n.magnitude(n.subtract(r,R,b));w>M&&(M=w);let g=n.magnitudeSquared(n.subtract(r,l,b));if(g>j){let E=Math.sqrt(g);P=(P+E)*.5,j=P*P;let W=E-P;l.x=(P*l.x+W*r.x)/E,l.y=(P*l.y+W*r.y)/E,l.z=(P*l.z+W*r.z)/E}}return P<M?(n.clone(l,c.center),c.radius=P):(n.clone(R,c.center),c.radius=M),c};a.fromEncodedCartesianVertices=function(e,t,o){if(x(o)||(o=new a),!x(e)||!x(t)||e.length!==t.length||e.length===0)return o.center=n.clone(n.ZERO,o.center),o.radius=0,o;let c=L;c.x=e[0]+t[0],c.y=e[1]+t[1],c.z=e[2]+t[2];let r=n.clone(c,F),d=n.clone(c,Y),f=n.clone(c,J),s=n.clone(c,K),m=n.clone(c,Q),z=n.clone(c,$),y=e.length,u;for(u=0;u<y;u+=3){let M=e[u]+t[u],w=e[u+1]+t[u+1],g=e[u+2]+t[u+2];c.x=M,c.y=w,c.z=g,M<r.x&&n.clone(c,r),M>s.x&&n.clone(c,s),w<d.y&&n.clone(c,d),w>m.y&&n.clone(c,m),g<f.z&&n.clone(c,f),g>z.z&&n.clone(c,z)}let i=n.magnitudeSquared(n.subtract(s,r,b)),v=n.magnitudeSquared(n.subtract(m,d,b)),C=n.magnitudeSquared(n.subtract(z,f,b)),q=r,O=s,p=i;v>p&&(p=v,q=d,O=m),C>p&&(p=C,q=f,O=z);let S=H;S.x=(q.x+O.x)*.5,S.y=(q.y+O.y)*.5,S.z=(q.z+O.z)*.5;let l=n.magnitudeSquared(n.subtract(O,S,b)),j=Math.sqrt(l),P=nn;P.x=r.x,P.y=d.y,P.z=f.z;let D=tn;D.x=s.x,D.y=m.y,D.z=z.z;let B=n.midpoint(P,D,en),R=0;for(u=0;u<y;u+=3){c.x=e[u]+t[u],c.y=e[u+1]+t[u+1],c.z=e[u+2]+t[u+2];let M=n.magnitude(n.subtract(c,B,b));M>R&&(R=M);let w=n.magnitudeSquared(n.subtract(c,S,b));if(w>l){let g=Math.sqrt(w);j=(j+g)*.5,l=j*j;let E=g-j;S.x=(j*S.x+E*c.x)/g,S.y=(j*S.y+E*c.y)/g,S.z=(j*S.z+E*c.z)/g}}return j<R?(n.clone(S,o.center),o.radius=j):(n.clone(B,o.center),o.radius=R),o};a.fromCornerPoints=function(e,t,o){h.typeOf.object("corner",e),h.typeOf.object("oppositeCorner",t),x(o)||(o=new a);let c=n.midpoint(e,t,o.center);return o.radius=n.distance(c,t),o};a.fromEllipsoid=function(e,t){return h.typeOf.object("ellipsoid",e),x(t)||(t=new a),n.clone(n.ZERO,t.center),t.radius=e.maximumRadius,t};var hn=new n;a.fromBoundingSpheres=function(e,t){if(x(t)||(t=new a),!x(e)||e.length===0)return t.center=n.clone(n.ZERO,t.center),t.radius=0,t;let o=e.length;if(o===1)return a.clone(e[0],t);if(o===2)return a.union(e[0],e[1],t);let c=[],r;for(r=0;r<o;r++)c.push(e[r].center);t=a.fromPoints(c,t);let d=t.center,f=t.radius;for(r=0;r<o;r++){let s=e[r];f=Math.max(f,n.distance(d,s.center,hn)+s.radius)}return t.radius=f,t};var zn=new n,Sn=new n,wn=new n;a.fromOrientedBoundingBox=function(e,t){h.defined("orientedBoundingBox",e),x(t)||(t=new a);let o=e.halfAxes,c=A.getColumn(o,0,zn),r=A.getColumn(o,1,Sn),d=A.getColumn(o,2,wn);return n.add(c,r,c),n.add(c,d,c),t.center=n.clone(e.center,t.center),t.radius=n.magnitude(c),t};var Pn=new n,gn=new n;a.fromTransformation=function(e,t){h.typeOf.object("transformation",e),x(t)||(t=new a);let o=_.getTranslation(e,Pn),c=_.getScale(e,gn),r=.5*n.magnitude(c);return t.center=n.clone(o,t.center),t.radius=r,t};a.clone=function(e,t){if(x(e))return x(t)?(t.center=n.clone(e.center,t.center),t.radius=e.radius,t):new a(e.center,e.radius)};a.packedLength=4;a.pack=function(e,t,o){h.typeOf.object("value",e),h.defined("array",t),o=T(o,0);let c=e.center;return t[o++]=c.x,t[o++]=c.y,t[o++]=c.z,t[o]=e.radius,t};a.unpack=function(e,t,o){h.defined("array",e),t=T(t,0),x(o)||(o=new a);let c=o.center;return c.x=e[t++],c.y=e[t++],c.z=e[t++],o.radius=e[t],o};var On=new n,jn=new n;a.union=function(e,t,o){h.typeOf.object("left",e),h.typeOf.object("right",t),x(o)||(o=new a);let c=e.center,r=e.radius,d=t.center,f=t.radius,s=n.subtract(d,c,On),m=n.magnitude(s);if(r>=m+f)return e.clone(o),o;if(f>=m+r)return t.clone(o),o;let z=(r+m+f)*.5,y=n.multiplyByScalar(s,(-r+z)/m,jn);return n.add(y,c,y),n.clone(y,o.center),o.radius=z,o};var bn=new n;a.expand=function(e,t,o){h.typeOf.object("sphere",e),h.typeOf.object("point",t),o=a.clone(e,o);let c=n.magnitude(n.subtract(t,o.center,bn));return c>o.radius&&(o.radius=c),o};a.intersectPlane=function(e,t){h.typeOf.object("sphere",e),h.typeOf.object("plane",t);let o=e.center,c=e.radius,r=t.normal,d=n.dot(r,o)+t.distance;return d<-c?U.OUTSIDE:d<c?U.INTERSECTING:U.INSIDE};a.transform=function(e,t,o){return h.typeOf.object("sphere",e),h.typeOf.object("transform",t),x(o)||(o=new a),o.center=_.multiplyByPoint(t,e.center,o.center),o.radius=_.getMaximumScale(t)*e.radius,o};var qn=new n;a.distanceSquaredTo=function(e,t){h.typeOf.object("sphere",e),h.typeOf.object("cartesian",t);let o=n.subtract(e.center,t,qn),c=n.magnitude(o)-e.radius;return c<=0?0:c*c};a.transformWithoutScale=function(e,t,o){return h.typeOf.object("sphere",e),h.typeOf.object("transform",t),x(o)||(o=new a),o.center=_.multiplyByPoint(t,e.center,o.center),o.radius=e.radius,o};var Mn=new n;a.computePlaneDistances=function(e,t,o,c){h.typeOf.object("sphere",e),h.typeOf.object("position",t),h.typeOf.object("direction",o),x(c)||(c=new rn);let r=n.subtract(e.center,t,Mn),d=n.dot(o,r);return c.start=d-e.radius,c.stop=d+e.radius,c};var dn=new n,Tn=new n,Cn=new n,Rn=new n,En=new n,Dn=new N,sn=new Array(8);for(let e=0;e<8;++e)sn[e]=new n;var fn=new k;a.projectTo2D=function(e,t,o){h.typeOf.object("sphere",e),fn._ellipsoid=Z.default,t=T(t,fn);let c=t.ellipsoid,r=e.center,d=e.radius,f;n.equals(r,n.ZERO)?f=n.clone(n.UNIT_X,dn):f=c.geodeticSurfaceNormal(r,dn);let s=n.cross(n.UNIT_Z,f,Tn);n.normalize(s,s);let m=n.cross(f,s,Cn);n.normalize(m,m),n.multiplyByScalar(f,d,f),n.multiplyByScalar(m,d,m),n.multiplyByScalar(s,d,s);let z=n.negate(m,En),y=n.negate(s,Rn),u=sn,i=u[0];n.add(f,m,i),n.add(i,s,i),i=u[1],n.add(f,m,i),n.add(i,y,i),i=u[2],n.add(f,z,i),n.add(i,y,i),i=u[3],n.add(f,z,i),n.add(i,s,i),n.negate(f,f),i=u[4],n.add(f,m,i),n.add(i,s,i),i=u[5],n.add(f,m,i),n.add(i,y,i),i=u[6],n.add(f,z,i),n.add(i,y,i),i=u[7],n.add(f,z,i),n.add(i,s,i);let v=u.length;for(let p=0;p<v;++p){let S=u[p];n.add(r,S,S);let l=c.cartesianToCartographic(S,Dn);t.project(l,S)}o=a.fromPoints(u,o),r=o.center;let C=r.x,q=r.y,O=r.z;return r.x=O,r.y=C,r.z=q,o};a.isOccluded=function(e,t){return h.typeOf.object("sphere",e),h.typeOf.object("occluder",t),!t.isBoundingSphereVisible(e)};a.equals=function(e,t){return e===t||x(e)&&x(t)&&n.equals(e.center,t.center)&&e.radius===t.radius};a.prototype.intersectPlane=function(e){return a.intersectPlane(this,e)};a.prototype.distanceSquaredTo=function(e){return a.distanceSquaredTo(this,e)};a.prototype.computePlaneDistances=function(e,t,o){return a.computePlaneDistances(this,e,t,o)};a.prototype.isOccluded=function(e){return a.isOccluded(this,e)};a.prototype.equals=function(e){return a.equals(this,e)};a.prototype.clone=function(e){return a.clone(this,e)};a.prototype.volume=function(){let e=this.radius;return xn*e*e*e};var et=a;export{k as a,U as b,rn as c,et as d};
