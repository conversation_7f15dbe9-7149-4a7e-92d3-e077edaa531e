//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * Compute the atmosphere color, applying Rayleigh and Mie scattering. This\n\
 * builtin uses automatic uniforms so the atmophere settings are synced with the\n\
 * state of the Scene, even in other contexts like Model.\n\
 *\n\
 * @name czm_computeAtmosphereColor\n\
 * @glslFunction\n\
 *\n\
 * @param {vec3} positionWC Position of the fragment in world coords (low precision)\n\
 * @param {vec3} lightDirection Light direction from the sun or other light source.\n\
 * @param {vec3} rayleighColor The Rayleigh scattering color computed by a scattering function\n\
 * @param {vec3} mieColor The Mie scattering color computed by a scattering function\n\
 * @param {float} opacity The opacity computed by a scattering function.\n\
 */\n\
vec4 czm_computeAtmosphereColor(\n\
    vec3 positionWC,\n\
    vec3 lightDirection,\n\
    vec3 rayleighColor,\n\
    vec3 mieColor,\n\
    float opacity\n\
) {\n\
    // Setup the primary ray: from the camera position to the vertex position.\n\
    vec3 cameraToPositionWC = positionWC - czm_viewerPositionWC;\n\
    vec3 cameraToPositionWCDirection = normalize(cameraToPositionWC);\n\
\n\
    float cosAngle = dot(cameraToPositionWCDirection, lightDirection);\n\
    float cosAngleSq = cosAngle * cosAngle;\n\
\n\
    float G = czm_atmosphereMieAnisotropy;\n\
    float GSq = G * G;\n\
\n\
    // The Rayleigh phase function.\n\
    float rayleighPhase = 3.0 / (50.2654824574) * (1.0 + cosAngleSq);\n\
    // The Mie phase function.\n\
    float miePhase = 3.0 / (25.1327412287) * ((1.0 - GSq) * (cosAngleSq + 1.0)) / (pow(1.0 + GSq - 2.0 * cosAngle * G, 1.5) * (2.0 + GSq));\n\
\n\
    // The final color is generated by combining the effects of the Rayleigh and Mie scattering.\n\
    vec3 rayleigh = rayleighPhase * rayleighColor;\n\
    vec3 mie = miePhase * mieColor;\n\
\n\
    vec3 color = (rayleigh + mie) * czm_atmosphereLightIntensity;\n\
\n\
    return vec4(color, opacity);\n\
}\n\
\n\
/**\n\
 * Compute the atmosphere color, applying Rayleigh and Mie scattering. This\n\
 * builtin uses automatic uniforms so the atmophere settings are synced with the\n\
 * state of the Scene, even in other contexts like Model.\n\
 *\n\
 * @name czm_computeAtmosphereColor\n\
 * @glslFunction\n\
 *\n\
 * @param {czm_ray} primaryRay Ray from the origin to sky fragment to in world coords (low precision)\n\
 * @param {vec3} lightDirection Light direction from the sun or other light source.\n\
 * @param {vec3} rayleighColor The Rayleigh scattering color computed by a scattering function\n\
 * @param {vec3} mieColor The Mie scattering color computed by a scattering function\n\
 * @param {float} opacity The opacity computed by a scattering function.\n\
 */\n\
vec4 czm_computeAtmosphereColor(\n\
    czm_ray primaryRay,\n\
    vec3 lightDirection,\n\
    vec3 rayleighColor,\n\
    vec3 mieColor,\n\
    float opacity\n\
) {\n\
    vec3 direction = normalize(primaryRay.direction);\n\
\n\
    float cosAngle = dot(direction, lightDirection);\n\
    float cosAngleSq = cosAngle * cosAngle;\n\
\n\
    float G = czm_atmosphereMieAnisotropy;\n\
    float GSq = G * G;\n\
\n\
    // The Rayleigh phase function.\n\
    float rayleighPhase = 3.0 / (50.2654824574) * (1.0 + cosAngleSq);\n\
    // The Mie phase function.\n\
    float miePhase = 3.0 / (25.1327412287) * ((1.0 - GSq) * (cosAngleSq + 1.0)) / (pow(1.0 + GSq - 2.0 * cosAngle * G, 1.5) * (2.0 + GSq));\n\
\n\
    // The final color is generated by combining the effects of the Rayleigh and Mie scattering.\n\
    vec3 rayleigh = rayleighPhase * rayleighColor;\n\
    vec3 mie = miePhase * mieColor;\n\
\n\
    vec3 color = (rayleigh + mie) * czm_atmosphereLightIntensity;\n\
\n\
    return vec4(color, opacity);\n\
}\n\
\n\
";
