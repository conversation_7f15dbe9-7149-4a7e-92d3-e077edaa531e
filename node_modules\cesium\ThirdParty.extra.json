[{"name": "autolinker"}, {"name": "basis_universal", "license": ["Apache-2.0"], "version": "1.15", "url": "https://github.com/BinomialLLC/basis_universal"}, {"name": "bitmap-sdf"}, {"name": "dompurify", "license": ["Apache-2.0"], "notes": "dompurify is available as both MPL-2.0 OR Apache-2.0"}, {"name": "draco3d"}, {"name": "earcut"}, {"name": "grapheme-splitter"}, {"name": "jsep"}, {"name": "kdbush"}, {"name": "Knockout", "license": ["MIT"], "version": "3.5.1", "url": "https://knockoutjs.com/"}, {"name": "Knockout ES5 plugin", "license": ["MIT"], "version": "0.4.6", "url": "https://github.com/Steve<PERSON>son/knockout-es5"}, {"name": "ktx-parse"}, {"name": "lerc"}, {"name": "mersenne-twister"}, {"name": "meshoptimizer"}, {"name": "nosleep.js"}, {"name": "pako", "license": ["MIT"], "notes": "pako is MIT, and its dependency zlib is attributed separately"}, {"name": "protobufjs"}, {"name": "rbush"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-client"}, {"name": "@tweenjs/tween.js"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "@zip.js/zip.js"}]